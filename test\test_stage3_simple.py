#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的第三阶段测试
测试一个产品的详情采集
"""

import os
import sqlite3
from stage3_crawler import Stage3Crawler

def test_single_product():
    """测试单个产品的详情采集"""
    print("🧪 第三阶段单个产品测试")
    print("=" * 50)
    
    # 检查数据库
    if not os.path.exists("novoline_spider_v2.db"):
        print("❌ 新数据库不存在")
        return
    
    # 创建爬虫
    crawler = Stage3Crawler("novoline_spider_v2.db")
    
    # 设置回调
    def on_progress(stage, message):
        print(f"[{stage}] {message}")
    
    def on_error(message):
        print(f"❌ 错误: {message}")
    
    def on_completion(message):
        print(f"✅ 完成: {message}")
    
    crawler.set_callbacks(on_progress, on_error, on_completion)
    
    # 获取一个待采集的产品
    with sqlite3.connect("novoline_spider_v2.db") as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT pu.id, pu.product_name, pu.product_url
            FROM product_urls pu
            LEFT JOIN product_details pd ON pu.id = pd.product_url_id
            WHERE pd.id IS NULL
            LIMIT 1
        ''')
        result = cursor.fetchone()
        
        if not result:
            print("❌ 没有待采集的产品")
            return
        
        product_url_id, product_name, product_url = result
        print(f"测试产品: {product_name}")
        print(f"URL: {product_url}")
        
        # 采集单个产品
        print(f"\n🚀 开始采集...")
        success = crawler._crawl_single_product(product_url_id, product_url, product_name)
        
        if success:
            print(f"\n📊 采集结果:")
            # 查看采集结果
            cursor.execute('''
                SELECT pd.title, pd.sku, pd.nav_full, pd.category_name, 
                       pd.regular_price, COUNT(tp.id) as tier_count
                FROM product_details pd
                LEFT JOIN tier_prices tp ON pd.id = tp.product_detail_id
                WHERE pd.product_url_id = ?
                GROUP BY pd.id
            ''', (product_url_id,))
            
            detail = cursor.fetchone()
            if detail:
                title, sku, nav_full, category_name, regular_price, tier_count = detail
                print(f"   标题: {title}")
                print(f"   SKU: {sku}")
                print(f"   导航: {nav_full}")
                print(f"   分类: {category_name}")
                print(f"   基础价格: ${regular_price}")
                print(f"   阶梯价格层级: {tier_count}")
                
                # 显示阶梯价格
                if tier_count > 0:
                    cursor.execute('''
                        SELECT quantity_display, unit_price, discount_percent
                        FROM tier_prices
                        WHERE product_detail_id = (
                            SELECT id FROM product_details WHERE product_url_id = ?
                        )
                        ORDER BY quantity_min
                    ''', (product_url_id,))
                    
                    tier_prices = cursor.fetchall()
                    print(f"\n   📊 阶梯价格:")
                    for i, (qty_display, unit_price, discount) in enumerate(tier_prices, 1):
                        print(f"      {i}. {qty_display} - ${unit_price} ({discount}% off)")
        else:
            print(f"❌ 采集失败")

def main():
    """主函数"""
    import os
    
    print("🎯 第三阶段简单测试")
    print("\n功能验证:")
    print("1. 数据库结构检查")
    print("2. 单个产品详情采集")
    print("3. 阶梯价格解析")
    print("4. 导航信息提取")
    
    test_single_product()
    
    print(f"\n💡 完整测试:")
    print("   启动GUI: python gui.py")
    print("   1. 查看'数据查看'Tab - 应该显示第三阶段状态")
    print("   2. 切换到'第三步：阶梯价格'Tab")
    print("   3. 选择采集范围，点击'开始第三阶段'")
    print("   4. 右键点击产品进行单独操作")

if __name__ == "__main__":
    main()
