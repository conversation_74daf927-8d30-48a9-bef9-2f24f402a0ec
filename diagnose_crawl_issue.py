#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断采集问题 - 为什么product_urls数量没有增加
"""

import sqlite3
import time

def check_database_locks():
    """检查数据库锁定状态"""
    print("🔒 检查数据库锁定状态...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db', timeout=5) as conn:
            cursor = conn.cursor()
            
            # 检查是否有未提交的事务
            cursor.execute('BEGIN IMMEDIATE')
            cursor.execute('ROLLBACK')
            print("   ✅ 数据库可以正常访问，没有锁定")
            
            # 检查WAL模式
            cursor.execute('PRAGMA journal_mode')
            journal_mode = cursor.fetchone()[0]
            print(f"   📝 日志模式: {journal_mode}")
            
            return True
            
    except sqlite3.OperationalError as e:
        print(f"   ❌ 数据库锁定或访问问题: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 其他数据库问题: {e}")
        return False

def check_crawl_process():
    """检查是否有采集进程在运行"""
    print("\n🔄 检查采集进程状态...")
    
    try:
        import psutil
        
        # 查找Python进程
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline and any('spider' in arg.lower() or 'crawl' in arg.lower() for arg in cmdline):
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'cmdline': ' '.join(cmdline)
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if python_processes:
            print(f"   ⚠️ 发现 {len(python_processes)} 个可能的采集进程:")
            for proc in python_processes:
                print(f"      PID {proc['pid']}: {proc['cmdline']}")
        else:
            print("   ✅ 没有发现活跃的采集进程")
            
        return python_processes
        
    except ImportError:
        print("   ⚠️ 无法检查进程状态 (需要psutil库)")
        return []
    except Exception as e:
        print(f"   ❌ 检查进程失败: {e}")
        return []

def check_recent_activity():
    """检查最近的数据库活动"""
    print("\n📊 检查最近的数据库活动...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 检查最近添加的产品URL
            cursor.execute('''
                SELECT COUNT(*), MAX(created_at) as latest_time
                FROM product_urls
                WHERE created_at > datetime('now', '-1 hour')
            ''')
            
            recent_count, latest_time = cursor.fetchone()
            print(f"   📈 最近1小时新增产品URL: {recent_count}")
            print(f"   🕒 最新记录时间: {latest_time}")
            
            # 检查最近更新的子分类
            cursor.execute('''
                SELECT sc.sub_category_name, sc.updated_at, COUNT(pu.id) as product_count
                FROM sub_categories sc
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                WHERE sc.updated_at > datetime('now', '-2 hours')
                GROUP BY sc.id, sc.sub_category_name, sc.updated_at
                ORDER BY sc.updated_at DESC
                LIMIT 10
            ''')
            
            recent_updates = cursor.fetchall()
            if recent_updates:
                print(f"\n   📝 最近2小时更新的分类:")
                for name, updated_at, count in recent_updates:
                    print(f"      {name}: {count} 个产品 (更新于 {updated_at})")
            else:
                print(f"\n   ⚠️ 最近2小时没有分类更新")
            
    except Exception as e:
        print(f"   ❌ 检查活动失败: {e}")

def check_uncrawled_categories():
    """详细检查未采集分类"""
    print("\n🔍 详细检查未采集分类...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 查找未采集的分类
            cursor.execute('''
                SELECT sc.id, sc.sub_category_name, sc.sub_category_url, 
                       sc.created_at, sc.updated_at, COUNT(pu.id) as product_count
                FROM sub_categories sc
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY sc.id
                HAVING product_count = 0
                ORDER BY sc.created_at DESC
            ''')
            
            uncrawled = cursor.fetchall()
            
            print(f"   📋 未采集分类数量: {len(uncrawled)}")
            
            if uncrawled:
                print(f"\n   ❌ 未采集的分类详情:")
                for i, (cat_id, name, url, created_at, updated_at, count) in enumerate(uncrawled, 1):
                    print(f"      {i:2d}. [ID:{cat_id}] {name}")
                    print(f"          URL: {url}")
                    print(f"          创建时间: {created_at}")
                    print(f"          更新时间: {updated_at}")
                    print(f"          产品数量: {count}")
                    print()
            else:
                print(f"   ✅ 所有分类都已采集")
            
            return uncrawled
            
    except Exception as e:
        print(f"   ❌ 检查未采集分类失败: {e}")
        return []

def check_crawl_method():
    """检查采集方法是否正常工作"""
    print("\n🧪 测试采集方法...")
    
    try:
        # 获取一个未采集的分类进行测试
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT sc.id, sc.sub_category_name, sc.sub_category_url
                FROM sub_categories sc
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                WHERE pu.id IS NULL
                LIMIT 1
            ''')
            
            result = cursor.fetchone()
            
            if not result:
                print("   ✅ 没有未采集的分类可供测试")
                return True
            
            cat_id, cat_name, cat_url = result
            print(f"   🎯 测试分类: {cat_name} (ID: {cat_id})")
            print(f"   🔗 URL: {cat_url}")
            
            # 检查URL是否可访问
            try:
                import requests
                response = requests.head(cat_url, timeout=10)
                print(f"   🌐 URL状态: {response.status_code}")
                
                if response.status_code != 200:
                    print(f"   ⚠️ URL可能有问题，状态码: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"   ❌ URL访问失败: {e}")
                return False
            
            print(f"   ✅ 测试分类URL可正常访问")
            return True
            
    except Exception as e:
        print(f"   ❌ 测试采集方法失败: {e}")
        return False

def check_database_integrity():
    """检查数据库完整性"""
    print("\n🔧 检查数据库完整性...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 完整性检查
            cursor.execute('PRAGMA integrity_check')
            integrity = cursor.fetchone()[0]
            print(f"   🔍 数据库完整性: {integrity}")
            
            # 检查表结构
            cursor.execute('PRAGMA table_info(product_urls)')
            columns = cursor.fetchall()
            expected_columns = ['id', 'sub_category_id', 'product_name', 'product_url', 'created_at', 'updated_at']
            actual_columns = [col[1] for col in columns]
            
            missing_columns = set(expected_columns) - set(actual_columns)
            if missing_columns:
                print(f"   ❌ 缺少字段: {missing_columns}")
                return False
            else:
                print(f"   ✅ product_urls表结构正常")
            
            # 检查外键约束
            cursor.execute('PRAGMA foreign_key_check')
            fk_errors = cursor.fetchall()
            if fk_errors:
                print(f"   ⚠️ 外键约束错误: {len(fk_errors)} 个")
                for error in fk_errors[:5]:  # 只显示前5个
                    print(f"      {error}")
            else:
                print(f"   ✅ 外键约束正常")
            
            return True
            
    except Exception as e:
        print(f"   ❌ 数据库完整性检查失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 采集问题诊断工具")
    print("=" * 60)
    print("诊断为什么product_urls数量没有增加")
    print()
    
    # 执行各项检查
    results = []
    
    results.append(check_database_locks())
    results.append(check_database_integrity())
    
    crawl_processes = check_crawl_process()
    check_recent_activity()
    uncrawled = check_uncrawled_categories()
    results.append(check_crawl_method())
    
    print("\n" + "=" * 60)
    print("🎯 诊断结果总结")
    print("=" * 60)
    
    if crawl_processes:
        print("⚠️ 发现可能的问题:")
        print(f"   • 有 {len(crawl_processes)} 个采集进程在运行")
        print(f"   • 这可能导致数据库锁定或冲突")
        print(f"   • 建议停止这些进程后重新尝试")
    
    if uncrawled:
        print(f"\n📋 待处理:")
        print(f"   • 有 {len(uncrawled)} 个分类未采集")
        print(f"   • 可以使用GUI的批量采集功能")
    
    if all(results):
        print(f"\n✅ 系统状态正常")
        if uncrawled:
            print(f"💡 建议:")
            print(f"   1. 确保没有其他采集进程在运行")
            print(f"   2. 重新启动程序")
            print(f"   3. 使用批量采集功能")
        else:
            print(f"🎉 所有分类都已采集完成！")
    else:
        print(f"\n❌ 发现系统问题，需要进一步检查")

if __name__ == "__main__":
    main()
