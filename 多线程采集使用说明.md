# 🚀 多线程采集功能使用说明

## 📋 功能概述

第三阶段产品详情采集现在支持多线程并发，大大提升采集速度！

### ✨ 主要特性

- **🔥 高速采集**：支持1-20个并发线程
- **🎛️ 灵活配置**：可在GUI中自定义线程数
- **🔒 线程安全**：完全线程安全的数据库操作
- **🔄 智能重试**：失败自动重试机制
- **📊 实时监控**：多线程进度实时显示
- **⏸️ 完全控制**：支持暂停/继续/停止

## 🎯 使用方法

### 1. 设置线程数

在第三阶段控制面板中：
- **并发线程数**：使用数字输入框设置1-20个线程
- **推荐设置**：
  - 网络良好：8-12个线程
  - 网络一般：5-8个线程
  - 网络较差：3-5个线程
  - 测试环境：1-3个线程

### 2. 启动多线程采集

1. 选择采集范围：
   - **全部**：采集所有产品
   - **仅未采集**：只采集新产品
   - **仅失败的**：重试失败的产品

2. 设置线程数（1-20）

3. 点击"开始第三阶段"

### 3. 监控采集进度

- **实时进度**：显示已处理/总数量
- **成功/失败统计**：实时更新
- **自动刷新**：界面数据自动更新
- **详细日志**：显示采集详情

## 📊 性能对比

| 线程数 | 预估速度提升 | 适用场景 |
|--------|-------------|----------|
| 1个线程 | 基准速度 | 测试/调试 |
| 5个线程 | 3-4倍 | 稳定采集 |
| 10个线程 | 6-8倍 | 高速采集 |
| 15个线程 | 8-12倍 | 极速采集 |
| 20个线程 | 10-15倍 | 最高速度 |

## ⚠️ 注意事项

### 线程数选择建议

- **不要设置过高**：过多线程可能导致网络拥塞
- **根据网络调整**：网络不稳定时减少线程数
- **服务器友好**：避免对目标服务器造成过大压力

### 最佳实践

1. **首次使用**：建议从5个线程开始
2. **观察效果**：监控成功率和速度
3. **逐步调整**：根据效果增减线程数
4. **稳定为主**：优先保证采集成功率

## 🔧 技术特性

### 线程安全保证

- **数据库锁**：使用线程锁保护数据库操作
- **状态同步**：线程安全的状态管理
- **统计准确**：准确的多线程统计

### 智能控制

- **暂停机制**：所有线程同步暂停
- **停止机制**：优雅停止所有线程
- **错误处理**：单线程错误不影响其他线程

### 资源管理

- **连接池**：高效的数据库连接管理
- **内存控制**：合理的内存使用
- **CPU优化**：平衡的CPU使用

## 📈 使用示例

### 场景1：快速采集大量产品

```
设置：10个线程
范围：仅未采集
预期：比单线程快6-8倍
```

### 场景2：重试失败产品

```
设置：5个线程
范围：仅失败的
预期：稳定重试，高成功率
```

### 场景3：完整重新采集

```
设置：8个线程
范围：全部
预期：全面更新所有产品信息
```

## 🎉 效果展示

使用多线程采集后，您将看到：

- **📈 速度提升**：采集速度显著提升
- **📊 实时统计**：准确的进度和统计
- **🔄 自动刷新**：界面数据实时更新
- **📝 详细日志**：完整的采集记录

## 🚀 开始使用

现在就重新启动程序，体验多线程高速采集的魅力吧！

1. 运行 `python main.py`
2. 进入第三阶段
3. 设置合适的线程数
4. 开始高速采集！

---

**提示**：第一次使用建议设置5个线程，观察效果后再调整。记住，稳定性比速度更重要！
