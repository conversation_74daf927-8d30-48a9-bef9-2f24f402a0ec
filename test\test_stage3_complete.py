#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三阶段完整功能测试
测试产品详情采集的完整流程
"""

import os
import sys
import shutil
import sqlite3
import time

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database_redesign import DatabaseRedesign
from product_detail_parser import ProductDetailParser
from stage3_crawler import Stage3Crawler

def test_stage3_complete():
    """测试第三阶段完整功能"""
    print("=" * 80)
    print("第三阶段完整功能测试")
    print("=" * 80)
    
    # 1. 创建新数据库并迁移数据
    print("\n🔧 步骤1: 数据库重新设计和数据迁移")
    print("-" * 50)
    
    test_db = "test_stage3_complete.db"
    if os.path.exists(test_db):
        os.remove(test_db)
    
    db = DatabaseRedesign(test_db)
    
    # 查找并迁移现有数据
    source_dbs = [
        "../demo/demo_stage1.db",
        "demo/demo_stage1.db",
        "../demo_stage1.db"
    ]
    
    for source_db in source_dbs:
        if os.path.exists(source_db):
            print(f"发现数据源: {source_db}")
            db.migrate_existing_data(source_db)
            break
    else:
        print("❌ 未找到数据源，请先运行第一阶段和第二阶段")
        return
    
    # 显示迁移后的统计
    stats = db.get_statistics()
    print(f"\n📊 数据迁移统计:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # 2. 测试产品详情解析器
    print(f"\n🧪 步骤2: 测试产品详情解析器")
    print("-" * 50)
    
    parser = ProductDetailParser()
    test_url = "https://novolinepromo.com/fitness-shaking-cup-portable-15oz-sports-water-bottle/"
    
    print(f"测试URL: {test_url}")
    detail_data = parser.parse_product_detail(test_url)
    
    if detail_data:
        print(f"✅ 解析成功:")
        print(f"   标题: {detail_data.get('title', 'N/A')}")
        print(f"   SKU: {detail_data.get('sku', 'N/A')}")
        print(f"   导航: {detail_data.get('nav_full', 'N/A')}")
        print(f"   分类: {detail_data.get('category_name', 'N/A')}")
        print(f"   基础价格: ${detail_data.get('regular_price', 'N/A')}")
        print(f"   阶梯价格层级: {len(detail_data.get('tier_prices', []))}")
        
        # 显示阶梯价格
        tier_prices = detail_data.get('tier_prices', [])
        if tier_prices:
            print(f"\n   📊 阶梯价格详情:")
            for i, tier in enumerate(tier_prices[:3], 1):  # 只显示前3个
                print(f"      {i}. {tier.get('quantity_display', 'N/A')} - "
                      f"${tier.get('unit_price', 'N/A')} "
                      f"({tier.get('discount_percent', 0)}% off)")
        
        # 生成原始数据
        raw_data = parser.generate_price_data_raw(tier_prices)
        print(f"\n   📝 原始价格数据: {raw_data[:100]}...")
        
    else:
        print(f"❌ 解析失败")
        return
    
    # 3. 测试第三阶段爬虫
    print(f"\n🚀 步骤3: 测试第三阶段爬虫")
    print("-" * 50)
    
    crawler = Stage3Crawler(test_db)
    
    # 设置回调
    def on_progress(stage, message):
        print(f"[{stage}] {message}")
    
    def on_error(message):
        print(f"❌ 错误: {message}")
    
    def on_completion(message):
        print(f"✅ 完成: {message}")
    
    crawler.set_callbacks(on_progress, on_error, on_completion)
    
    # 显示第三阶段统计
    stage3_stats = crawler.get_stage3_statistics()
    print(f"\n📊 第三阶段统计:")
    for key, value in stage3_stats.items():
        print(f"   {key}: {value}")
    
    if stage3_stats['pending'] == 0:
        print(f"\n⚠️ 没有待采集的产品，手动添加一个测试产品...")
        
        # 手动添加测试产品
        with sqlite3.connect(test_db) as conn:
            cursor = conn.cursor()
            
            # 查找一个子目录
            cursor.execute('SELECT id FROM sub_categories LIMIT 1')
            sub_cat_result = cursor.fetchone()
            
            if sub_cat_result:
                sub_cat_id = sub_cat_result[0]
                
                # 插入测试产品
                cursor.execute('''
                    INSERT OR REPLACE INTO product_urls 
                    (sub_category_id, product_name, product_url)
                    VALUES (?, ?, ?)
                ''', (sub_cat_id, "测试产品 - Fitness Shaking Cup", test_url))
                
                conn.commit()
                print(f"✅ 已添加测试产品")
            else:
                print(f"❌ 未找到子目录，无法添加测试产品")
                return
    
    # 询问是否开始采集
    choice = input(f"\n开始第三阶段采集测试? (y/N): ").strip().lower()
    if choice == 'y':
        print(f"\n🚀 开始第三阶段采集...")
        
        # 限制只采集1个产品进行测试
        with sqlite3.connect(test_db) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT pu.id, pu.product_name, pu.product_url
                FROM product_urls pu
                LEFT JOIN product_details pd ON pu.id = pd.product_url_id
                WHERE pd.id IS NULL
                LIMIT 1
            ''')
            test_product = cursor.fetchone()
        
        if test_product:
            product_url_id, product_name, product_url = test_product
            print(f"测试产品: {product_name}")
            
            # 手动采集单个产品
            success = crawler._crawl_single_product(product_url_id, product_url, product_name)
            
            if success:
                print(f"✅ 采集成功")
                
                # 查看采集结果
                with sqlite3.connect(test_db) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        SELECT pd.title, pd.sku, pd.nav_full, pd.category_name, 
                               pd.regular_price, pd.price_data_raw,
                               COUNT(tp.id) as tier_count
                        FROM product_details pd
                        LEFT JOIN tier_prices tp ON pd.id = tp.product_detail_id
                        WHERE pd.product_url_id = ?
                        GROUP BY pd.id
                    ''', (product_url_id,))
                    
                    result = cursor.fetchone()
                    if result:
                        title, sku, nav_full, category_name, regular_price, price_data_raw, tier_count = result
                        
                        print(f"\n📋 采集结果:")
                        print(f"   标题: {title}")
                        print(f"   SKU: {sku}")
                        print(f"   导航: {nav_full}")
                        print(f"   分类: {category_name}")
                        print(f"   基础价格: ${regular_price}")
                        print(f"   阶梯价格层级: {tier_count}")
                        print(f"   原始数据: {(price_data_raw or '')[:100]}...")
                        
                        # 查看阶梯价格详情
                        cursor.execute('''
                            SELECT quantity_display, unit_price, discount_percent
                            FROM tier_prices
                            WHERE product_detail_id = (
                                SELECT id FROM product_details WHERE product_url_id = ?
                            )
                            ORDER BY quantity_min
                        ''', (product_url_id,))
                        
                        tier_prices = cursor.fetchall()
                        if tier_prices:
                            print(f"\n   📊 阶梯价格详情:")
                            for i, (qty_display, unit_price, discount) in enumerate(tier_prices, 1):
                                print(f"      {i}. {qty_display} - ${unit_price} ({discount}% off)")
                
            else:
                print(f"❌ 采集失败")
        else:
            print(f"❌ 未找到测试产品")
    
    # 4. 最终统计
    print(f"\n📊 最终统计:")
    print("-" * 50)
    
    final_stats = db.get_statistics()
    for key, value in final_stats.items():
        print(f"   {key}: {value}")
    
    stage3_final_stats = crawler.get_stage3_statistics()
    print(f"\n📊 第三阶段最终统计:")
    for key, value in stage3_final_stats.items():
        print(f"   {key}: {value}")
    
    print(f"\n🎉 第三阶段测试完成!")
    print(f"   测试数据库: {test_db}")
    print(f"   可以使用数据库工具查看详细数据")

def test_enhanced_gui():
    """测试增强的GUI"""
    print(f"\n🖥️ 启动增强的GUI界面...")
    print("   这将展示第三阶段的完整管理界面")
    
    try:
        from enhanced_gui import EnhancedSpiderGUI
        app = EnhancedSpiderGUI()
        app.mainloop()
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")

def main():
    """主函数"""
    print("🎯 第三阶段完整功能测试")
    print("\n功能说明:")
    print("1. 数据库重新设计，支持完整的产品详情存储")
    print("2. 产品详情解析器，支持导航、SKU、阶梯价格等")
    print("3. 第三阶段爬虫，支持状态管理、重试机制")
    print("4. 增强的GUI界面，支持第三阶段管理")
    
    while True:
        print(f"\n请选择测试项目:")
        print("1. 完整功能测试")
        print("2. 启动增强GUI")
        print("3. 退出")
        
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            test_stage3_complete()
        elif choice == "2":
            test_enhanced_gui()
        elif choice == "3":
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选择，请输入 1-3")

if __name__ == "__main__":
    main()
