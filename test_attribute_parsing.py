#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试属性解析功能
"""

import sqlite3
from stage4_image_crawler import Stage4ImageCrawler
from bs4 import BeautifulSoup

def test_attribute_parsing_logic():
    """测试属性解析逻辑"""
    print("=" * 60)
    print("🧪 测试属性解析逻辑")
    print("=" * 60)
    
    # 测试用的HTML示例
    test_cases = [
        {
            "name": "标准WooCommerce表格",
            "html": '''
            <h2>Additional Information</h2>
            <table class="woocommerce-product-attributes shop_attributes" aria-label="Product Details">
                <tbody>
                    <tr class="woocommerce-product-attributes-item">
                        <th class="woocommerce-product-attributes-item__label" scope="row">Material</th>
                        <td class="woocommerce-product-attributes-item__value"><p>Stainless Steel</p></td>
                    </tr>
                    <tr class="woocommerce-product-attributes-item">
                        <th class="woocommerce-product-attributes-item__label" scope="row">Size</th>
                        <td class="woocommerce-product-attributes-item__value"><p>16OZ</p></td>
                    </tr>
                    <tr class="woocommerce-product-attributes-item">
                        <th class="woocommerce-product-attributes-item__label" scope="row">Color</th>
                        <td class="woocommerce-product-attributes-item__value"><p>Silver</p></td>
                    </tr>
                    <tr class="woocommerce-product-attributes-item">
                        <th class="woocommerce-product-attributes-item__label" scope="row">Weight</th>
                        <td class="woocommerce-product-attributes-item__value"><p>0.5 lbs</p></td>
                    </tr>
                    <tr class="woocommerce-product-attributes-item">
                        <th class="woocommerce-product-attributes-item__label" scope="row">Brand</th>
                        <td class="woocommerce-product-attributes-item__value"><p>Novoline</p></td>
                    </tr>
                </tbody>
            </table>
            '''
        },
        {
            "name": "简单表格",
            "html": '''
            <table class="product-attributes">
                <tr>
                    <th>Material</th>
                    <td>Fabric</td>
                </tr>
                <tr>
                    <th>Size</th>
                    <td>19.69*19.69 inches</td>
                </tr>
            </table>
            '''
        },
        {
            "name": "DL/DT/DD结构",
            "html": '''
            <dl>
                <dt>Material</dt>
                <dd>Plastic</dd>
                <dt>Dimensions</dt>
                <dd>10x5x3 cm</dd>
                <dt>Weight</dt>
                <dd>200g</dd>
            </dl>
            '''
        },
        {
            "name": "段落格式",
            "html": '''
            <div>
                <p>Material: Cotton</p>
                <p>Size: Large</p>
                <p>Color: Blue</p>
                <p>Origin: China</p>
            </div>
            '''
        }
    ]
    
    try:
        crawler = Stage4ImageCrawler("novoline_spider_v2.db")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试用例 {i}: {test_case['name']}")
            print("-" * 40)
            
            attributes = crawler._parse_attributes_from_additional_info(test_case['html'])
            
            print(f"   解析结果: {len(attributes)} 个属性")
            for j, (name, value) in enumerate(attributes, 1):
                print(f"      属性{j}: {name} = {value}")
            
            if len(attributes) > 4:
                print(f"   ⚠️ 超过4个属性，只保留前4个")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_database_structure():
    """测试数据库结构"""
    print(f"\n" + "=" * 60)
    print("🔍 测试数据库结构")
    print("=" * 60)
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute('PRAGMA table_info(product_images)')
            columns = cursor.fetchall()
            
            print(f"📋 product_images表结构:")
            attribute_fields = []
            for col in columns:
                col_name = col[1]
                if 'attribute' in col_name:
                    attribute_fields.append(col_name)
                    print(f"   ✅ {col_name} ({col[2]})")
            
            if len(attribute_fields) == 8:  # 4个name + 4个value
                print(f"\n✅ 属性字段完整: {len(attribute_fields)} 个字段")
            else:
                print(f"\n⚠️ 属性字段不完整: 期望8个，实际{len(attribute_fields)}个")
            
            # 检查现有数据
            cursor.execute('SELECT COUNT(*) FROM product_images')
            total_count = cursor.fetchone()[0]
            
            cursor.execute('''
                SELECT COUNT(*) FROM product_images 
                WHERE attribute1_name IS NOT NULL OR attribute2_name IS NOT NULL
            ''')
            with_attributes = cursor.fetchone()[0]
            
            print(f"\n📊 数据统计:")
            print(f"   总记录数: {total_count}")
            print(f"   有属性数据: {with_attributes}")
            
            return True
            
    except Exception as e:
        print(f"❌ 数据库结构测试失败: {e}")
        return False

def simulate_single_product_crawl():
    """模拟单个产品的属性采集"""
    print(f"\n" + "=" * 60)
    print("🚀 模拟单个产品属性采集")
    print("=" * 60)
    
    try:
        # 获取一个测试产品
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT pu.id, pu.product_url, pu.product_name
                FROM product_urls pu
                LEFT JOIN product_images pi ON pu.id = pi.product_url_id
                WHERE pi.product_url_id IS NULL
                LIMIT 1
            ''')
            
            result = cursor.fetchone()
            
            if not result:
                print("❌ 没有找到可测试的产品")
                return False
            
            product_id, product_url, product_name = result
            
            print(f"🎯 测试产品:")
            print(f"   ID: {product_id}")
            print(f"   名称: {product_name}")
            print(f"   URL: {product_url}")
            
            # 模拟属性数据
            mock_attributes = [
                ("Material", "Stainless Steel"),
                ("Size", "16OZ"),
                ("Color", "Silver"),
                ("Weight", "0.5 lbs")
            ]
            
            print(f"\n📝 模拟属性数据:")
            for i, (name, value) in enumerate(mock_attributes, 1):
                print(f"   属性{i}: {name} = {value}")
            
            # 模拟保存到数据库
            print(f"\n💾 模拟保存到数据库...")
            
            # 准备属性数据
            attribute_data = {}
            for i in range(1, 5):
                if i <= len(mock_attributes):
                    name, value = mock_attributes[i-1]
                    attribute_data[f'attribute{i}_name'] = name
                    attribute_data[f'attribute{i}_value'] = value
                else:
                    attribute_data[f'attribute{i}_name'] = None
                    attribute_data[f'attribute{i}_value'] = None
            
            # 插入测试数据
            cursor.execute('''
                INSERT OR REPLACE INTO product_images (
                    product_url_id, product_url, product_title,
                    attribute1_name, attribute1_value,
                    attribute2_name, attribute2_value,
                    attribute3_name, attribute3_value,
                    attribute4_name, attribute4_value,
                    crawl_status, images_downloaded
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'success', 0)
            ''', (
                product_id, product_url, product_name,
                attribute_data['attribute1_name'], attribute_data['attribute1_value'],
                attribute_data['attribute2_name'], attribute_data['attribute2_value'],
                attribute_data['attribute3_name'], attribute_data['attribute3_value'],
                attribute_data['attribute4_name'], attribute_data['attribute4_value']
            ))
            
            conn.commit()
            
            print(f"✅ 测试数据保存成功")
            
            # 验证保存结果
            cursor.execute('''
                SELECT attribute1_name, attribute1_value, attribute2_name, attribute2_value,
                       attribute3_name, attribute3_value, attribute4_name, attribute4_value
                FROM product_images 
                WHERE product_url_id = ?
            ''', (product_id,))
            
            saved_data = cursor.fetchone()
            if saved_data:
                print(f"\n🔍 验证保存结果:")
                for i in range(4):
                    name = saved_data[i*2]
                    value = saved_data[i*2+1]
                    if name and value:
                        print(f"   属性{i+1}: {name} = {value}")
                    else:
                        print(f"   属性{i+1}: 空")
            
            return True
            
    except Exception as e:
        print(f"❌ 模拟采集失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 属性解析功能测试")
    print("测试Additional Information表格的属性解析和存储")
    print()
    
    # 测试属性解析逻辑
    parsing_ok = test_attribute_parsing_logic()
    
    # 测试数据库结构
    db_ok = test_database_structure()
    
    # 模拟单个产品采集
    crawl_ok = simulate_single_product_crawl()
    
    print("\n" + "=" * 60)
    print("🎯 测试结果总结")
    print("=" * 60)
    
    print(f"属性解析逻辑: {'✅ 通过' if parsing_ok else '❌ 失败'}")
    print(f"数据库结构: {'✅ 通过' if db_ok else '❌ 失败'}")
    print(f"模拟采集: {'✅ 通过' if crawl_ok else '❌ 失败'}")
    
    if all([parsing_ok, db_ok, crawl_ok]):
        print(f"\n🎉 所有测试通过！")
        print(f"\n🚀 下一步:")
        print(f"   1. 启动GUI: python main.py")
        print(f"   2. 进入第四步：产品图片")
        print(f"   3. 开始采集，观察属性字段")
        print(f"   4. 检查表格中的属性显示")
    else:
        print(f"\n⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
