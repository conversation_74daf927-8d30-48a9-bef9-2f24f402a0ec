#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试步骤四的新查询逻辑
"""

import sqlite3
from stage4_image_crawler import Stage4ImageCrawler

def test_query_logic():
    """测试新的查询逻辑"""
    print("=" * 60)
    print("🧪 测试步骤四新查询逻辑")
    print("=" * 60)
    
    try:
        # 初始化爬虫
        crawler = Stage4ImageCrawler("novoline_spider_v2.db")
        
        # 测试不同的过滤条件
        filter_types = ['all', 'pending', 'failed']
        
        for filter_type in filter_types:
            print(f"\n📋 测试过滤条件: {filter_type}")
            print("-" * 40)
            
            products = crawler.get_products_for_image_crawl(filter_type)
            
            print(f"   找到产品数量: {len(products)}")
            
            if products:
                print(f"   前3个产品示例:")
                for i, product in enumerate(products[:3], 1):
                    print(f"      {i}. ID: {product['product_url_id']}")
                    print(f"         名称: {product['product_name']}")
                    print(f"         状态: {product['crawl_status']}")
                    print(f"         尝试次数: {product['crawl_attempts']}")
                    print()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_data_consistency():
    """检查数据一致性"""
    print(f"\n" + "=" * 60)
    print("🔍 检查数据一致性")
    print("=" * 60)
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 总product_urls数量
            cursor.execute('SELECT COUNT(*) FROM product_urls')
            total_urls = cursor.fetchone()[0]
            
            # product_images中的数量
            cursor.execute('SELECT COUNT(*) FROM product_images')
            total_images = cursor.fetchone()[0]
            
            # 成功采集的数量
            cursor.execute('SELECT COUNT(*) FROM product_images WHERE crawl_status = "success"')
            success_images = cursor.fetchone()[0]
            
            # 失败的数量
            cursor.execute('SELECT COUNT(*) FROM product_images WHERE crawl_status = "failed"')
            failed_images = cursor.fetchone()[0]
            
            # 待处理的数量（product_urls中不在product_images中的）
            cursor.execute('''
                SELECT COUNT(*) FROM product_urls pu
                LEFT JOIN product_images pi ON pu.id = pi.product_url_id
                WHERE pi.product_url_id IS NULL
            ''')
            pending_count = cursor.fetchone()[0]
            
            print(f"📊 数据统计:")
            print(f"   总产品URL: {total_urls:,}")
            print(f"   已有图片记录: {total_images:,}")
            print(f"   成功采集: {success_images:,}")
            print(f"   采集失败: {failed_images:,}")
            print(f"   待处理: {pending_count:,}")
            
            # 验证逻辑
            expected_total = success_images + failed_images + pending_count
            print(f"\n🔍 逻辑验证:")
            print(f"   成功 + 失败 + 待处理 = {expected_total:,}")
            print(f"   总产品URL = {total_urls:,}")
            print(f"   差异: {abs(expected_total - total_urls):,}")
            
            if expected_total == total_urls:
                print(f"   ✅ 数据一致性检查通过")
            else:
                print(f"   ⚠️ 数据可能存在不一致")
            
            # 显示一些示例数据
            print(f"\n📋 待处理产品示例:")
            cursor.execute('''
                SELECT pu.id, pu.product_name, pu.product_url
                FROM product_urls pu
                LEFT JOIN product_images pi ON pu.id = pi.product_url_id
                WHERE pi.product_url_id IS NULL
                ORDER BY pu.id
                LIMIT 5
            ''')
            
            pending_samples = cursor.fetchall()
            for i, (pid, name, url) in enumerate(pending_samples, 1):
                print(f"   {i}. [ID:{pid}] {name}")
                print(f"      URL: {url}")
            
            if len(pending_samples) == 0:
                print(f"   🎉 没有待处理的产品，所有产品都已采集！")
            
            return True
            
    except Exception as e:
        print(f"❌ 检查数据一致性失败: {e}")
        return False

def simulate_stage4_workflow():
    """模拟步骤四工作流程"""
    print(f"\n" + "=" * 60)
    print("🚀 模拟步骤四工作流程")
    print("=" * 60)
    
    try:
        crawler = Stage4ImageCrawler("novoline_spider_v2.db")
        
        # 1. 获取待处理产品
        pending_products = crawler.get_products_for_image_crawl('pending')
        print(f"📋 待处理产品: {len(pending_products)} 个")
        
        # 2. 获取失败产品
        failed_products = crawler.get_products_for_image_crawl('failed')
        print(f"❌ 失败产品: {len(failed_products)} 个")
        
        # 3. 获取所有需要处理的产品
        all_products = crawler.get_products_for_image_crawl('all')
        print(f"📊 总需处理: {len(all_products)} 个")
        
        # 4. 验证逻辑
        expected_all = len(pending_products) + len(failed_products)
        print(f"\n🔍 逻辑验证:")
        print(f"   待处理 + 失败 = {expected_all}")
        print(f"   all查询结果 = {len(all_products)}")
        
        if expected_all == len(all_products):
            print(f"   ✅ 查询逻辑正确")
        else:
            print(f"   ⚠️ 查询逻辑可能有问题")
            print(f"   差异: {abs(expected_all - len(all_products))}")
        
        # 5. 显示推荐操作
        print(f"\n💡 推荐操作:")
        if len(pending_products) > 0:
            print(f"   1. 选择'pending'模式采集 {len(pending_products)} 个新产品")
        if len(failed_products) > 0:
            print(f"   2. 选择'failed'模式重新采集 {len(failed_products)} 个失败产品")
        if len(all_products) == 0:
            print(f"   🎉 所有产品都已成功采集图片！")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟工作流程失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 步骤四查询逻辑测试")
    print("验证新的简化查询逻辑和数据一致性")
    print()
    
    # 测试查询逻辑
    query_ok = test_query_logic()
    
    # 检查数据一致性
    consistency_ok = check_data_consistency()
    
    # 模拟工作流程
    workflow_ok = simulate_stage4_workflow()
    
    print("\n" + "=" * 60)
    print("🎯 测试结果总结")
    print("=" * 60)
    
    print(f"查询逻辑测试: {'✅ 通过' if query_ok else '❌ 失败'}")
    print(f"数据一致性检查: {'✅ 通过' if consistency_ok else '❌ 失败'}")
    print(f"工作流程模拟: {'✅ 通过' if workflow_ok else '❌ 失败'}")
    
    if all([query_ok, consistency_ok, workflow_ok]):
        print(f"\n🎉 所有测试通过！")
        print(f"\n🚀 下一步:")
        print(f"   1. 启动GUI: python main.py")
        print(f"   2. 进入第四步：产品图片")
        print(f"   3. 选择'pending'开始采集")
        print(f"   4. 观察新的数据表格显示")
    else:
        print(f"\n⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
