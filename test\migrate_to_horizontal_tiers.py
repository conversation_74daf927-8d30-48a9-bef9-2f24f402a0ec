#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库结构迁移脚本 - 横向阶梯价格结构
将product_details表重新设计为横向存储阶梯价格信息
移除tier_prices表，所有信息合并到一张表中
"""

import os
import sqlite3
import shutil
from datetime import datetime

def backup_database(db_path):
    """备份数据库"""
    if not os.path.exists(db_path):
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = "backup"
    os.makedirs(backup_dir, exist_ok=True)
    backup_path = f"{backup_dir}/{os.path.basename(db_path)}.backup_{timestamp}"
    
    shutil.copy2(db_path, backup_path)
    print(f"✅ 数据库已备份到: {backup_path}")
    return backup_path

def check_table_exists(cursor, table_name):
    """检查表是否存在"""
    cursor.execute('''
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
    ''', (table_name,))
    return cursor.fetchone() is not None

def migrate_to_horizontal_structure(db_path):
    """迁移到横向阶梯价格结构"""
    print(f"🔧 开始迁移数据库: {db_path}")
    
    # 备份数据库
    backup_path = backup_database(db_path)
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 1. 检查现有的product_details表
            if check_table_exists(cursor, 'product_details'):
                print("🔄 备份现有product_details数据...")
                
                # 备份现有数据
                cursor.execute("SELECT * FROM product_details")
                existing_data = cursor.fetchall()
                
                # 获取列信息
                cursor.execute("PRAGMA table_info(product_details)")
                old_columns = [row[1] for row in cursor.fetchall()]
                print(f"   现有列: {old_columns}")
                print(f"   现有数据: {len(existing_data)} 条")
                
                # 删除旧表
                cursor.execute("DROP TABLE product_details")
                print("   ✅ 旧表已删除")
            
            # 2. 创建新的product_details表（横向阶梯价格结构）
            print("🔧 创建新的product_details表...")
            cursor.execute('''
                CREATE TABLE product_details (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_url_id INTEGER NOT NULL UNIQUE,
                    
                    -- 基本信息
                    title TEXT,
                    sku TEXT,
                    product_image_url TEXT,
                    short_description TEXT,
                    
                    -- 导航信息
                    nav_level1 TEXT,
                    nav_level2 TEXT,
                    nav_level3 TEXT,
                    nav_full TEXT,
                    
                    -- 分类信息
                    category_name TEXT,
                    category_url TEXT,
                    
                    -- 阶梯价格1 (通常是1件的价格)
                    tier1_quantity TEXT,        -- "1 piece"
                    tier1_price DECIMAL(10,2),  -- 4.29
                    tier1_discount TEXT,        -- "0% off"
                    
                    -- 阶梯价格2
                    tier2_quantity TEXT,        -- "200 pieces"
                    tier2_price DECIMAL(10,2),  -- 3.30
                    tier2_discount TEXT,        -- "23% off"
                    
                    -- 阶梯价格3
                    tier3_quantity TEXT,        -- "500 pieces"
                    tier3_price DECIMAL(10,2),  -- 3.26
                    tier3_discount TEXT,        -- "24% off"
                    
                    -- 阶梯价格4
                    tier4_quantity TEXT,        -- "1,000 pieces"
                    tier4_price DECIMAL(10,2),  -- 3.22
                    tier4_discount TEXT,        -- "24% off"
                    
                    -- 阶梯价格5
                    tier5_quantity TEXT,        -- "3,000 pieces"
                    tier5_price DECIMAL(10,2),  -- 3.18
                    tier5_discount TEXT,        -- "25% off"
                    
                    -- 阶梯价格6
                    tier6_quantity TEXT,        -- "5,000+ pieces"
                    tier6_price DECIMAL(10,2),  -- 3.13
                    tier6_discount TEXT,        -- "27% off"
                    
                    -- 合并的完整阶梯价格信息
                    all_tiers_combined TEXT,    -- "1 piece: $4.29 (0% off)||200 pieces: $3.30 (23% off)||..."
                    
                    -- 采集状态管理
                    crawl_status TEXT DEFAULT 'pending',
                    crawl_attempts INTEGER DEFAULT 0,
                    last_crawl_attempt TIMESTAMP,
                    error_message TEXT,
                    
                    -- 时间戳
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (product_url_id) REFERENCES product_urls (id)
                )
            ''')
            print("   ✅ 新表创建完成")
            
            # 3. 如果有旧数据，尝试迁移基本信息
            if 'existing_data' in locals() and existing_data:
                print("🔄 迁移基本信息...")
                
                # 创建列映射
                column_mapping = {}
                for i, col in enumerate(old_columns):
                    column_mapping[col] = i
                
                migrated_count = 0
                for row in existing_data:
                    try:
                        # 提取基本信息
                        product_url_id = row[column_mapping.get('product_url_id', 1)]
                        title = row[column_mapping.get('title', None)] if 'title' in column_mapping else None
                        sku = row[column_mapping.get('sku', None)] if 'sku' in column_mapping else None
                        nav_level1 = row[column_mapping.get('nav_level1', None)] if 'nav_level1' in column_mapping else None
                        nav_level2 = row[column_mapping.get('nav_level2', None)] if 'nav_level2' in column_mapping else None
                        nav_level3 = row[column_mapping.get('nav_level3', None)] if 'nav_level3' in column_mapping else None
                        nav_full = row[column_mapping.get('nav_full', None)] if 'nav_full' in column_mapping else None
                        category_name = row[column_mapping.get('category_name', None)] if 'category_name' in column_mapping else None
                        category_url = row[column_mapping.get('category_url', None)] if 'category_url' in column_mapping else None
                        crawl_status = row[column_mapping.get('crawl_status', None)] if 'crawl_status' in column_mapping else 'pending'
                        
                        # 插入基本信息（阶梯价格字段为空，等待重新采集）
                        cursor.execute('''
                            INSERT INTO product_details 
                            (product_url_id, title, sku, nav_level1, nav_level2, nav_level3, nav_full,
                             category_name, category_url, crawl_status, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                        ''', (product_url_id, title, sku, nav_level1, nav_level2, nav_level3, nav_full,
                              category_name, category_url, 'pending'))  # 重置状态为pending，需要重新采集阶梯价格
                        
                        migrated_count += 1
                        
                    except Exception as e:
                        print(f"   ⚠️ 迁移记录失败: {e}")
                        continue
                
                print(f"   ✅ 迁移了 {migrated_count} 条基本信息")
            
            # 4. 删除tier_prices表（如果存在）
            if check_table_exists(cursor, 'tier_prices'):
                print("🗑️ 删除tier_prices表...")
                cursor.execute("DROP TABLE tier_prices")
                print("   ✅ tier_prices表已删除")
            
            conn.commit()
            
        print("✅ 数据库迁移完成")
        
        # 验证新结构
        print("\n🔍 验证新表结构:")
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(product_details)")
            new_columns = [row[1] for row in cursor.fetchall()]
            print(f"   新表列数: {len(new_columns)}")
            print(f"   阶梯价格字段: tier1_quantity, tier1_price, tier1_discount, ...")
            print(f"   合并字段: all_tiers_combined")
            
            # 检查数据
            cursor.execute("SELECT COUNT(*) FROM product_details")
            count = cursor.fetchone()[0]
            print(f"   数据条数: {count}")
        
        # 测试新的插入方法
        print("\n🧪 测试新的数据结构:")
        from database import DatabaseManager
        db = DatabaseManager(db_path)
        
        # 模拟测试数据
        test_data = {
            'title': 'Test Product',
            'sku': 'TEST123',
            'nav_level1': 'Test Category',
            'nav_full': 'Test Category > Test Subcategory',
            'tier_prices': [
                {'quantity_display': '1 piece', 'unit_price': 4.29, 'discount_percent': 0},
                {'quantity_display': '200 pieces', 'unit_price': 3.30, 'discount_percent': 23},
                {'quantity_display': '500 pieces', 'unit_price': 3.26, 'discount_percent': 24}
            ]
        }
        
        test_id = db.insert_product_detail(999, test_data)
        print(f"   ✅ 测试插入成功，ID: {test_id}")
        
        # 验证数据
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT tier1_quantity, tier1_price, tier1_discount, all_tiers_combined FROM product_details WHERE id = ?', (test_id,))
            result = cursor.fetchone()
            if result:
                print(f"   ✅ 阶梯1: {result[0]} - ${result[1]} ({result[2]})")
                print(f"   ✅ 合并信息: {result[3]}")
            
            # 清理测试数据
            cursor.execute('DELETE FROM product_details WHERE id = ?', (test_id,))
            conn.commit()
            print("   ✅ 测试数据已清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 恢复备份
        if backup_path and os.path.exists(backup_path):
            print("🔄 正在恢复备份...")
            shutil.copy2(backup_path, db_path)
            print("✅ 已恢复到备份版本")
        
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Novoline爬虫 - 横向阶梯价格结构迁移工具")
    print("=" * 60)
    print("此工具将:")
    print("1. 重新设计product_details表为横向阶梯价格结构")
    print("2. 移除tier_prices表")
    print("3. 支持6个阶梯价格层级")
    print("4. 添加合并的阶梯价格字段")
    print("5. 便于Excel导出和分析")
    print()
    
    db_path = "novoline_spider_v2.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        print("请确保您在正确的目录中运行此脚本")
        return
    
    print(f"📁 找到数据库文件: {db_path}")
    
    # 确认迁移
    confirm = input(f"\n确认迁移到横向阶梯价格结构? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 迁移已取消")
        return
    
    # 执行迁移
    if migrate_to_horizontal_structure(db_path):
        print(f"\n🎉 数据库迁移成功！")
        print("\n📋 迁移总结:")
        print("   - product_details表: 横向存储6个阶梯价格层级")
        print("   - 每个阶梯包含: 数量、价格、折扣三个字段")
        print("   - all_tiers_combined: 合并的完整阶梯价格信息")
        print("   - tier_prices表: 已删除，不再需要")
        print("\n🎯 现在可以运行第三阶段采集，数据将直接存储在一张表中！")
        print("📊 导出Excel时，每个产品的所有信息都在一行中，无需vlookup！")
    else:
        print(f"\n❌ 数据库迁移失败")

if __name__ == "__main__":
    main()
