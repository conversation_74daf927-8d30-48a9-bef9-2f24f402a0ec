#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库重新设计 - 支持第三阶段产品详情采集
"""

import sqlite3
import os
from datetime import datetime

class DatabaseRedesign:
    """数据库重新设计类"""
    
    def __init__(self, db_path: str = "novoline_spider_v2.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 1. 主目录表 (保持不变)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS main_categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category_name TEXT NOT NULL,
                    category_url TEXT NOT NULL UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 2. 子目录表 (保持不变)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sub_categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    main_category_id INTEGER NOT NULL,
                    sub_category_name TEXT NOT NULL,
                    sub_category_url TEXT NOT NULL UNIQUE,
                    product_count INTEGER DEFAULT 0,
                    page_count INTEGER DEFAULT 0,
                    last_crawled_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (main_category_id) REFERENCES main_categories (id)
                )
            ''')
            
            # 3. 产品URL表 (第二阶段数据)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_urls (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sub_category_id INTEGER NOT NULL,
                    product_name TEXT NOT NULL,
                    product_url TEXT NOT NULL UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (sub_category_id) REFERENCES sub_categories (id)
                )
            ''')
            
            # 4. 产品详情表 (第三阶段数据) - 重新设计
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_details (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_url_id INTEGER NOT NULL UNIQUE,
                    
                    -- 基本信息
                    title TEXT,
                    sku TEXT,
                    
                    -- 导航信息 (分级存储)
                    nav_level1 TEXT,  -- 一级导航 (如: Drinkware & Can Coolers)
                    nav_level2 TEXT,  -- 二级导航 (如: Shaker & Blender Bottles)
                    nav_level3 TEXT,  -- 三级导航 (如果有)
                    nav_full TEXT,    -- 完整导航路径
                    
                    -- 分类信息
                    category_name TEXT,
                    category_url TEXT,
                    
                    -- 基础价格
                    regular_price DECIMAL(10,2),
                    sale_price DECIMAL(10,2),
                    currency TEXT DEFAULT 'USD',
                    
                    -- 原始价格数据 (完整保存)
                    price_data_raw TEXT,  -- 原始JSON或分隔符格式
                    
                    -- 采集状态
                    crawl_status TEXT DEFAULT 'pending',  -- pending, success, failed, retrying
                    crawl_attempts INTEGER DEFAULT 0,
                    last_crawl_attempt TIMESTAMP,
                    error_message TEXT,
                    
                    -- 时间戳
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (product_url_id) REFERENCES product_urls (id)
                )
            ''')
            
            # 5. 阶梯价格表 (详细的价格层级)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tier_prices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_detail_id INTEGER NOT NULL,
                    
                    -- 数量层级
                    quantity_min INTEGER NOT NULL,
                    quantity_max INTEGER,  -- NULL表示无上限 (如5000+)
                    quantity_display TEXT,  -- 显示文本 (如: "5,000+ pieces")
                    
                    -- 价格信息
                    unit_price DECIMAL(10,2) NOT NULL,
                    total_price DECIMAL(10,2),
                    currency TEXT DEFAULT 'USD',
                    
                    -- 折扣信息
                    discount_percent INTEGER,  -- 折扣百分比
                    discount_amount DECIMAL(10,2),  -- 折扣金额
                    
                    -- 原始数据
                    raw_data TEXT,  -- 原始HTML或数据
                    
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (product_detail_id) REFERENCES product_details (id)
                )
            ''')
            
            # 6. 采集进度表 (扩展支持第三阶段)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_progress (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stage TEXT NOT NULL,  -- categories, products, details
                    status TEXT NOT NULL, -- not_started, in_progress, completed, failed
                    total_items INTEGER DEFAULT 0,
                    completed_items INTEGER DEFAULT 0,
                    failed_items INTEGER DEFAULT 0,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    error_message TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 7. 错误日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS error_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stage TEXT NOT NULL,
                    product_url TEXT,
                    error_type TEXT,
                    error_message TEXT,
                    stack_trace TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_urls_sub_category ON product_urls(sub_category_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_details_url_id ON product_details(product_url_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_details_status ON product_details(crawl_status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tier_prices_product ON tier_prices(product_detail_id)')
            
            conn.commit()
            print("✅ 数据库结构创建完成")
    
    def migrate_existing_data(self, old_db_path: str):
        """从旧数据库迁移数据"""
        if not os.path.exists(old_db_path):
            print(f"❌ 旧数据库不存在: {old_db_path}")
            return
        
        print(f"🔄 开始迁移数据从 {old_db_path}")
        
        # 连接旧数据库
        old_conn = sqlite3.connect(old_db_path)
        old_cursor = old_conn.cursor()
        
        with sqlite3.connect(self.db_path) as new_conn:
            new_cursor = new_conn.cursor()
            
            # 迁移主目录 - 只迁移核心字段
            old_cursor.execute('SELECT id, category_name, category_url FROM main_categories')
            main_cats = old_cursor.fetchall()
            for row in main_cats:
                new_cursor.execute('''
                    INSERT OR REPLACE INTO main_categories
                    (id, category_name, category_url, created_at, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ''', row)
            print(f"✅ 迁移主目录: {len(main_cats)} 条")

            # 迁移子目录 - 只迁移核心字段
            old_cursor.execute('''
                SELECT id, main_category_id, sub_category_name, sub_category_url,
                       COALESCE(product_count, 0), COALESCE(page_count, 0), last_crawled_at
                FROM sub_categories
            ''')
            sub_cats = old_cursor.fetchall()
            for row in sub_cats:
                new_cursor.execute('''
                    INSERT OR REPLACE INTO sub_categories
                    (id, main_category_id, sub_category_name, sub_category_url,
                     product_count, page_count, last_crawled_at, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ''', row)
            print(f"✅ 迁移子目录: {len(sub_cats)} 条")
            
            # 迁移产品URL (只保留基本信息)
            old_cursor.execute('''
                SELECT sub_category_id, product_name, product_url
                FROM product_urls
            ''')
            products = old_cursor.fetchall()
            for sub_cat_id, name, url in products:
                new_cursor.execute('''
                    INSERT OR REPLACE INTO product_urls
                    (sub_category_id, product_name, product_url, created_at, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ''', (sub_cat_id, name, url))
            print(f"✅ 迁移产品URL: {len(products)} 条")
            
            new_conn.commit()
        
        old_conn.close()
        print("🎉 数据迁移完成")
    
    def get_statistics(self):
        """获取统计信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            stats = {}
            
            # 基本统计
            cursor.execute('SELECT COUNT(*) FROM main_categories')
            stats['main_categories'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM sub_categories')
            stats['sub_categories'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM product_urls')
            stats['product_urls'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM product_details')
            stats['product_details'] = cursor.fetchone()[0]
            
            # 第三阶段状态统计
            cursor.execute('''
                SELECT crawl_status, COUNT(*) 
                FROM product_details 
                GROUP BY crawl_status
            ''')
            status_stats = dict(cursor.fetchall())
            stats['detail_status'] = status_stats
            
            # 待采集产品数
            cursor.execute('''
                SELECT COUNT(*) 
                FROM product_urls pu
                LEFT JOIN product_details pd ON pu.id = pd.product_url_id
                WHERE pd.id IS NULL
            ''')
            stats['pending_details'] = cursor.fetchone()[0]
            
            return stats

def main():
    """主函数"""
    print("🎯 数据库重新设计 - 支持第三阶段产品详情采集")
    print("=" * 60)
    
    # 创建新数据库
    db = DatabaseRedesign("novoline_spider_v2.db")
    
    # 迁移现有数据
    old_db_files = [
        "novoline_spider.db",
        "demo/demo_stage1.db",
        "demo/demo_stage2.db"
    ]
    
    for old_db in old_db_files:
        if os.path.exists(old_db):
            print(f"\n发现旧数据库: {old_db}")
            confirm = input(f"是否迁移数据? (y/N): ").strip().lower()
            if confirm == 'y':
                db.migrate_existing_data(old_db)
            break
    
    # 显示统计信息
    print(f"\n📊 数据库统计:")
    stats = db.get_statistics()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    print(f"\n🎉 数据库重新设计完成!")
    print(f"   新数据库: novoline_spider_v2.db")
    print(f"   支持完整的第三阶段产品详情采集")

if __name__ == "__main__":
    main()
