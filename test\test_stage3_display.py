#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第三阶段数据显示
验证数据库字段是否正确显示
"""

import sqlite3
import os

def test_database_content():
    """测试数据库内容"""
    print("🗄️ 数据库内容测试")
    print("=" * 60)
    
    if not os.path.exists("novoline_spider_v2.db"):
        print("❌ 新数据库不存在")
        return
    
    with sqlite3.connect("novoline_spider_v2.db") as conn:
        cursor = conn.cursor()
        
        # 检查product_details表
        print("📊 product_details 表结构:")
        cursor.execute("PRAGMA table_info(product_details)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM product_details")
        count = cursor.fetchone()[0]
        print(f"\n📈 product_details 数据量: {count}")
        
        if count > 0:
            print("\n📋 product_details 样本数据:")
            cursor.execute('''
                SELECT id, product_url_id, title, sku, nav_level1, nav_level2, nav_level3,
                       nav_full, category_name, regular_price, crawl_status
                FROM product_details 
                LIMIT 3
            ''')
            rows = cursor.fetchall()
            
            for i, row in enumerate(rows, 1):
                print(f"\n   样本 {i}:")
                print(f"     ID: {row[0]}")
                print(f"     产品URL ID: {row[1]}")
                print(f"     标题: {row[2]}")
                print(f"     SKU: {row[3]}")
                print(f"     导航1: {row[4]}")
                print(f"     导航2: {row[5]}")
                print(f"     导航3: {row[6]}")
                print(f"     完整导航: {row[7]}")
                print(f"     分类: {row[8]}")
                print(f"     价格: {row[9]}")
                print(f"     状态: {row[10]}")
        
        # 检查tier_prices表
        print(f"\n" + "=" * 60)
        print("📊 tier_prices 表结构:")
        cursor.execute("PRAGMA table_info(tier_prices)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM tier_prices")
        count = cursor.fetchone()[0]
        print(f"\n📈 tier_prices 数据量: {count}")
        
        if count > 0:
            print("\n📋 tier_prices 样本数据:")
            cursor.execute('''
                SELECT id, product_detail_id, quantity_min, quantity_max, 
                       quantity_display, unit_price, discount_percent
                FROM tier_prices 
                LIMIT 5
            ''')
            rows = cursor.fetchall()
            
            for i, row in enumerate(rows, 1):
                print(f"   {i}. ID:{row[0]} | 产品详情ID:{row[1]} | 数量:{row[2]}-{row[3]} | 显示:{row[4]} | 价格:${row[5]} | 折扣:{row[6]}%")

def test_gui_display_fields():
    """测试GUI应该显示的字段"""
    print(f"\n🖥️ GUI字段显示测试")
    print("=" * 60)
    
    print("📋 第三阶段Tab应该显示的字段:")
    
    print("\n1️⃣ 产品详情Tab (product_details):")
    expected_fields = [
        "id", "product_url_id", "title", "sku",
        "nav_level1", "nav_level2", "nav_level3", "nav_full",
        "category_name", "category_url",
        "regular_price", "sale_price", "currency",
        "price_data_raw", "crawl_status", "crawl_attempts", "error_message"
    ]
    
    for field in expected_fields:
        print(f"   ✅ {field}")
    
    print("\n2️⃣ 阶梯价格Tab (tier_prices):")
    tier_fields = [
        "id", "product_detail_id", "quantity_min", "quantity_max", 
        "quantity_display", "unit_price", "discount_percent", "raw_data"
    ]
    
    for field in tier_fields:
        print(f"   ✅ {field}")
    
    print("\n📝 特殊字段说明:")
    print("   🔸 price_data_raw: 原始价格数据 (|和#分隔符格式)")
    print("   🔸 nav_level1/2/3: 分级导航信息")
    print("   🔸 nav_full: 完整导航路径")
    print("   🔸 quantity_display: 数量显示文本")
    print("   🔸 raw_data: 原始HTML数据")

def test_expected_vs_actual():
    """对比期望与实际的字段"""
    print(f"\n🔍 期望vs实际对比")
    print("=" * 60)
    
    if not os.path.exists("novoline_spider_v2.db"):
        print("❌ 数据库不存在")
        return
    
    with sqlite3.connect("novoline_spider_v2.db") as conn:
        cursor = conn.cursor()
        
        # 检查product_details实际字段
        cursor.execute("PRAGMA table_info(product_details)")
        actual_fields = [col[1] for col in cursor.fetchall()]
        
        expected_fields = [
            "id", "product_url_id", "title", "sku",
            "nav_level1", "nav_level2", "nav_level3", "nav_full",
            "category_name", "category_url",
            "regular_price", "sale_price", "currency",
            "price_data_raw", "crawl_status", "crawl_attempts", 
            "last_crawl_attempt", "error_message", "created_at", "updated_at"
        ]
        
        print("📊 product_details 字段对比:")
        for field in expected_fields:
            if field in actual_fields:
                print(f"   ✅ {field}")
            else:
                print(f"   ❌ {field} (缺失)")
        
        # 检查是否有额外字段
        extra_fields = set(actual_fields) - set(expected_fields)
        if extra_fields:
            print(f"\n🔸 额外字段: {', '.join(extra_fields)}")

def main():
    """主函数"""
    print("🧪 第三阶段数据显示测试")
    print("验证GUI是否正确显示数据库字段")
    
    test_database_content()
    test_gui_display_fields()
    test_expected_vs_actual()
    
    print(f"\n💡 GUI测试建议:")
    print("1. 启动GUI: python gui.py")
    print("2. 切换到'第三步：阶梯价格'Tab")
    print("3. 查看'产品详情'和'阶梯价格'两个子Tab")
    print("4. 验证字段是否与上述列表一致")
    print("5. 检查是否有横向滚动条")
    print("6. 在'数据查看'Tab中选择'product_details'查看详情")

if __name__ == "__main__":
    main()
