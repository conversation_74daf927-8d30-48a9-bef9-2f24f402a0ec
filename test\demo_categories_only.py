#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仅演示第一阶段：目录结构爬取
"""

import os
from database import DatabaseManager
from html_parser import NovolineParser

def main():
    """主演示函数"""
    print("=" * 60)
    print("Novoline网站爬虫 - 目录结构解析演示")
    print("=" * 60)
    
    # 检查1.txt文件
    if not os.path.exists("1.txt"):
        print("❌ 错误：未找到1.txt文件")
        return
    
    # 创建数据库和解析器
    db = DatabaseManager("categories_demo.db")
    parser = NovolineParser()
    
    print("📋 开始解析目录结构...")
    
    # 解析目录结构
    categories = parser.parse_categories_from_file("1.txt")
    
    if not categories:
        print("❌ 解析失败")
        return
    
    print(f"✅ 成功解析出 {len(categories)} 个主目录")
    
    # 存储到数据库
    print("💾 正在存储到数据库...")
    
    total_main_cats = 0
    total_sub_cats = 0
    
    for category_data in categories:
        main_cat = category_data['main_category']
        
        # 插入主目录
        main_cat_id = db.insert_main_category(
            main_cat['name'],
            main_cat['url'],
            main_cat.get('icon_url', '')
        )
        total_main_cats += 1
        
        print(f"📁 {main_cat['name']} ({len(category_data['sub_categories'])} 个子目录)")
        
        # 插入子目录
        for sub_cat in category_data['sub_categories']:
            db.insert_sub_category(
                main_cat_id,
                sub_cat['name'],
                sub_cat['url']
            )
            total_sub_cats += 1
            print(f"   └─ {sub_cat['name']}")
    
    print("\n" + "=" * 60)
    print("📊 存储完成统计")
    print("=" * 60)
    
    stats = db.get_statistics()
    print(f"✅ 主目录数量: {stats['main_categories']}")
    print(f"✅ 子目录数量: {stats['sub_categories']}")
    
    print(f"\n💾 数据已保存到: categories_demo.db")
    
    # 显示详细统计
    print("\n" + "=" * 60)
    print("📈 详细统计信息")
    print("=" * 60)
    
    import sqlite3
    with sqlite3.connect("categories_demo.db") as conn:
        cursor = conn.cursor()
        
        # 显示各主目录的子目录数量
        cursor.execute("""
            SELECT mc.category_name, COUNT(sc.id) as sub_count
            FROM main_categories mc
            LEFT JOIN sub_categories sc ON mc.id = sc.main_category_id
            GROUP BY mc.id, mc.category_name
            ORDER BY sub_count DESC
        """)
        sub_stats = cursor.fetchall()
        
        print("📊 各主目录的子目录数量:")
        for main_name, sub_count in sub_stats:
            print(f"   • {main_name}: {sub_count} 个子目录")
        
        # 显示一些示例子目录
        print("\n🔍 部分子目录示例:")
        cursor.execute("""
            SELECT mc.category_name, sc.sub_category_name, sc.sub_category_url
            FROM main_categories mc
            JOIN sub_categories sc ON mc.id = sc.main_category_id
            LIMIT 10
        """)
        examples = cursor.fetchall()
        
        for main_name, sub_name, sub_url in examples:
            print(f"   • {main_name} > {sub_name}")
            print(f"     {sub_url}")
    
    print("\n🎯 目录结构解析演示完成！")
    print("💡 提示：运行 'python main.py' 可启动完整的图形界面爬虫")

if __name__ == "__main__":
    main()
