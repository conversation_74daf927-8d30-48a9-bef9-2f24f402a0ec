
# 在stage4_image_crawler.py中替换_extract_image_urls方法

def _extract_image_urls(self, soup: BeautifulSoup, base_url: str) -> List[str]:
    """
    提取产品图片URL（增强版本）
    
    Args:
        soup: BeautifulSoup对象
        base_url: 基础URL
        
    Returns:
        图片URL列表
    """
    image_urls = []
    
    try:
        # 增强的图片选择器（基于实际HTML结构分析）
        image_selectors = [
            # 标准WooCommerce选择器
            '.woocommerce-product-gallery img',
            '.product-images img',
            '.product-gallery img',
            '.wp-post-image',
            '.attachment-woocommerce_single',
            
            # 新增的选择器（针对Novoline网站的特殊结构）
            '.images img',  # 主图片容器
            '.nickx_product_images_with_video img',  # 特定的图片视频容器
            '.nickx-slider img',  # 滑动器中的图片
            '.nswiper-slide img',  # Swiper滑动项中的图片
            '.nswiper-wrapper img',  # Swiper包装器中的图片
            'div[class*="slider"] img',  # 任何包含slider的div中的图片
            'div[class*="swiper"] img',  # 任何包含swiper的div中的图片
        ]
        
        found_images = set()  # 使用set避免重复
        
        for selector in image_selectors:
            images = soup.select(selector)
            for img in images:
                # 尝试多种src属性
                src_attrs = ['src', 'data-src', 'data-lazy-src', 'data-zoom-image', 'data-o_src']
                
                for attr in src_attrs:
                    img_url = img.get(attr)
                    if img_url:
                        # 转换为绝对URL
                        full_url = urljoin(base_url, img_url)
                        
                        # 过滤掉明显不是产品图片的URL
                        if self._is_valid_product_image(full_url):
                            found_images.add(full_url)
                        break  # 找到一个有效URL就跳出
        
        # 如果通过标准选择器没找到图片，尝试通过产品信息匹配
        if not found_images:
            # 通过alt属性或src路径匹配产品相关图片
            all_imgs = soup.find_all('img')
            for img in all_imgs:
                alt = img.get('alt', '').lower()
                src = img.get('src', '').lower()
                
                # 检查是否包含产品相关关键词
                if any(keyword in alt or keyword in src for keyword in ['product', 'novo', 'creative', 'outdoor']):
                    for attr in ['src', 'data-src', 'data-lazy-src', 'data-zoom-image']:
                        img_url = img.get(attr)
                        if img_url:
                            full_url = urljoin(base_url, img_url)
                            if self._is_valid_product_image(full_url):
                                found_images.add(full_url)
                            break
        
        # 转换为列表并限制数量
        image_urls = list(found_images)[:10]  # 最多10张图片
        
    except Exception as e:
        self._notify_error(f"提取图片URL失败: {e}")
    
    return image_urls
