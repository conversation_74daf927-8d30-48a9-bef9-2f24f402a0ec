#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
迁移旧数据库的数据到新数据库
"""

import os
import sqlite3
import shutil
from datetime import datetime

def backup_new_database():
    """备份新数据库"""
    new_db = "novoline_spider_v2.db"
    if os.path.exists(new_db):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"novoline_spider_v2_backup_{timestamp}.db"
        shutil.copy2(new_db, backup_path)
        print(f"✅ 新数据库已备份到: {backup_path}")
        return backup_path
    return None

def find_old_database():
    """查找旧数据库文件"""
    possible_paths = [
        "novoline_spider.db",
        "demo/demo_stage1.db",
        "demo/demo_stage2.db",
        "../demo/demo_stage1.db",
        "../demo/demo_stage2.db"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"📁 找到旧数据库: {path}")
            return path
    
    return None

def migrate_data(old_db_path, new_db_path):
    """迁移数据"""
    print(f"🔄 开始迁移数据...")
    print(f"   源数据库: {old_db_path}")
    print(f"   目标数据库: {new_db_path}")
    
    try:
        # 连接两个数据库
        old_conn = sqlite3.connect(old_db_path)
        new_conn = sqlite3.connect(new_db_path)
        
        old_cursor = old_conn.cursor()
        new_cursor = new_conn.cursor()
        
        # 1. 迁移主目录数据
        print("📋 迁移主目录数据...")
        old_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='main_categories'")
        if old_cursor.fetchone():
            old_cursor.execute("SELECT category_name, category_url FROM main_categories")
            main_categories = old_cursor.fetchall()
            
            for category_name, category_url in main_categories:
                new_cursor.execute('''
                    INSERT OR IGNORE INTO main_categories (category_name, category_url)
                    VALUES (?, ?)
                ''', (category_name, category_url))
            
            print(f"   ✅ 迁移了 {len(main_categories)} 个主目录")
        
        # 2. 迁移子目录数据
        print("📋 迁移子目录数据...")
        old_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sub_categories'")
        if old_cursor.fetchone():
            old_cursor.execute('''
                SELECT sc.sub_category_name, sc.sub_category_url, mc.category_name
                FROM sub_categories sc
                LEFT JOIN main_categories mc ON sc.main_category_id = mc.id
            ''')
            sub_categories = old_cursor.fetchall()
            
            for sub_name, sub_url, main_name in sub_categories:
                # 获取主目录ID
                new_cursor.execute('SELECT id FROM main_categories WHERE category_name = ?', (main_name,))
                main_result = new_cursor.fetchone()
                if main_result:
                    main_id = main_result[0]
                    new_cursor.execute('''
                        INSERT OR IGNORE INTO sub_categories (main_category_id, sub_category_name, sub_category_url)
                        VALUES (?, ?, ?)
                    ''', (main_id, sub_name, sub_url))
            
            print(f"   ✅ 迁移了 {len(sub_categories)} 个子目录")
        
        # 3. 迁移产品URL数据
        print("📋 迁移产品URL数据...")
        old_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='product_urls'")
        if old_cursor.fetchone():
            old_cursor.execute('''
                SELECT pu.product_name, pu.product_url, sc.sub_category_name
                FROM product_urls pu
                LEFT JOIN sub_categories sc ON pu.sub_category_id = sc.id
            ''')
            product_urls = old_cursor.fetchall()
            
            for product_name, product_url, sub_name in product_urls:
                # 获取子目录ID
                new_cursor.execute('SELECT id FROM sub_categories WHERE sub_category_name = ?', (sub_name,))
                sub_result = new_cursor.fetchone()
                if sub_result:
                    sub_id = sub_result[0]
                    new_cursor.execute('''
                        INSERT OR IGNORE INTO product_urls (sub_category_id, product_name, product_url)
                        VALUES (?, ?, ?)
                    ''', (sub_id, product_name, product_url))
            
            print(f"   ✅ 迁移了 {len(product_urls)} 个产品URL")
        
        # 提交更改
        new_conn.commit()
        
        # 关闭连接
        old_conn.close()
        new_conn.close()
        
        print("✅ 数据迁移完成！")
        return True
        
    except Exception as e:
        print(f"❌ 数据迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_migration(new_db_path):
    """验证迁移结果"""
    print("\n🔍 验证迁移结果...")
    
    try:
        with sqlite3.connect(new_db_path) as conn:
            cursor = conn.cursor()
            
            # 检查各表的数据量
            cursor.execute("SELECT COUNT(*) FROM main_categories")
            main_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM sub_categories")
            sub_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM product_urls")
            product_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM product_details")
            detail_count = cursor.fetchone()[0]
            
            print(f"📊 迁移后的数据统计:")
            print(f"   主目录: {main_count} 个")
            print(f"   子目录: {sub_count} 个")
            print(f"   产品URL: {product_count} 个")
            print(f"   产品详情: {detail_count} 个")
            
            if main_count > 0 and sub_count > 0 and product_count > 0:
                print("✅ 数据迁移验证成功！")
                return True
            else:
                print("⚠️ 部分数据可能未迁移成功")
                return False
                
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Novoline爬虫 - 数据迁移工具")
    print("=" * 60)
    print("此工具将旧数据库的数据迁移到新的横向阶梯价格数据库中")
    print()
    
    # 检查新数据库
    new_db = "novoline_spider_v2.db"
    if not os.path.exists(new_db):
        print(f"❌ 新数据库不存在: {new_db}")
        print("请先运行程序创建新数据库")
        return
    
    # 备份新数据库
    backup_path = backup_new_database()
    
    # 查找旧数据库
    old_db = find_old_database()
    if not old_db:
        print("❌ 未找到旧数据库文件")
        print("请确保以下文件之一存在:")
        print("   - novoline_spider.db")
        print("   - demo/demo_stage1.db")
        print("   - demo/demo_stage2.db")
        return
    
    # 确认迁移
    print(f"\n准备迁移数据:")
    print(f"   源: {old_db}")
    print(f"   目标: {new_db}")
    
    confirm = input(f"\n确认开始迁移? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 迁移已取消")
        return
    
    # 执行迁移
    if migrate_data(old_db, new_db):
        # 验证迁移
        if verify_migration(new_db):
            print(f"\n🎉 数据迁移成功完成！")
            print(f"   现在可以正常使用第一阶段和第二阶段的数据了")
            print(f"   第三阶段可以继续采集产品详情信息")
        else:
            print(f"\n⚠️ 迁移可能不完整，请检查数据")
    else:
        print(f"\n❌ 数据迁移失败")
        if backup_path:
            print(f"   可以从备份恢复: {backup_path}")

if __name__ == "__main__":
    main()
