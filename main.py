#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Novoline网站爬虫主程序
分阶段爬取网站内容：目录结构、产品URL、阶梯价格
"""

import sys
import os
from gui import SpiderGUI

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'requests',
        'beautifulsoup4',
        'lxml'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'beautifulsoup4':
                import bs4
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包，请先安装：")
        for package in missing_packages:
            print(f"  pip install {package}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("Novoline网站爬虫")
    print("=" * 50)
    print("功能说明：")
    print("1. 第一阶段：爬取主目录结构信息")
    print("2. 第二阶段：爬取每个目录下的产品URL")
    print("3. 第三阶段：爬取产品详细页面的阶梯价格信息")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 检查1.txt文件是否存在
    if not os.path.exists("1.txt"):
        print("错误：未找到1.txt文件，请确保该文件存在于当前目录")
        input("按回车键退出...")
        return
    
    try:
        # 启动GUI
        app = SpiderGUI()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
