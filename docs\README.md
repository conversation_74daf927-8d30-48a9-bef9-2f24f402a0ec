# Novoline网站爬虫项目

## 📁 项目结构

```
spider-flow/
├── main.py                 # 主程序入口
├── gui.py                  # 图形用户界面
├── spider_core.py          # 爬虫核心逻辑
├── database.py             # 数据库管理
├── html_parser.py          # HTML解析器
├── migrate_database.py     # 数据库迁移工具
├── view_stats.py           # 数据统计查看
├── test/                   # 测试文件目录
│   ├── test_stage2_corrected.py    # 修正版第二阶段测试
│   ├── check_crawl_status.py       # 采集状态检查
│   └── ...
├── demo/                   # 演示文件目录
│   ├── demo_stage1.db              # 第一阶段演示数据
│   ├── demo_stage2.py              # 第二阶段演示
│   └── ...
├── backup/                 # 备份文件目录
└── docs/                   # 文档文件目录
    ├── 第二阶段修正总结.md         # 阶段职责修正说明
    ├── GUI改进总结.md              # GUI功能改进
    └── ...
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r docs/requirements.txt
```

### 2. 启动GUI
```bash
python gui.py
```

### 3. 命令行使用
```bash
python main.py
```

## 📋 阶段功能说明

### 第一阶段：分类URL收集 ✅
- **目标**: 建立网站的分类层级结构
- **数据**: 主目录、子目录的名称和URL
- **不访问**: 产品页面

### 第二阶段：产品URL收集 ✅ (已修正)
- **目标**: 在分类页面翻页，收集所有产品URL
- **数据**: 产品名称、产品URL
- **不访问**: 产品详情页 (重要修正)
- **不收集**: SKU、价格等详细信息

### 第三阶段：产品详情采集 🚧
- **目标**: 访问产品详情页收集完整信息
- **数据**: SKU、价格、规格、图片、详细描述
- **访问**: 每个产品的详情页面

## 🎯 核心特性

### GUI界面增强
- ✅ 分类表格显示产品数量统计
- ✅ 产品表格显示SKU和价格信息
- ✅ 实时数据刷新，无需重启
- ✅ 暂停/继续功能
- ✅ 右键菜单重新采集

### 断点续采机制
- ✅ 精确记录每个分类的采集状态
- ✅ 自动跳过已完成的分类
- ✅ 从中断处准确继续采集

### 单独重新采集
- ✅ 支持单独重新采集子目录
- ✅ 支持重新采集整个主目录
- ✅ 自动检测数量不一致

## 🔧 工具说明

### 核心工具
- `view_stats.py` - 查看数据统计
- `migrate_database.py` - 数据库结构升级

### 测试工具
- `test/test_stage2_corrected.py` - 修正版第二阶段测试
- `test/check_crawl_status.py` - 采集状态检查

### 演示工具
- `demo/demo_stage2.py` - 第二阶段功能演示

## 📊 数据库

项目使用SQLite数据库存储数据：
- `main_categories` - 主目录表
- `sub_categories` - 子目录表 (含产品统计)
- `product_urls` - 产品URL表 (第二阶段填充基本信息，第三阶段填充详细信息)
- `tier_prices` - 阶梯价格表

## 🧪 测试验证

### 测试修正版第二阶段
```bash
python test/test_stage2_corrected.py
```

### 检查采集状态
```bash
python test/check_crawl_status.py
```

## 📝 重要修正

### 阶段职责重新梳理
- **修正前**: 第二阶段收集产品URL + SKU + 价格 (错误)
- **修正后**: 第二阶段只收集产品URL + 名称 (正确)
- **说明**: SKU、价格等详细信息在第三阶段访问产品详情页时收集

详见: `docs/第二阶段修正总结.md`
