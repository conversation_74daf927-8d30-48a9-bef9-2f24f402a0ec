#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建步骤四的数据库表结构
"""

import sqlite3

def create_stage4_table():
    """创建步骤四的产品图片表"""
    print("🔧 创建步骤四数据库表...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 创建产品图片表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_images (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_url_id INTEGER NOT NULL,
                    product_url TEXT NOT NULL,
                    product_title TEXT,
                    product_sku TEXT,
                    description TEXT,
                    additional_information TEXT,
                    breadcrumb_path TEXT,
                    local_directory TEXT,
                    
                    -- 图片URL和本地路径（10张图片）
                    image1_url TEXT,
                    image1_local TEXT,
                    image2_url TEXT,
                    image2_local TEXT,
                    image3_url TEXT,
                    image3_local TEXT,
                    image4_url TEXT,
                    image4_local TEXT,
                    image5_url TEXT,
                    image5_local TEXT,
                    image6_url TEXT,
                    image6_local TEXT,
                    image7_url TEXT,
                    image7_local TEXT,
                    image8_url TEXT,
                    image8_local TEXT,
                    image9_url TEXT,
                    image9_local TEXT,
                    image10_url TEXT,
                    image10_local TEXT,
                    
                    -- 采集状态
                    crawl_status TEXT DEFAULT 'pending',  -- pending, success, failed
                    crawl_attempts INTEGER DEFAULT 0,
                    error_message TEXT,
                    images_downloaded INTEGER DEFAULT 0,
                    last_crawl_attempt TIMESTAMP,
                    
                    -- 时间戳
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    
                    -- 外键约束
                    FOREIGN KEY (product_url_id) REFERENCES product_urls(id),
                    
                    -- 唯一约束
                    UNIQUE(product_url_id)
                )
            ''')
            
            # 创建索引
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_product_images_url_id 
                ON product_images(product_url_id)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_product_images_status 
                ON product_images(crawl_status)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_product_images_sku 
                ON product_images(product_sku)
            ''')
            
            conn.commit()
            print("✅ 产品图片表创建成功")
            
            # 检查表结构
            cursor.execute('PRAGMA table_info(product_images)')
            columns = cursor.fetchall()
            print(f"📋 表结构包含 {len(columns)} 个字段:")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
            
            return True
            
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

def check_existing_data():
    """检查现有数据，为步骤四做准备"""
    print(f"\n📊 检查现有数据...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 检查product_details数据
            cursor.execute('''
                SELECT COUNT(*) FROM product_details 
                WHERE crawl_status = 'success'
            ''')
            success_details = cursor.fetchone()[0]
            
            # 检查有SKU的产品数量
            cursor.execute('''
                SELECT COUNT(*) FROM product_details 
                WHERE crawl_status = 'success' AND sku IS NOT NULL AND sku != ''
            ''')
            with_sku = cursor.fetchone()[0]
            
            # 检查有标题的产品数量
            cursor.execute('''
                SELECT COUNT(*) FROM product_details 
                WHERE crawl_status = 'success' AND title IS NOT NULL AND title != ''
            ''')
            with_title = cursor.fetchone()[0]
            
            print(f"   成功采集的产品详情: {success_details:,}")
            print(f"   有SKU的产品: {with_sku:,}")
            print(f"   有标题的产品: {with_title:,}")
            
            # 显示示例数据
            cursor.execute('''
                SELECT pu.product_url, pd.title, pd.sku
                FROM product_details pd
                JOIN product_urls pu ON pd.product_url_id = pu.id
                WHERE pd.crawl_status = 'success' 
                  AND pd.sku IS NOT NULL 
                  AND pd.title IS NOT NULL
                LIMIT 5
            ''')
            
            samples = cursor.fetchall()
            if samples:
                print(f"\n📋 示例数据:")
                for url, title, sku in samples:
                    print(f"   SKU: {sku}")
                    print(f"   标题: {title}")
                    print(f"   URL: {url}")
                    print()
            
            return success_details > 0
            
    except Exception as e:
        print(f"❌ 检查数据失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 步骤四：产品图片采集 - 数据库初始化")
    print("=" * 60)
    print("为产品图片采集创建数据库表结构")
    print()
    
    # 创建表结构
    if create_stage4_table():
        print("\n✅ 数据库表创建成功")
    else:
        print("\n❌ 数据库表创建失败")
        return
    
    # 检查现有数据
    if check_existing_data():
        print("\n✅ 现有数据检查完成，可以开始步骤四")
        print("\n🚀 下一步:")
        print("   1. 运行图片采集程序")
        print("   2. 选择匹配条件（SKU/标题/URL）")
        print("   3. 开始多线程图片采集")
    else:
        print("\n⚠️ 没有足够的产品详情数据")
        print("   请先完成步骤三的产品详情采集")

if __name__ == "__main__":
    main()
