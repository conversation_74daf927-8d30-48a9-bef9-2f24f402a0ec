#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的GUI界面
验证第二阶段功能的GUI显示
"""

import os
import sys
import shutil
from gui import SpiderGUI

def prepare_test_data():
    """准备测试数据"""
    print("准备测试数据...")
    
    # 检查是否有第一阶段数据
    if os.path.exists("demo_stage1.db"):
        # 复制到测试数据库
        test_db = "test_gui.db"
        if os.path.exists(test_db):
            os.remove(test_db)
        shutil.copy2("demo_stage1.db", test_db)
        print(f"✅ 测试数据已准备: {test_db}")
        return test_db
    elif os.path.exists("test/demo_stage1.db"):
        # 从test目录复制
        test_db = "test_gui.db"
        if os.path.exists(test_db):
            os.remove(test_db)
        shutil.copy2("test/demo_stage1.db", test_db)
        print(f"✅ 测试数据已准备: {test_db}")
        return test_db
    else:
        print("❌ 未找到第一阶段数据，请先运行第一阶段")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("改进的GUI界面测试")
    print("=" * 60)
    
    # 准备测试数据
    test_db = prepare_test_data()
    if not test_db:
        return
    
    print("\n🚀 启动改进的GUI界面...")
    print("\n📋 新功能特点:")
    print("   ✅ 分类表格显示产品数量统计")
    print("   ✅ 产品表格显示SKU和价格信息")
    print("   ✅ 第二阶段支持暂停/继续功能")
    print("   ✅ 实时刷新数据显示")
    print("   ✅ 详细的采集进度信息")
    
    print("\n🔍 测试建议:")
    print("   1. 查看分类Tab，观察产品数量显示")
    print("   2. 启动第二阶段，观察实时数据更新")
    print("   3. 测试暂停/继续功能")
    print("   4. 查看产品Tab的新列信息")
    
    print(f"\n📁 使用测试数据库: {test_db}")
    print("   (关闭GUI后会保留此文件供查看)")
    
    # 启动GUI
    try:
        # 临时修改数据库路径
        import database
        original_db_path = database.DatabaseManager().db_path
        
        # 创建GUI实例并设置测试数据库
        app = SpiderGUI()
        app.spider.db.db_path = test_db
        app.db.db_path = test_db
        
        print("\n✨ GUI已启动，请在界面中测试新功能！")
        app.run()
        
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n📊 测试完成，数据库文件保留: {test_db}")
    print("   可以使用 python view_stats.py 查看数据统计")

if __name__ == "__main__":
    main()
