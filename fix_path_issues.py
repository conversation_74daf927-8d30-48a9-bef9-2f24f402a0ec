#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复路径创建问题
"""

import os
import re
import sqlite3

def test_problematic_paths():
    """测试有问题的路径"""
    print("🧪 测试有问题的路径...")
    
    def clean_path_part(part: str) -> str:
        """清理路径部分"""
        if not part:
            return ""
        
        # 移除所有可能的问题字符
        cleaned = re.sub(r'[<>:"/\\|?*\xa0\u00a0\u2000-\u200f\u2028-\u202f\ufeff]', '', part)
        
        # 移除多余的空格
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        # 移除首尾的点、空格和特殊字符
        cleaned = cleaned.strip('. \t\n\r')
        
        # 移除括号内容
        cleaned = re.sub(r'\([^)]*\)', '', cleaned).strip()
        
        # 移除常见的后缀词
        cleaned = re.sub(r'\s+(with|w/|w)\s*$', '', cleaned, flags=re.IGNORECASE).strip()
        
        # 限制长度
        if len(cleaned) > 20:
            cleaned = cleaned[:20].strip()
        
        return cleaned
    
    def create_safe_directory(breadcrumb_path: str, product_title: str, images_root: str = "images") -> str:
        """创建安全的目录路径"""
        try:
            # 基础路径
            path_parts = [images_root]
            
            # 处理面包屑路径
            if breadcrumb_path:
                breadcrumb_parts = breadcrumb_path.split(' / ')
                # 只取前2级分类，避免路径过深
                for part in breadcrumb_parts[:2]:
                    cleaned_part = clean_path_part(part)
                    if cleaned_part and cleaned_part not in path_parts:
                        path_parts.append(cleaned_part)
            
            # 处理产品标题
            if product_title:
                cleaned_title = clean_path_part(product_title)
                # 避免重复
                if cleaned_title and cleaned_title not in path_parts:
                    path_parts.append(cleaned_title)
            
            # 如果路径太少，添加默认目录
            if len(path_parts) < 2:
                path_parts.append("products")
            
            # 构建路径
            full_path = os.path.join(*path_parts)
            
            # 检查路径长度（Windows限制260字符）
            if len(full_path) > 200:
                # 使用简化路径
                simplified_parts = [images_root]
                if len(path_parts) > 1:
                    simplified_parts.append(path_parts[1][:15])  # 主分类
                if len(path_parts) > 2:
                    simplified_parts.append(path_parts[-1][:20])  # 产品名
                else:
                    simplified_parts.append("item")
                
                full_path = os.path.join(*simplified_parts)
            
            # 创建目录
            os.makedirs(full_path, exist_ok=True)
            
            return full_path
            
        except Exception as e:
            print(f"路径创建失败: {e}")
            # 返回最简单的路径
            fallback_path = os.path.join(images_root, "fallback")
            os.makedirs(fallback_path, exist_ok=True)
            return fallback_path
    
    # 从错误日志中提取的问题路径
    test_cases = [
        {
            "breadcrumb": "Drinkware & Can Coolers / Barware",
            "product": "Circle Shaped Stainless Steel Hip Flask ",
            "expected_issue": "重复产品名称"
        },
        {
            "breadcrumb": "Drinkware & Can Coolers / Bottle Sleeves",
            "product": "Laserable Magnetic Leatherette Beverage ",
            "expected_issue": "产品名称截断"
        },
        {
            "breadcrumb": "Drinkware & Can Coolers / Bottle Sleeves",
            "product": "Neoprene Can Cooler One Color Imprint W ",
            "expected_issue": "以W结尾"
        },
        {
            "breadcrumb": "Drinkware & Can Coolers / Bottles",
            "product": "Stainless Steel Tumbler with Flip Straw ",
            "expected_issue": "包含with"
        },
        {
            "breadcrumb": "Drinkware & Can Coolers / Bottles",
            "product": "Creative Outdoor Silicone Folding Water ",
            "expected_issue": "长产品名称"
        }
    ]
    
    print(f"\n📋 测试结果:")
    for i, case in enumerate(test_cases, 1):
        print(f"\n   测试 {i}: {case['expected_issue']}")
        print(f"      面包屑: {case['breadcrumb']}")
        print(f"      产品名: '{case['product']}'")
        
        try:
            safe_path = create_safe_directory(case['breadcrumb'], case['product'], "test_images")
            print(f"      结果路径: {safe_path}")
            print(f"      路径长度: {len(safe_path)}")
            
            if os.path.exists(safe_path):
                print(f"      ✅ 目录创建成功")
            else:
                print(f"      ❌ 目录创建失败")
                
        except Exception as e:
            print(f"      ❌ 测试失败: {e}")
    
    # 清理测试目录
    try:
        import shutil
        if os.path.exists("test_images"):
            shutil.rmtree("test_images")
            print(f"\n🧹 测试目录已清理")
    except:
        pass

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 路径问题修复工具")
    print("=" * 60)
    print("分析和修复步骤四的路径创建问题")
    print()
    
    # 测试有问题的路径
    test_problematic_paths()
    
    print("\n" + "=" * 60)
    print("🎯 分析完成")
    print("=" * 60)
    print("✅ 路径测试完成")
    
    print(f"\n🚀 现在需要手动修复stage4_image_crawler.py中的_create_local_directory方法")

if __name__ == "__main__":
    main()
