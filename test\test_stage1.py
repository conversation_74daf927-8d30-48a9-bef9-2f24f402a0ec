#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第一阶段功能：目录结构解析和存储
"""

import os
import sys
from database import DatabaseManager
from html_parser import NovolineParser

def test_html_parsing():
    """测试HTML解析功能"""
    print("=" * 50)
    print("测试HTML解析功能")
    print("=" * 50)
    
    if not os.path.exists("1.txt"):
        print("错误：未找到1.txt文件")
        return False
    
    parser = NovolineParser()
    categories = parser.parse_categories_from_file("1.txt")
    
    if not categories:
        print("错误：未能解析出目录结构")
        return False
    
    print(f"成功解析出 {len(categories)} 个主目录：")
    for i, category in enumerate(categories, 1):
        main_cat = category['main_category']
        sub_cats = category['sub_categories']
        print(f"{i}. {main_cat['name']} ({len(sub_cats)} 个子目录)")
        print(f"   URL: {main_cat['url']}")
        if main_cat.get('icon_url'):
            print(f"   图标: {main_cat['icon_url']}")
        
        # 显示前3个子目录
        for j, sub_cat in enumerate(sub_cats[:3]):
            print(f"   - {sub_cat['name']}: {sub_cat['url']}")
        if len(sub_cats) > 3:
            print(f"   ... 还有 {len(sub_cats) - 3} 个子目录")
        print()
    
    return True

def test_database_operations():
    """测试数据库操作"""
    print("=" * 50)
    print("测试数据库操作")
    print("=" * 50)
    
    # 创建测试数据库
    test_db_path = "test_novoline.db"
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
    
    db = DatabaseManager(test_db_path)
    
    # 测试插入主目录
    main_cat_id = db.insert_main_category(
        "测试主目录",
        "https://example.com/test",
        "https://example.com/icon.png"
    )
    print(f"插入主目录，ID: {main_cat_id}")
    
    # 测试插入子目录
    sub_cat_id = db.insert_sub_category(
        main_cat_id,
        "测试子目录",
        "https://example.com/test/sub"
    )
    print(f"插入子目录，ID: {sub_cat_id}")
    
    # 测试插入产品
    product_id = db.insert_product_url(
        sub_cat_id,
        "测试产品",
        "https://example.com/product/1",
        "https://example.com/product/1.jpg",
        "这是一个测试产品"
    )
    print(f"插入产品，ID: {product_id}")
    
    # 测试插入阶梯价格
    price_id = db.insert_tier_price(
        product_id,
        1, 10, 5.99, "USD"
    )
    print(f"插入价格，ID: {price_id}")
    
    # 测试统计信息
    stats = db.get_statistics()
    print(f"统计信息: {stats}")
    
    # 测试进度更新
    db.update_crawl_progress("test", "completed", 10, 10)
    progress = db.get_crawl_progress("test")
    print(f"进度信息: {progress}")
    
    # 清理测试数据库
    try:
        db = None  # 确保数据库连接关闭
        import time
        time.sleep(0.1)  # 短暂等待
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        print("数据库操作测试完成")
    except Exception as e:
        print(f"清理测试数据库时出错: {e}")
        print("数据库操作测试完成（文件未删除）")
    
    return True

def test_full_stage1():
    """测试完整的第一阶段流程"""
    print("=" * 50)
    print("测试完整的第一阶段流程")
    print("=" * 50)
    
    if not os.path.exists("1.txt"):
        print("错误：未找到1.txt文件")
        return False
    
    # 创建测试数据库
    test_db_path = "test_stage1.db"
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
    
    db = DatabaseManager(test_db_path)
    parser = NovolineParser()
    
    # 解析目录结构
    categories = parser.parse_categories_from_file("1.txt")
    if not categories:
        print("错误：未能解析出目录结构")
        return False
    
    print(f"解析出 {len(categories)} 个主目录")
    
    # 存储到数据库
    total_main_cats = 0
    total_sub_cats = 0
    
    for category_data in categories:
        main_cat = category_data['main_category']
        
        # 插入主目录
        main_cat_id = db.insert_main_category(
            main_cat['name'],
            main_cat['url'],
            main_cat.get('icon_url', '')
        )
        total_main_cats += 1
        
        # 插入子目录
        for sub_cat in category_data['sub_categories']:
            db.insert_sub_category(
                main_cat_id,
                sub_cat['name'],
                sub_cat['url']
            )
            total_sub_cats += 1
    
    print(f"成功存储 {total_main_cats} 个主目录和 {total_sub_cats} 个子目录")
    
    # 验证数据
    stats = db.get_statistics()
    print(f"数据库统计: {stats}")
    
    if stats['main_categories'] == total_main_cats and stats['sub_categories'] == total_sub_cats:
        print("✓ 数据存储验证成功")
        
        # 保留测试数据库供查看
        print(f"测试数据库已保存为: {test_db_path}")
        return True
    else:
        print("✗ 数据存储验证失败")
        return False

def main():
    """主测试函数"""
    print("Novoline爬虫第一阶段测试")
    print("测试目录结构解析和数据库存储功能")
    
    success_count = 0
    total_tests = 3
    
    # 测试HTML解析
    if test_html_parsing():
        print("✓ HTML解析测试通过")
        success_count += 1
    else:
        print("✗ HTML解析测试失败")
    
    # 测试数据库操作
    if test_database_operations():
        print("✓ 数据库操作测试通过")
        success_count += 1
    else:
        print("✗ 数据库操作测试失败")
    
    # 测试完整流程
    if test_full_stage1():
        print("✓ 第一阶段完整流程测试通过")
        success_count += 1
    else:
        print("✗ 第一阶段完整流程测试失败")
    
    print("=" * 50)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！第一阶段功能正常")
        return True
    else:
        print("❌ 部分测试失败，请检查代码")
        return False

if __name__ == "__main__":
    main()
