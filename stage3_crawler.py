#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三阶段爬虫 - 产品详情采集
支持状态管理、重试机制、批量处理
"""

import time
import sqlite3
import threading
from typing import Callable, Optional, List, Dict
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
from product_detail_parser import ProductDetailParser
from database import DatabaseManager

class Stage3Crawler:
    """第三阶段爬虫类"""
    
    def __init__(self, db_path: str = "novoline_spider_v2.db", max_workers: int = 5):
        self.db = DatabaseManager(db_path)
        self.parser = ProductDetailParser()

        # 状态控制
        self.is_running = False
        self.is_paused = False
        self.should_stop = False

        # 多线程设置
        self.max_workers = max_workers  # 最大线程数
        self.executor = None
        self.futures = []

        # 线程安全的统计
        self.stats_lock = threading.Lock()
        self.completed_count = 0
        self.failed_count = 0
        self.total_count = 0

        # 回调函数
        self.progress_callback = None
        self.error_callback = None
        self.completion_callback = None

        # 重试设置
        self.max_retries = 1
        self.retry_delay = 5  # 秒
    
    def set_callbacks(self, progress_callback: Callable = None, 
                     error_callback: Callable = None,
                     completion_callback: Callable = None):
        """设置回调函数"""
        self.progress_callback = progress_callback
        self.error_callback = error_callback
        self.completion_callback = completion_callback
    
    def start_stage3(self, filter_status: str = None):
        """
        启动第三阶段采集
        
        Args:
            filter_status: 过滤状态 ('pending', 'failed', None表示所有)
        """
        if self.is_running:
            self._notify_error("第三阶段正在运行中")
            return
        
        self.is_running = True
        self.should_stop = False

        # 重置统计
        with self.stats_lock:
            self.completed_count = 0
            self.failed_count = 0
            self.total_count = 0

        # 在新线程中运行多线程采集
        thread = threading.Thread(target=self._stage3_worker_multithreaded, args=(filter_status,))
        thread.daemon = True
        thread.start()
    
    def pause(self):
        """暂停采集"""
        self.is_paused = True
        self._notify_progress("已暂停第三阶段采集")
    
    def resume(self):
        """继续采集"""
        self.is_paused = False
        self._notify_progress("继续第三阶段采集")
    
    def stop(self):
        """停止采集"""
        self.should_stop = True
        self.is_running = False

        # 停止线程池
        if self.executor:
            self.executor.shutdown(wait=False)
            self.executor = None

        self._notify_progress("正在停止第三阶段采集...")
    
    def _stage3_worker(self, filter_status: str = None):
        """第三阶段工作线程"""
        try:
            self._notify_progress("开始第三阶段 - 产品详情采集")
            
            # 获取待采集的产品列表
            products = self._get_products_to_crawl(filter_status)
            
            if not products:
                self._notify_completion("没有需要采集的产品")
                return
            
            total_count = len(products)
            completed_count = 0
            failed_count = 0
            
            self._notify_progress(f"找到 {total_count} 个产品需要采集详情")
            
            for i, (product_url_id, product_name, product_url) in enumerate(products, 1):
                if self.should_stop:
                    break
                
                # 检查暂停状态
                while self.is_paused and not self.should_stop:
                    time.sleep(1)
                
                if self.should_stop:
                    break
                
                self._notify_progress(f"正在采集 ({i}/{total_count}): {product_name}")
                
                # 采集产品详情
                success = self._crawl_single_product(product_url_id, product_url, product_name)
                
                if success:
                    completed_count += 1
                else:
                    failed_count += 1
                
                # 添加延迟避免请求过快
                time.sleep(2)
            
            if not self.should_stop:
                self._notify_completion(f"第三阶段完成: 成功 {completed_count}, 失败 {failed_count}")
            else:
                self._notify_progress(f"第三阶段已停止: 已处理 {completed_count + failed_count}/{total_count}")
            
        except Exception as e:
            self._notify_error(f"第三阶段执行失败: {e}")
        finally:
            self.is_running = False
    
    def _get_products_to_crawl(self, filter_status: str = None) -> List[tuple]:
        """获取需要采集的产品列表"""
        with sqlite3.connect(self.db.db_path) as conn:
            cursor = conn.cursor()
            
            if filter_status == 'pending':
                # 只获取未采集的产品
                cursor.execute('''
                    SELECT pu.id, pu.product_name, pu.product_url
                    FROM product_urls pu
                    LEFT JOIN product_details pd ON pu.id = pd.product_url_id
                    WHERE pd.id IS NULL
                    ORDER BY pu.id
                ''')
            elif filter_status == 'failed':
                # 只获取采集失败的产品
                cursor.execute('''
                    SELECT pu.id, pu.product_name, pu.product_url
                    FROM product_urls pu
                    JOIN product_details pd ON pu.id = pd.product_url_id
                    WHERE pd.crawl_status = 'failed'
                    ORDER BY pu.id
                ''')
            else:
                # 获取所有需要采集的产品（未采集 + 失败的）
                cursor.execute('''
                    SELECT pu.id, pu.product_name, pu.product_url
                    FROM product_urls pu
                    LEFT JOIN product_details pd ON pu.id = pd.product_url_id
                    WHERE pd.id IS NULL OR pd.crawl_status = 'failed'
                    ORDER BY pu.id
                ''')
            
            return cursor.fetchall()

    def _stage3_worker_multithreaded(self, filter_status: str = None):
        """多线程第三阶段工作方法"""
        try:
            self._notify_progress("开始多线程采集产品详情...")

            # 获取待采集的产品
            products = self._get_products_to_crawl(filter_status)

            if not products:
                self._notify_completion("没有找到需要采集的产品")
                self.is_running = False
                return

            with self.stats_lock:
                self.total_count = len(products)

            self._notify_progress(f"找到 {len(products)} 个产品，开始使用 {self.max_workers} 个线程采集...")

            # 创建线程池
            self.executor = ThreadPoolExecutor(max_workers=self.max_workers)

            try:
                # 提交所有任务
                future_to_product = {}
                for product_url_id, product_name, product_url in products:
                    if self.should_stop:
                        break

                    future = self.executor.submit(
                        self._crawl_single_product_thread_safe,
                        product_url_id, product_url, product_name
                    )
                    future_to_product[future] = (product_url_id, product_name, product_url)

                # 处理完成的任务
                for future in as_completed(future_to_product):
                    if self.should_stop:
                        break

                    product_url_id, product_name, product_url = future_to_product[future]

                    try:
                        success = future.result()

                        with self.stats_lock:
                            if success:
                                self.completed_count += 1
                            else:
                                self.failed_count += 1

                            # 更新进度
                            total_processed = self.completed_count + self.failed_count
                            progress_msg = f"进度: {total_processed}/{self.total_count} | 成功: {self.completed_count} | 失败: {self.failed_count}"

                        self._notify_progress(progress_msg)

                        # 每处理10个产品休息一下，避免过于频繁的请求
                        if total_processed % 10 == 0:
                            time.sleep(1)

                    except Exception as e:
                        self._notify_error(f"处理产品 {product_name} 时出错: {e}")
                        with self.stats_lock:
                            self.failed_count += 1

            finally:
                # 关闭线程池
                self.executor.shutdown(wait=True)
                self.executor = None

            # 完成统计
            with self.stats_lock:
                final_msg = f"采集完成！总计: {self.total_count} | 成功: {self.completed_count} | 失败: {self.failed_count}"

            self._notify_completion(final_msg)

        except Exception as e:
            self._notify_error(f"多线程采集失败: {e}")
        finally:
            self.is_running = False

    def _crawl_single_product_thread_safe(self, product_url_id: int, product_url: str, product_name: str) -> bool:
        """线程安全的单产品采集方法"""
        try:
            # 解析产品详情
            detail_data = self.parser.parse_product_details(product_url)

            if not detail_data:
                self._update_product_status(product_url_id, 'failed', '解析产品详情失败')
                return False

            # 保存产品详情（使用横向阶梯价格结构）
            self.db.insert_product_detail(product_url_id, detail_data)

            # 更新状态为成功
            self._update_product_status(product_url_id, 'success')

            return True

        except Exception as e:
            error_msg = f"采集产品详情失败: {str(e)}"
            self._update_product_status(product_url_id, 'failed', error_msg)
            return False

    def _crawl_single_product(self, product_url_id: int, product_url: str, product_name: str) -> bool:
        """采集单个产品的详情"""
        try:
            # 更新状态为正在采集
            self._update_product_status(product_url_id, 'retrying')
            
            # 解析产品详情
            detail_data = self.parser.parse_product_detail(product_url)
            
            if detail_data:
                # 保存产品详情
                self._save_product_detail(product_url_id, detail_data)
                
                # 更新状态为成功
                self._update_product_status(product_url_id, 'success')
                
                return True
            else:
                # 解析失败，尝试重试
                return self._handle_crawl_failure(product_url_id, product_url, "解析失败")
                
        except Exception as e:
            # 采集异常，尝试重试
            return self._handle_crawl_failure(product_url_id, product_url, str(e))
    
    def _handle_crawl_failure(self, product_url_id: int, product_url: str, error_msg: str) -> bool:
        """处理采集失败"""
        # 获取当前重试次数
        attempts = self._get_crawl_attempts(product_url_id)
        
        if attempts < self.max_retries:
            # 还可以重试
            self._notify_progress(f"采集失败，{self.retry_delay}秒后重试: {error_msg}")
            time.sleep(self.retry_delay)
            
            # 增加重试次数
            self._increment_crawl_attempts(product_url_id, error_msg)
            
            # 重新尝试
            try:
                detail_data = self.parser.parse_product_detail(product_url)
                if detail_data:
                    self._save_product_detail(product_url_id, detail_data)
                    self._update_product_status(product_url_id, 'success')
                    return True
            except Exception as e:
                error_msg = f"重试失败: {e}"
        
        # 重试次数用完或重试仍失败
        self._update_product_status(product_url_id, 'failed', error_msg)
        self._log_error(product_url, error_msg)
        return False
    
    def _save_product_detail(self, product_url_id: int, detail_data: Dict):
        """保存产品详情到数据库"""
        # 使用新的横向阶梯价格结构插入数据
        # 所有阶梯价格信息已经包含在detail_data中，直接插入即可
        self.db.insert_product_detail(product_url_id, detail_data)
    
    def _update_product_status(self, product_url_id: int, status: str, error_msg: str = None):
        """更新产品采集状态"""
        with sqlite3.connect(self.db.db_path) as conn:
            cursor = conn.cursor()

            # 检查记录是否存在
            cursor.execute('SELECT id FROM product_details WHERE product_url_id = ?', (product_url_id,))
            exists = cursor.fetchone()

            if exists:
                # 更新现有记录
                cursor.execute('''
                    UPDATE product_details
                    SET crawl_status = ?, last_crawl_attempt = CURRENT_TIMESTAMP,
                        error_message = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE product_url_id = ?
                ''', (status, error_msg, product_url_id))
            else:
                # 创建新记录（只设置必要字段）
                cursor.execute('''
                    INSERT INTO product_details
                    (product_url_id, crawl_status, last_crawl_attempt, error_message,
                     crawl_attempts, created_at, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP, ?, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ''', (product_url_id, status, error_msg))

            conn.commit()
    
    def _get_crawl_attempts(self, product_url_id: int) -> int:
        """获取采集尝试次数"""
        with sqlite3.connect(self.db.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT crawl_attempts FROM product_details 
                WHERE product_url_id = ?
            ''', (product_url_id,))
            result = cursor.fetchone()
            return result[0] if result else 0
    
    def _increment_crawl_attempts(self, product_url_id: int, error_msg: str):
        """增加采集尝试次数"""
        with sqlite3.connect(self.db.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE product_details 
                SET crawl_attempts = crawl_attempts + 1,
                    error_message = ?,
                    last_crawl_attempt = CURRENT_TIMESTAMP
                WHERE product_url_id = ?
            ''', (error_msg, product_url_id))
            conn.commit()
    
    def _log_error(self, product_url: str, error_msg: str):
        """记录错误日志"""
        with sqlite3.connect(self.db.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO error_logs (stage, product_url, error_type, error_message)
                VALUES ('details', ?, 'CrawlError', ?)
            ''', (product_url, error_msg))
            conn.commit()
    
    def _notify_progress(self, message: str):
        """通知进度"""
        if self.progress_callback:
            self.progress_callback("details", message)
        else:
            print(f"[details] {message}")
    
    def _notify_error(self, message: str):
        """通知错误"""
        if self.error_callback:
            self.error_callback(message)
        else:
            print(f"❌ {message}")
    
    def _notify_completion(self, message: str):
        """通知完成"""
        if self.completion_callback:
            self.completion_callback(message)
        else:
            print(f"✅ {message}")
    
    def get_stage3_statistics(self) -> Dict:
        """获取第三阶段统计信息"""
        with sqlite3.connect(self.db.db_path) as conn:
            cursor = conn.cursor()
            
            stats = {}
            
            # 总产品数
            cursor.execute('SELECT COUNT(*) FROM product_urls')
            stats['total_products'] = cursor.fetchone()[0]
            
            # 各状态统计
            cursor.execute('''
                SELECT 
                    COUNT(CASE WHEN pd.crawl_status = 'success' THEN 1 END) as success,
                    COUNT(CASE WHEN pd.crawl_status = 'failed' THEN 1 END) as failed,
                    COUNT(CASE WHEN pd.crawl_status = 'retrying' THEN 1 END) as retrying,
                    COUNT(CASE WHEN pd.id IS NULL THEN 1 END) as pending
                FROM product_urls pu
                LEFT JOIN product_details pd ON pu.id = pd.product_url_id
            ''')
            
            result = cursor.fetchone()
            stats['success'] = result[0]
            stats['failed'] = result[1] 
            stats['retrying'] = result[2]
            stats['pending'] = result[3]
            
            return stats

def main():
    """主函数 - 测试第三阶段爬虫"""
    print("🎯 第三阶段爬虫测试")
    
    crawler = Stage3Crawler()
    
    # 设置回调
    def on_progress(stage, message):
        print(f"[{stage}] {message}")
    
    def on_error(message):
        print(f"❌ {message}")
    
    def on_completion(message):
        print(f"✅ {message}")
    
    crawler.set_callbacks(on_progress, on_error, on_completion)
    
    # 显示统计信息
    stats = crawler.get_stage3_statistics()
    print(f"\n📊 第三阶段统计:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # 询问是否开始采集
    if stats['pending'] > 0 or stats['failed'] > 0:
        choice = input(f"\n开始第三阶段采集? (y/N): ").strip().lower()
        if choice == 'y':
            crawler.start_stage3()
            
            # 等待完成
            while crawler.is_running:
                time.sleep(1)

if __name__ == "__main__":
    main()
