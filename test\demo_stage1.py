#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一阶段演示脚本
演示目录结构爬取功能
"""

import os
import time
from spider_core import NovolineSpider

def progress_callback(stage, message):
    """进度回调函数"""
    print(f"[{stage}] {message}")

def error_callback(message, stack_trace=None):
    """错误回调函数"""
    print(f"❌ 错误: {message}")
    if stack_trace:
        print(f"详细信息: {stack_trace}")

def completion_callback(message):
    """完成回调函数"""
    print(f"🎉 {message}")

def main():
    """主演示函数"""
    print("=" * 60)
    print("Novoline网站爬虫 - 第一阶段演示")
    print("=" * 60)
    
    # 检查1.txt文件
    if not os.path.exists("1.txt"):
        print("❌ 错误：未找到1.txt文件")
        return
    
    # 创建爬虫实例
    spider = NovolineSpider("demo_stage1.db")
    spider.set_callbacks(
        progress_callback=progress_callback,
        error_callback=error_callback,
        completion_callback=completion_callback
    )
    
    print("📋 准备开始第一阶段爬取...")
    print("🎯 目标：解析目录结构并存储到数据库")
    print()
    
    # 开始爬取
    spider.start_crawling("categories", restart=True)
    
    # 等待完成
    print("⏳ 爬取进行中，请等待...")
    while spider.is_running:
        time.sleep(1)
    
    # 显示结果
    print("\n" + "=" * 60)
    print("📊 爬取结果统计")
    print("=" * 60)
    
    stats = spider.db.get_statistics()
    print(f"✅ 主目录数量: {stats['main_categories']}")
    print(f"✅ 子目录数量: {stats['sub_categories']}")
    print(f"✅ 产品数量: {stats['products']}")
    print(f"✅ 价格记录: {stats['tier_prices']}")
    print(f"⚠️  错误数量: {stats['errors']}")
    
    # 显示进度信息
    progress_data = spider.db.get_crawl_progress()
    if progress_data:
        print("\n📈 进度详情:")
        for progress in progress_data:
            stage = progress['stage']
            status = progress['status']
            total = progress['total_items']
            completed = progress['completed_items']
            print(f"   {stage}: {status} ({completed}/{total})")
    
    print(f"\n💾 数据已保存到: demo_stage1.db")
    print("🔍 您可以使用SQLite工具查看数据库内容")
    
    # 显示一些示例数据
    print("\n" + "=" * 60)
    print("📝 示例数据预览")
    print("=" * 60)
    
    import sqlite3
    with sqlite3.connect("demo_stage1.db") as conn:
        cursor = conn.cursor()
        
        # 显示主目录
        cursor.execute("SELECT category_name, category_url FROM main_categories LIMIT 5")
        main_cats = cursor.fetchall()
        
        print("🗂️  主目录示例:")
        for name, url in main_cats:
            print(f"   • {name}")
            print(f"     {url}")
        
        # 显示子目录统计
        cursor.execute("""
            SELECT mc.category_name, COUNT(sc.id) as sub_count
            FROM main_categories mc
            LEFT JOIN sub_categories sc ON mc.id = sc.main_category_id
            GROUP BY mc.id, mc.category_name
            ORDER BY sub_count DESC
        """)
        sub_stats = cursor.fetchall()
        
        print("\n📊 各主目录的子目录数量:")
        for main_name, sub_count in sub_stats:
            print(f"   • {main_name}: {sub_count} 个子目录")
    
    print("\n🎯 第一阶段演示完成！")
    print("💡 提示：您可以运行 'python main.py' 启动图形界面进行完整的爬取")

if __name__ == "__main__":
    main()
