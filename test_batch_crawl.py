#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量采集功能
"""

import sqlite3

def check_uncrawled_categories():
    """检查未采集的分类"""
    print("=" * 60)
    print("🔍 检查未采集的分类")
    print("=" * 60)
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 查找没有产品URL的子分类
            cursor.execute('''
                SELECT sc.id, sc.sub_category_name, sc.sub_category_url, COUNT(pu.id) as product_count
                FROM sub_categories sc
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY sc.id, sc.sub_category_name, sc.sub_category_url
                ORDER BY product_count ASC, sc.id
            ''')
            
            rows = cursor.fetchall()
            
            uncrawled = []
            crawled = []
            
            for row in rows:
                cat_id, name, url, count = row
                if count == 0:
                    uncrawled.append((cat_id, name, url))
                else:
                    crawled.append((cat_id, name, count))
            
            print(f"📊 分类统计:")
            print(f"   总分类数: {len(rows)}")
            print(f"   已采集: {len(crawled)}")
            print(f"   未采集: {len(uncrawled)}")
            
            if uncrawled:
                print(f"\n❌ 未采集的分类 ({len(uncrawled)}个):")
                for i, (cat_id, name, url) in enumerate(uncrawled, 1):
                    print(f"   {i:2d}. [ID:{cat_id}] {name}")
                    print(f"       URL: {url}")
            else:
                print(f"\n✅ 所有分类都已采集完成！")
            
            if crawled:
                print(f"\n✅ 已采集的分类 (前10个):")
                for i, (cat_id, name, count) in enumerate(crawled[:10], 1):
                    print(f"   {i:2d}. [ID:{cat_id}] {name}: {count} 个产品")
            
            return uncrawled
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return []

def check_total_products():
    """检查总产品数量"""
    print(f"\n" + "=" * 60)
    print("📊 产品数量统计")
    print("=" * 60)
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 总产品URL数量
            cursor.execute('SELECT COUNT(*) FROM product_urls')
            total_products = cursor.fetchone()[0]
            
            # 已采集详情数量
            cursor.execute('SELECT COUNT(*) FROM product_details WHERE crawl_status = "success"')
            crawled_details = cursor.fetchone()[0]
            
            # 待采集详情数量
            cursor.execute('''
                SELECT COUNT(*) FROM product_urls pu
                LEFT JOIN product_details pd ON pu.id = pd.product_url_id
                WHERE pd.id IS NULL OR pd.crawl_status != "success"
            ''')
            pending_details = cursor.fetchone()[0]
            
            print(f"📈 数据统计:")
            print(f"   总产品URL: {total_products:,}")
            print(f"   已采集详情: {crawled_details:,}")
            print(f"   待采集详情: {pending_details:,}")
            
            if total_products > 0:
                detail_progress = (crawled_details / total_products) * 100
                print(f"   详情采集进度: {detail_progress:.1f}%")
            
    except Exception as e:
        print(f"❌ 统计失败: {e}")

def simulate_batch_crawl():
    """模拟批量采集过程"""
    print(f"\n" + "=" * 60)
    print("🚀 模拟批量采集过程")
    print("=" * 60)
    
    uncrawled = check_uncrawled_categories()
    
    if not uncrawled:
        print("✅ 没有未采集的分类，无需批量采集")
        return
    
    print(f"\n🔧 批量采集建议:")
    print(f"   1. 重新启动程序: python main.py")
    print(f"   2. 进入第二阶段（产品URL）")
    print(f"   3. 点击'批量采集未采集分类'按钮")
    print(f"   4. 确认采集 {len(uncrawled)} 个分类")
    print(f"   5. 等待采集完成")
    
    print(f"\n⏱️ 预估时间:")
    print(f"   每个分类约需 30-60 秒")
    print(f"   总计约需 {len(uncrawled) * 45 // 60} 分钟")
    
    print(f"\n📋 采集顺序:")
    for i, (cat_id, name, url) in enumerate(uncrawled[:5], 1):
        print(f"   {i}. {name}")
    
    if len(uncrawled) > 5:
        print(f"   ... 还有 {len(uncrawled) - 5} 个分类")

def main():
    """主函数"""
    print("🧪 批量采集功能测试")
    print("检查未采集的分类并提供批量采集建议")
    print()
    
    # 检查未采集分类
    uncrawled = check_uncrawled_categories()
    
    # 检查产品统计
    check_total_products()
    
    # 模拟批量采集
    simulate_batch_crawl()
    
    print(f"\n" + "=" * 60)
    print("🎯 总结")
    print("=" * 60)
    
    if uncrawled:
        print(f"🔧 发现 {len(uncrawled)} 个未采集的分类")
        print(f"💡 建议使用GUI中的'批量采集未采集分类'功能")
        print(f"🚀 这将自动采集所有缺失的产品URL")
    else:
        print(f"✅ 所有分类都已采集完成")
        print(f"🎉 可以进入第三阶段采集产品详情")

if __name__ == "__main__":
    main()
