#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图形用户界面模块
使用tkinter创建分阶段爬虫控制界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import sqlite3
import os
import sys
from spider_core import NovolineSpider
from database import DatabaseManager

class SpiderGUI:
    """爬虫图形界面类"""
    
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.root.title("Novoline网站爬虫 - 分阶段数据采集系统")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # 使用新的数据库结构
        if os.path.exists("novoline_spider_v2.db"):
            # 使用新数据库
            self.db = DatabaseManager("novoline_spider_v2.db")
            self.spider = NovolineSpider("novoline_spider_v2.db")
            self.has_new_database = True
            print("✅ 使用新数据库: novoline_spider_v2.db")
        else:
            # 回退到旧数据库
            self.db = DatabaseManager()
            self.spider = NovolineSpider()
            self.has_new_database = False
            print("⚠️ 使用旧数据库结构")

        # 设置爬虫回调
        self.spider.set_callbacks(
            progress_callback=self.on_progress_update,
            error_callback=self.on_error,
            completion_callback=self.on_completion
        )

        # 启用第三阶段自动刷新
        self.stage3_auto_refresh_enabled = True
        self.debug_mode = False  # 设置为False以减少日志输出

        # 创建界面
        self.create_widgets()

        # 启动状态更新线程
        self.start_status_update_thread()

        # 启动第三阶段自动刷新（延迟5秒开始，避免启动时的冲突）
        if self.has_new_database:
            self.root.after(5000, self.auto_refresh_stage3_data)
    
    def create_widgets(self):
        """创建界面组件"""
        # 配置根窗口网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # 创建主容器
        main_container = ttk.Frame(self.root, padding="10")
        main_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_container.columnconfigure(0, weight=1)
        main_container.rowconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_container, text="Novoline网站爬虫 - 分阶段数据采集系统",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 10))

        # 创建Tab控件
        self.notebook = ttk.Notebook(main_container)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 创建各个Tab页面
        self.create_stage1_tab()
        self.create_stage2_tab()
        self.create_stage3_tab()
        self.create_data_view_tab()
        self.create_log_tab()

    def create_stage1_tab(self):
        """创建第一阶段Tab页面"""
        stage1_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(stage1_frame, text="第一步：目录结构")

        # 配置网格权重
        stage1_frame.columnconfigure(0, weight=1)
        stage1_frame.rowconfigure(2, weight=1)

        # 阶段说明
        desc_frame = ttk.LabelFrame(stage1_frame, text="阶段说明", padding="10")
        desc_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        desc_text = ("第一阶段：解析网站目录结构\n"
                    "• 从1.txt文件中解析主目录和子目录信息\n"
                    "• 提取目录名称、URL和图标信息\n"
                    "• 存储到数据库中供后续阶段使用")
        ttk.Label(desc_frame, text=desc_text, justify=tk.LEFT).pack(anchor=tk.W)

        # 控制面板
        control_frame = ttk.LabelFrame(stage1_frame, text="控制面板", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 重新开始选项
        self.stage1_restart_var = tk.BooleanVar(value=False)
        restart_check = ttk.Checkbutton(control_frame, text="重新开始（清空目录结构数据）",
                                       variable=self.stage1_restart_var)
        restart_check.pack(anchor=tk.W, pady=(0, 10))

        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(anchor=tk.W)

        self.stage1_start_btn = ttk.Button(button_frame, text="开始第一阶段",
                                          command=lambda: self.start_stage("categories"))
        self.stage1_start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stage1_stop_btn = ttk.Button(button_frame, text="停止",
                                         command=self.stop_crawling, state=tk.DISABLED)
        self.stage1_stop_btn.pack(side=tk.LEFT)

        # 进度和数据展示
        self.create_stage_progress_frame(stage1_frame, "stage1")

    def create_stage2_tab(self):
        """创建第二阶段Tab页面"""
        stage2_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(stage2_frame, text="第二步：产品URL")

        # 配置网格权重
        stage2_frame.columnconfigure(0, weight=1)
        stage2_frame.rowconfigure(2, weight=1)

        # 阶段说明
        desc_frame = ttk.LabelFrame(stage2_frame, text="阶段说明", padding="10")
        desc_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        desc_text = ("第二阶段：爬取产品URL\n"
                    "• 访问每个子目录页面\n"
                    "• 提取产品名称、URL\n"
                    "• 处理分页和动态加载内容")
        ttk.Label(desc_frame, text=desc_text, justify=tk.LEFT).pack(anchor=tk.W)

        # 控制面板
        control_frame = ttk.LabelFrame(stage2_frame, text="控制面板", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 重新开始选项
        self.stage2_restart_var = tk.BooleanVar(value=False)
        restart_check = ttk.Checkbutton(control_frame, text="重新开始（清空产品数据）",
                                       variable=self.stage2_restart_var)
        restart_check.pack(anchor=tk.W, pady=(0, 10))

        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(anchor=tk.W)

        self.stage2_start_btn = ttk.Button(button_frame, text="开始第二阶段",
                                          command=lambda: self.start_stage("products"))
        self.stage2_start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stage2_pause_btn = ttk.Button(button_frame, text="暂停",
                                          command=self.pause_crawling, state=tk.DISABLED)
        self.stage2_pause_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stage2_resume_btn = ttk.Button(button_frame, text="继续",
                                           command=self.resume_crawling, state=tk.DISABLED)
        self.stage2_resume_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stage2_stop_btn = ttk.Button(button_frame, text="停止",
                                         command=self.stop_crawling, state=tk.DISABLED)
        self.stage2_stop_btn.pack(side=tk.LEFT)

        # 进度和数据展示
        self.create_stage_progress_frame(stage2_frame, "stage2")

    def create_stage3_tab(self):
        """创建第三阶段Tab页面"""
        stage3_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(stage3_frame, text="第三步：阶梯价格")

        # 配置网格权重
        stage3_frame.columnconfigure(0, weight=1)
        stage3_frame.rowconfigure(2, weight=1)

        # 阶段说明
        desc_frame = ttk.LabelFrame(stage3_frame, text="阶段说明", padding="10")
        desc_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        desc_text = ("第三阶段：采集产品详情\n"
                    "• 访问每个产品详情页面\n"
                    "• 提取完整导航、SKU、分类信息\n"
                    "• 采集阶梯价格和折扣信息\n"
                    "• 支持状态管理和重试机制")
        ttk.Label(desc_frame, text=desc_text, justify=tk.LEFT).pack(anchor=tk.W)

        # 控制面板
        control_frame = ttk.LabelFrame(stage3_frame, text="控制面板", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 过滤选项
        filter_frame = ttk.Frame(control_frame)
        filter_frame.pack(anchor=tk.W, pady=(0, 10))

        ttk.Label(filter_frame, text="采集范围:").pack(side=tk.LEFT)

        self.stage3_filter = tk.StringVar(value="all")
        ttk.Radiobutton(filter_frame, text="全部", variable=self.stage3_filter, value="all").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(filter_frame, text="仅未采集", variable=self.stage3_filter, value="pending").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(filter_frame, text="仅失败的", variable=self.stage3_filter, value="failed").pack(side=tk.LEFT, padx=5)

        # 线程数设置
        thread_frame = ttk.Frame(control_frame)
        thread_frame.pack(anchor=tk.W, pady=(10, 10))

        ttk.Label(thread_frame, text="并发线程数:").pack(side=tk.LEFT)

        self.thread_count_var = tk.IntVar(value=5)  # 默认5个线程
        thread_spinbox = ttk.Spinbox(thread_frame, from_=1, to=20, width=5,
                                   textvariable=self.thread_count_var)
        thread_spinbox.pack(side=tk.LEFT, padx=(5, 10))

        ttk.Label(thread_frame, text="(1-20个线程，推荐5-10个)",
                 foreground="gray").pack(side=tk.LEFT)

        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(anchor=tk.W)

        self.stage3_start_btn = ttk.Button(button_frame, text="开始第三阶段",
                                          command=self.start_stage3_new)
        self.stage3_start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stage3_pause_btn = ttk.Button(button_frame, text="暂停",
                                          command=self.pause_stage3_new, state=tk.DISABLED)
        self.stage3_pause_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stage3_resume_btn = ttk.Button(button_frame, text="继续",
                                           command=self.resume_stage3_new, state=tk.DISABLED)
        self.stage3_resume_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stage3_stop_btn = ttk.Button(button_frame, text="停止",
                                         command=self.stop_stage3_new, state=tk.DISABLED)
        self.stage3_stop_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 批量操作按钮
        batch_frame = ttk.Frame(control_frame)
        batch_frame.pack(anchor=tk.W, pady=(10, 0))

        ttk.Button(batch_frame, text="重试失败的", command=self.retry_failed_products).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(batch_frame, text="刷新状态", command=self.refresh_prices_data).pack(side=tk.LEFT, padx=(0, 10))

        # 自动刷新控制
        self.auto_refresh_var = tk.BooleanVar(value=True)
        auto_refresh_check = ttk.Checkbutton(batch_frame, text="自动刷新",
                                           variable=self.auto_refresh_var,
                                           command=self.toggle_auto_refresh)
        auto_refresh_check.pack(side=tk.LEFT)

        # 进度和数据展示
        self.create_stage_progress_frame(stage3_frame, "stage3")

    def create_data_view_tab(self):
        """创建数据查看Tab页面"""
        data_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(data_frame, text="数据查看")

        # 配置网格权重
        data_frame.columnconfigure(0, weight=1)
        data_frame.rowconfigure(1, weight=1)

        # 统计信息框架
        stats_frame = ttk.LabelFrame(data_frame, text="数据统计", padding="10")
        stats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        self.stats_text = tk.StringVar()
        self.stats_label = ttk.Label(stats_frame, textvariable=self.stats_text, font=("Arial", 10))
        self.stats_label.pack(anchor=tk.W)

        # 数据展示区域
        data_display_frame = ttk.LabelFrame(data_frame, text="数据预览", padding="10")
        data_display_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        data_display_frame.columnconfigure(0, weight=1)
        data_display_frame.rowconfigure(1, weight=1)

        # 数据类型选择
        type_frame = ttk.Frame(data_display_frame)
        type_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(type_frame, text="数据类型:").pack(side=tk.LEFT, padx=(0, 10))

        self.data_type_var = tk.StringVar(value="main_categories")
        data_type_combo = ttk.Combobox(type_frame, textvariable=self.data_type_var,
                                      values=["main_categories", "sub_categories", "product_urls", "product_details", "tier_prices"],
                                      state="readonly", width=20)
        data_type_combo.pack(side=tk.LEFT, padx=(0, 10))
        data_type_combo.bind('<<ComboboxSelected>>', self.on_data_type_changed)

        refresh_btn = ttk.Button(type_frame, text="刷新数据", command=self.refresh_data_view)
        refresh_btn.pack(side=tk.LEFT)

        # 数据表格
        self.create_data_tree(data_display_frame)

    def create_log_tab(self):
        """创建日志Tab页面"""
        log_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(log_frame, text="运行日志")

        # 配置网格权重
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 日志控制按钮
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.grid(row=1, column=0, sticky=tk.W)

        clear_log_btn = ttk.Button(log_control_frame, text="清空日志", command=self.clear_log)
        clear_log_btn.pack(side=tk.LEFT, padx=(0, 10))

        export_log_btn = ttk.Button(log_control_frame, text="导出日志", command=self.export_log)
        export_log_btn.pack(side=tk.LEFT)

    def create_stage_progress_frame(self, parent, stage_name):
        """创建阶段进度框架"""
        progress_frame = ttk.LabelFrame(parent, text="进度信息", padding="10")
        progress_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        progress_frame.columnconfigure(1, weight=1)
        progress_frame.rowconfigure(3, weight=1)

        # 当前状态
        ttk.Label(progress_frame, text="状态:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        status_label = ttk.Label(progress_frame, text="就绪", foreground="green")
        status_label.grid(row=0, column=1, sticky=tk.W)
        setattr(self, f"{stage_name}_status_label", status_label)

        # 进度条
        ttk.Label(progress_frame, text="进度:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        progress_bar.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(5, 0))
        setattr(self, f"{stage_name}_progress_bar", progress_bar)

        # 进度文本
        progress_text = tk.StringVar(value="0/0")
        progress_label = ttk.Label(progress_frame, textvariable=progress_text)
        progress_label.grid(row=2, column=1, sticky=tk.W, pady=(5, 0))
        setattr(self, f"{stage_name}_progress_text", progress_text)

        # 第二阶段添加详细进度信息
        if stage_name == "stage2":
            ttk.Label(progress_frame, text="详细进度:").grid(row=3, column=0, sticky=tk.W, padx=(0, 10))
            detail_text = tk.StringVar(value="等待开始...")
            detail_label = ttk.Label(progress_frame, textvariable=detail_text, font=("Arial", 8))
            detail_label.grid(row=3, column=1, sticky=tk.W, pady=(5, 0))
            setattr(self, f"{stage_name}_detail_text", detail_text)

        # 数据预览
        if stage_name == "stage1":
            self.create_categories_preview(progress_frame)
        elif stage_name == "stage2":
            self.create_products_preview(progress_frame)
        elif stage_name == "stage3":
            self.create_prices_preview(progress_frame)

    def create_categories_preview(self, parent):
        """创建目录预览"""
        preview_frame = ttk.LabelFrame(parent, text="目录预览", padding="5")
        preview_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)

        # 创建树形视图
        columns = ("name", "url", "status")
        self.categories_tree = ttk.Treeview(preview_frame, columns=columns, show="tree headings", height=8)

        # 设置列标题
        self.categories_tree.heading("#0", text="类型")
        self.categories_tree.heading("name", text="名称 (产品数量)")
        self.categories_tree.heading("url", text="URL")
        self.categories_tree.heading("status", text="状态/最后采集时间")

        # 设置列宽
        self.categories_tree.column("#0", width=80)
        self.categories_tree.column("name", width=300)
        self.categories_tree.column("url", width=250)
        self.categories_tree.column("status", width=150)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.categories_tree.yview)
        self.categories_tree.configure(yscrollcommand=scrollbar.set)

        self.categories_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 添加右键菜单
        self.create_categories_context_menu()

    def create_categories_context_menu(self):
        """创建分类表格的右键菜单"""
        self.categories_context_menu = tk.Menu(self.root, tearoff=0)
        self.categories_context_menu.add_command(label="重新采集此子目录", command=self.recrawl_sub_category)
        self.categories_context_menu.add_command(label="重新采集此主目录", command=self.recrawl_main_category)
        self.categories_context_menu.add_separator()
        self.categories_context_menu.add_command(label="查看详细状态", command=self.show_category_detail)

        # 绑定右键事件
        self.categories_tree.bind("<Button-3>", self.show_categories_context_menu)

    def show_categories_context_menu(self, event):
        """显示分类右键菜单"""
        # 选择点击的项目
        item = self.categories_tree.identify_row(event.y)
        if item:
            self.categories_tree.selection_set(item)
            self.categories_context_menu.post(event.x_root, event.y_root)

    def recrawl_sub_category(self):
        """重新采集选中的子目录"""
        selection = self.categories_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个子目录")
            return

        item = selection[0]
        item_text = self.categories_tree.item(item, "text")

        if item_text != "子目录":
            messagebox.showwarning("警告", "请选择一个子目录进行重新采集")
            return

        # 获取子目录信息
        values = self.categories_tree.item(item, "values")
        if not values:
            return

        sub_name = values[0].split(" (")[0].replace("✅ ", "").replace("⏳ ", "").replace("⚠️ ", "")
        sub_url = values[1]

        # 从数据库获取子目录ID
        try:
            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT id FROM sub_categories WHERE sub_category_url = ?', (sub_url,))
                result = cursor.fetchone()

                if result:
                    sub_id = result[0]

                    # 确认对话框
                    if messagebox.askyesno("确认重新采集",
                                         f"确定要重新采集子目录 '{sub_name}' 吗？\n\n"
                                         f"这将删除该子目录下的所有现有产品数据，然后重新采集。"):

                        self.log_message(f"开始重新采集子目录: {sub_name}")
                        self.spider.crawl_single_sub_category(sub_id, reset_first=True)

                else:
                    messagebox.showerror("错误", "未找到对应的子目录")

        except Exception as e:
            messagebox.showerror("错误", f"重新采集失败: {e}")

    def recrawl_main_category(self):
        """重新采集选中的主目录"""
        selection = self.categories_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个目录")
            return

        item = selection[0]
        item_text = self.categories_tree.item(item, "text")

        # 如果选择的是子目录，获取其父目录
        if item_text == "子目录":
            parent_item = self.categories_tree.parent(item)
            if parent_item:
                item = parent_item
            else:
                messagebox.showwarning("警告", "无法找到父目录")
                return

        # 获取主目录信息
        values = self.categories_tree.item(item, "values")
        if not values:
            return

        main_name = values[0].split(" (")[0]
        main_url = values[1]

        # 从数据库获取主目录ID
        try:
            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT id FROM main_categories WHERE category_url = ?', (main_url,))
                result = cursor.fetchone()

                if result:
                    main_id = result[0]

                    # 确认对话框
                    if messagebox.askyesno("确认重新采集",
                                         f"确定要重新采集主目录 '{main_name}' 吗？\n\n"
                                         f"这将删除该主目录下所有子目录的产品数据，然后重新采集。"):

                        self.log_message(f"开始重新采集主目录: {main_name}")
                        self.spider.crawl_single_main_category(main_id, reset_first=True)

                else:
                    messagebox.showerror("错误", "未找到对应的主目录")

        except Exception as e:
            messagebox.showerror("错误", f"重新采集失败: {e}")

    def show_category_detail(self):
        """显示分类详细状态"""
        selection = self.categories_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个目录")
            return

        item = selection[0]
        values = self.categories_tree.item(item, "values")
        item_text = self.categories_tree.item(item, "text")

        if not values:
            return

        # 获取详细信息
        if item_text == "子目录":
            name = values[0].split(" (")[0].replace("✅ ", "").replace("⏳ ", "").replace("⚠️ ", "")
            url = values[1]
            status = values[2] if len(values) > 2 else "未知"

            # 从数据库获取详细信息
            try:
                with sqlite3.connect(self.db.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        SELECT sc.product_count, sc.page_count, sc.last_crawled_at,
                               COUNT(pu.id) as actual_count
                        FROM sub_categories sc
                        LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                        WHERE sc.sub_category_url = ?
                        GROUP BY sc.id
                    ''', (url,))
                    result = cursor.fetchone()

                    if result:
                        recorded_count, page_count, last_crawled, actual_count = result

                        detail_text = f"子目录详细信息:\n\n"
                        detail_text += f"名称: {name}\n"
                        detail_text += f"URL: {url}\n"
                        detail_text += f"记录的产品数: {recorded_count or 0}\n"
                        detail_text += f"实际产品数: {actual_count}\n"
                        detail_text += f"页面数: {page_count or 0}\n"
                        detail_text += f"最后采集时间: {last_crawled or '未采集'}\n"
                        detail_text += f"状态: {status}\n"

                        if recorded_count != actual_count:
                            detail_text += f"\n⚠️ 数量不一致！建议重新采集"

                        messagebox.showinfo("分类详细信息", detail_text)
                    else:
                        messagebox.showerror("错误", "未找到详细信息")

            except Exception as e:
                messagebox.showerror("错误", f"获取详细信息失败: {e}")
        else:
            # 主目录信息
            name = values[0].split(" (")[0]
            url = values[1]
            messagebox.showinfo("主目录信息", f"主目录: {name}\nURL: {url}")

    def create_products_context_menu(self):
        """创建产品表格的右键菜单"""
        self.products_context_menu = tk.Menu(self.root, tearoff=0)
        self.products_context_menu.add_command(label="重新采集此产品详情", command=self.recrawl_product_detail)
        self.products_context_menu.add_command(label="查看产品详情", command=self.view_product_detail)
        self.products_context_menu.add_separator()
        self.products_context_menu.add_command(label="标记为失败", command=self.mark_product_failed)
        self.products_context_menu.add_command(label="重置采集状态", command=self.reset_product_crawl_status)

        # 绑定右键事件
        self.products_tree.bind("<Button-3>", self.show_products_context_menu)

    def show_products_context_menu(self, event):
        """显示产品右键菜单"""
        item = self.products_tree.identify_row(event.y)
        if item:
            self.products_tree.selection_set(item)
            self.products_context_menu.post(event.x_root, event.y_root)

    def recrawl_product_detail(self):
        """重新采集产品详情"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个产品")
            return

        item = selection[0]
        values = self.products_tree.item(item, "values")
        if not values:
            return

        product_name = values[0]
        product_url = values[3]

        if messagebox.askyesno("确认重新采集",
                             f"确定要重新采集产品 '{product_name}' 的详情吗？\n\n"
                             f"这将重新访问产品页面，采集SKU、价格等详细信息。"):

            self.log_message(f"开始重新采集产品详情: {product_name}")

            # 这里可以调用第三阶段的单个产品采集
            try:
                # 检查是否有新数据库
                if not self.has_new_database:
                    messagebox.showwarning("提示", "需要新数据库结构才能重新采集产品详情")
                    return

                # 导入第三阶段爬虫
                from stage3_crawler import Stage3Crawler

                crawler = Stage3Crawler("novoline_spider_v2.db")

                # 获取产品URL ID
                with sqlite3.connect("novoline_spider_v2.db") as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT id FROM product_urls WHERE product_url = ?', (product_url,))
                    result = cursor.fetchone()

                    if result:
                        product_url_id = result[0]

                        # 在新线程中采集
                        import threading
                        def crawl_worker():
                            success = crawler._crawl_single_product(product_url_id, product_url, product_name)
                            if success:
                                self.log_message(f"✅ 产品详情采集成功: {product_name}")
                            else:
                                self.log_message(f"❌ 产品详情采集失败: {product_name}")

                            # 刷新显示（在主线程中执行）
                            self.root.after(1000, self.refresh_products_data)

                        thread = threading.Thread(target=crawl_worker)
                        thread.daemon = True
                        thread.start()

                    else:
                        messagebox.showerror("错误", "未找到对应的产品记录")

            except ImportError:
                messagebox.showwarning("提示", "第三阶段功能模块未找到")
            except Exception as e:
                messagebox.showerror("错误", f"重新采集失败: {e}")

    def view_product_detail(self):
        """查看产品详情"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个产品")
            return

        item = selection[0]
        values = self.products_tree.item(item, "values")
        if not values:
            return

        product_name = values[0]
        product_url = values[3]

        # 检查是否有新数据库
        if not self.has_new_database:
            messagebox.showinfo("产品信息", f"产品: {product_name}\nURL: {product_url}\n\n提示: 需要新数据库结构才能查看详情")
            return

        try:
            with sqlite3.connect("novoline_spider_v2.db") as conn:
                cursor = conn.cursor()

                # 检查是否有product_details表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='product_details'")
                if cursor.fetchone():
                    # 查询产品详情（使用新的横向阶梯价格结构）
                    cursor.execute('''
                        SELECT pd.title, pd.sku, pd.nav_full, pd.category_name,
                               pd.tier1_price, pd.crawl_status, pd.error_message,
                               pd.all_tiers_combined
                        FROM product_urls pu
                        LEFT JOIN product_details pd ON pu.id = pd.product_url_id
                        WHERE pu.product_url = ?
                    ''', (product_url,))

                    result = cursor.fetchone()
                    if result and result[0]:  # 有详情数据
                        title, sku, nav_full, category_name, tier1_price, crawl_status, error_message, all_tiers_combined = result

                        detail_text = f"产品详细信息:\n\n"
                        detail_text += f"标题: {title or 'N/A'}\n"
                        detail_text += f"SKU: {sku or 'N/A'}\n"
                        detail_text += f"导航: {nav_full or 'N/A'}\n"
                        detail_text += f"分类: {category_name or 'N/A'}\n"
                        detail_text += f"起始价格: ${tier1_price or 'N/A'}\n"
                        detail_text += f"采集状态: {crawl_status or 'N/A'}\n"
                        if error_message:
                            detail_text += f"错误信息: {error_message}\n"
                        if all_tiers_combined:
                            detail_text += f"\n阶梯价格:\n{all_tiers_combined.replace('||', chr(10))}\n"
                        detail_text += f"\nURL: {product_url}"

                        messagebox.showinfo("产品详细信息", detail_text)
                    else:
                        messagebox.showinfo("产品信息", f"产品: {product_name}\nURL: {product_url}\n\n状态: 尚未采集详情")
                else:
                    messagebox.showinfo("产品信息", f"产品: {product_name}\nURL: {product_url}\n\n提示: 需要升级到新数据库结构才能查看详情")

        except Exception as e:
            messagebox.showerror("错误", f"查看产品详情失败: {e}")

    def mark_product_failed(self):
        """标记产品为失败"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个产品")
            return

        item = selection[0]
        values = self.products_tree.item(item, "values")
        product_name = values[0]

        if messagebox.askyesno("确认", f"确定要标记产品 '{product_name}' 为失败状态吗？"):
            # 实现标记失败的逻辑
            messagebox.showinfo("提示", "标记失败功能需要新数据库结构")

    def reset_product_crawl_status(self):
        """重置产品采集状态"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个产品")
            return

        item = selection[0]
        values = self.products_tree.item(item, "values")
        product_name = values[0]

        if messagebox.askyesno("确认", f"确定要重置产品 '{product_name}' 的采集状态吗？"):
            # 实现重置状态的逻辑
            messagebox.showinfo("提示", "重置状态功能需要新数据库结构")

    def start_stage3_new(self):
        """启动新的第三阶段"""
        try:
            # 检查是否有新数据库
            if not self.has_new_database:
                messagebox.showerror("错误", "需要新数据库结构才能使用第三阶段功能。\n请确保 novoline_spider_v2.db 存在。")
                return

            # 导入第三阶段爬虫
            from stage3_crawler import Stage3Crawler

            # 获取用户设置的线程数
            thread_count = self.thread_count_var.get()

            # 总是创建新的爬虫实例，确保线程数正确
            self.stage3_crawler = Stage3Crawler("novoline_spider_v2.db", max_workers=thread_count)
            self.stage3_crawler.set_callbacks(
                self.on_progress_update,
                self.on_error,
                self.on_completion
            )

            # 获取过滤选项
            filter_value = self.stage3_filter.get()
            filter_status = None if filter_value == "all" else filter_value

            # 启动第三阶段
            self.stage3_crawler.start_stage3(filter_status)

            # 更新按钮状态
            self.stage3_start_btn.config(state=tk.DISABLED)
            self.stage3_pause_btn.config(state=tk.NORMAL)
            self.stage3_stop_btn.config(state=tk.NORMAL)

            # 启动监控
            self.monitor_stage3_new()

            self.log_message("第三阶段已启动")

        except ImportError as e:
            messagebox.showerror("错误", f"第三阶段模块导入失败: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"启动第三阶段失败: {e}")

    def pause_stage3_new(self):
        """暂停第三阶段"""
        if hasattr(self, 'stage3_crawler'):
            self.stage3_crawler.pause()
            self.stage3_pause_btn.config(state=tk.DISABLED)
            self.stage3_resume_btn.config(state=tk.NORMAL)
            self.log_message("第三阶段已暂停")

    def resume_stage3_new(self):
        """继续第三阶段"""
        if hasattr(self, 'stage3_crawler'):
            self.stage3_crawler.resume()
            self.stage3_pause_btn.config(state=tk.NORMAL)
            self.stage3_resume_btn.config(state=tk.DISABLED)
            self.log_message("第三阶段已继续")

    def stop_stage3_new(self):
        """停止第三阶段"""
        if hasattr(self, 'stage3_crawler'):
            self.stage3_crawler.stop()
            self.reset_stage3_buttons()
            self.log_message("第三阶段已停止")

    def monitor_stage3_new(self):
        """监控第三阶段状态"""
        if hasattr(self, 'stage3_crawler') and self.stage3_crawler.is_running:
            # 刷新第三阶段数据显示
            self.refresh_prices_data()
            # 1秒后再次检查（提高刷新频率）
            self.root.after(1000, self.monitor_stage3_new)
        else:
            # 采集结束，重置按钮状态
            self.reset_stage3_buttons()
            self.refresh_prices_data()

            # 即使采集结束，也继续定期刷新数据（每5秒）
            if hasattr(self, 'stage3_auto_refresh_enabled') and self.stage3_auto_refresh_enabled:
                self.root.after(5000, self.auto_refresh_stage3_data)

    def reset_stage3_buttons(self):
        """重置第三阶段按钮状态"""
        self.stage3_start_btn.config(state=tk.NORMAL)
        self.stage3_pause_btn.config(state=tk.DISABLED)
        self.stage3_resume_btn.config(state=tk.DISABLED)
        self.stage3_stop_btn.config(state=tk.DISABLED)

    def retry_failed_products(self):
        """重试失败的产品"""
        try:
            if not self.has_new_database:
                messagebox.showwarning("提示", "需要新数据库结构才能重试失败的产品")
                return

            if messagebox.askyesno("确认", "确定要重试所有失败的产品吗？"):
                from stage3_crawler import Stage3Crawler

                if not hasattr(self, 'stage3_crawler'):
                    self.stage3_crawler = Stage3Crawler("novoline_spider_v2.db")
                    self.stage3_crawler.set_callbacks(
                        self.on_progress_update,
                        self.on_error,
                        self.on_completion
                    )

                self.stage3_crawler.start_stage3('failed')
                self.monitor_stage3_new()
                self.log_message("开始重试失败的产品")

        except Exception as e:
            messagebox.showerror("错误", f"重试失败: {e}")



    def create_products_preview(self, parent):
        """创建产品预览"""
        preview_frame = ttk.LabelFrame(parent, text="产品预览", padding="5")
        preview_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)

        # 创建树形视图 (移除SKU和价格，添加第三阶段状态)
        columns = ("name", "category", "stage3_status", "url")
        self.products_tree = ttk.Treeview(preview_frame, columns=columns, show="headings", height=8)

        # 设置列标题
        self.products_tree.heading("name", text="产品名称")
        self.products_tree.heading("category", text="所属分类")
        self.products_tree.heading("stage3_status", text="第三阶段状态")
        self.products_tree.heading("url", text="产品URL")

        # 设置列宽
        self.products_tree.column("name", width=250)
        self.products_tree.column("category", width=200)
        self.products_tree.column("stage3_status", width=120)
        self.products_tree.column("url", width=300)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar.set)

        self.products_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 添加右键菜单
        self.create_products_context_menu()

        # 添加刷新按钮
        refresh_btn = ttk.Button(preview_frame, text="刷新", command=self.refresh_products_data)
        refresh_btn.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))

    def create_prices_preview(self, parent):
        """创建第三阶段详情预览 - 显示真实数据库字段"""
        # 创建主框架
        main_frame = ttk.Frame(parent)
        main_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=1)

        # 创建Notebook来分别显示两个表
        self.stage3_notebook = ttk.Notebook(main_frame)
        self.stage3_notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 产品详情Tab（包含所有阶梯价格信息）
        self.create_product_details_tab()

    def create_product_details_tab(self):
        """创建产品详情Tab - 显示product_details表的真实字段"""
        details_frame = ttk.Frame(self.stage3_notebook)
        self.stage3_notebook.add(details_frame, text="产品详情 (product_details)")

        details_frame.columnconfigure(0, weight=1)
        details_frame.rowconfigure(0, weight=1)

        # 产品详情表格 - 显示所有6个阶梯价格字段
        details_columns = (
            "id", "product_url_id", "title", "sku",
            "nav_level1", "nav_level2", "nav_level3", "nav_full",
            "category_name", "category_url",
            "tier1_quantity", "tier1_price", "tier1_discount",
            "tier2_quantity", "tier2_price", "tier2_discount",
            "tier3_quantity", "tier3_price", "tier3_discount",
            "tier4_quantity", "tier4_price", "tier4_discount",
            "tier5_quantity", "tier5_price", "tier5_discount",
            "tier6_quantity", "tier6_price", "tier6_discount",
            "all_tiers_combined", "crawl_status", "crawl_attempts", "error_message"
        )

        self.product_details_tree = ttk.Treeview(details_frame, columns=details_columns, show="headings", height=10)

        # 设置列标题和宽度
        column_widths = {
            "id": 50, "product_url_id": 80, "title": 200, "sku": 100,
            "nav_level1": 120, "nav_level2": 120, "nav_level3": 120, "nav_full": 200,
            "category_name": 120, "category_url": 150,
            "tier1_quantity": 100, "tier1_price": 80, "tier1_discount": 80,
            "tier2_quantity": 100, "tier2_price": 80, "tier2_discount": 80,
            "tier3_quantity": 100, "tier3_price": 80, "tier3_discount": 80,
            "tier4_quantity": 100, "tier4_price": 80, "tier4_discount": 80,
            "tier5_quantity": 100, "tier5_price": 80, "tier5_discount": 80,
            "tier6_quantity": 100, "tier6_price": 80, "tier6_discount": 80,
            "all_tiers_combined": 300, "crawl_status": 80, "crawl_attempts": 80, "error_message": 200
        }

        for col in details_columns:
            self.product_details_tree.heading(col, text=col)
            self.product_details_tree.column(col, width=column_widths.get(col, 100))

        # 添加滚动条
        details_v_scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.product_details_tree.yview)
        details_h_scrollbar = ttk.Scrollbar(details_frame, orient=tk.HORIZONTAL, command=self.product_details_tree.xview)
        self.product_details_tree.configure(yscrollcommand=details_v_scrollbar.set, xscrollcommand=details_h_scrollbar.set)

        self.product_details_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        details_v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        details_h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # 添加第三阶段专用的刷新按钮
        refresh_frame = ttk.Frame(details_frame)
        refresh_frame.grid(row=2, column=0, sticky=tk.W, pady=(5, 0))

        ttk.Button(refresh_frame, text="刷新产品详情", command=self.refresh_prices_data).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(refresh_frame, text="刷新统计", command=self.refresh_stage3_stats).pack(side=tk.LEFT)



    def create_data_tree(self, parent):
        """创建数据查看表格"""
        tree_frame = ttk.Frame(parent)
        tree_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)

        # 创建树形视图
        self.data_tree = ttk.Treeview(tree_frame, show="headings", height=15)

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.data_tree.xview)
        self.data_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        self.data_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
    
    def start_stage(self, stage):
        """开始指定阶段的爬取"""
        restart = False

        # 根据阶段获取重新开始选项
        if stage == "categories":
            restart = self.stage1_restart_var.get()
        elif stage == "products":
            restart = self.stage2_restart_var.get()
        elif stage == "prices":
            restart = self.stage3_restart_var.get()

        if restart:
            result = messagebox.askyesno("确认", f"确定要重新开始{stage}阶段吗？这将清空相关数据。")
            if not result:
                return

        self.spider.start_crawling(stage, restart)
        self.update_all_button_states(running=True, current_stage=stage)
        self.log_message(f"开始{stage}阶段爬取，重新开始: {restart}")

    def stop_crawling(self):
        """停止爬取"""
        self.spider.stop_crawling()
        self.update_all_button_states(running=False)
        self.log_message("爬取已停止")

    def pause_crawling(self):
        """暂停爬取"""
        self.spider.pause_crawling()
        self.stage2_pause_btn.config(state=tk.DISABLED)
        self.stage2_resume_btn.config(state=tk.NORMAL)
        self.log_message("爬取已暂停")

    def resume_crawling(self):
        """继续爬取"""
        self.spider.resume_crawling()
        self.stage2_pause_btn.config(state=tk.NORMAL)
        self.stage2_resume_btn.config(state=tk.DISABLED)
        self.log_message("爬取已继续")
    
    def update_all_button_states(self, running=False, current_stage=None):
        """更新所有按钮状态"""
        if running:
            # 禁用所有开始按钮，启用对应的停止按钮
            self.stage1_start_btn.config(state=tk.DISABLED)
            self.stage2_start_btn.config(state=tk.DISABLED)
            self.stage3_start_btn.config(state=tk.DISABLED)

            if current_stage == "categories":
                self.stage1_stop_btn.config(state=tk.NORMAL)
                self.stage1_status_label.config(text="运行中", foreground="blue")
            elif current_stage == "products":
                self.stage2_stop_btn.config(state=tk.NORMAL)
                self.stage2_pause_btn.config(state=tk.NORMAL)
                self.stage2_resume_btn.config(state=tk.DISABLED)
                self.stage2_status_label.config(text="运行中", foreground="blue")
            elif current_stage == "prices":
                self.stage3_stop_btn.config(state=tk.NORMAL)
                self.stage3_status_label.config(text="运行中", foreground="blue")
        else:
            # 启用所有开始按钮，禁用所有停止按钮
            self.stage1_start_btn.config(state=tk.NORMAL)
            self.stage2_start_btn.config(state=tk.NORMAL)
            self.stage3_start_btn.config(state=tk.NORMAL)

            self.stage1_stop_btn.config(state=tk.DISABLED)
            self.stage2_stop_btn.config(state=tk.DISABLED)
            self.stage2_pause_btn.config(state=tk.DISABLED)
            self.stage2_resume_btn.config(state=tk.DISABLED)
            self.stage3_stop_btn.config(state=tk.DISABLED)

            self.stage1_status_label.config(text="就绪", foreground="green")
            self.stage2_status_label.config(text="就绪", foreground="green")
            self.stage3_status_label.config(text="就绪", foreground="green")
    
    def on_progress_update(self, stage, message):
        """进度更新回调"""
        self.root.after(0, lambda: self._update_progress(stage, message))
    
    def _update_progress(self, stage, message):
        """更新进度显示"""
        self.log_message(f"[{stage}] {message}")

        # 更新对应阶段的状态标签
        if stage == "categories":
            self.stage1_status_label.config(text="进行中", foreground="blue")
        elif stage == "products":
            self.stage2_status_label.config(text="进行中", foreground="blue")
            # 更新详细进度信息
            if hasattr(self, 'stage2_detail_text'):
                self.stage2_detail_text.set(message)
            # 第二阶段进行中时，定期刷新数据显示
            if "已处理" in message or "个产品" in message:
                self.refresh_categories_data()
                self.refresh_products_data()
        elif stage == "prices":
            self.stage3_status_label.config(text="进行中", foreground="blue")
    
    def on_error(self, message, stack_trace=None):
        """错误回调"""
        self.root.after(0, lambda: self._handle_error(message, stack_trace))
    
    def _handle_error(self, message, stack_trace):
        """处理错误"""
        self.log_message(f"错误: {message}", "ERROR")
        if stack_trace:
            self.log_message(f"详细信息: {stack_trace}", "DEBUG")
    
    def on_completion(self, message):
        """完成回调"""
        self.root.after(0, lambda: self._handle_completion(message))
    
    def _handle_completion(self, message):
        """处理完成"""
        self.update_all_button_states(running=False)
        self.log_message(f"完成: {message}", "SUCCESS")
        messagebox.showinfo("完成", message)

        # 刷新数据显示
        self.refresh_all_data()

    def refresh_all_data(self):
        """刷新所有数据显示"""
        self.refresh_categories_data()
        self.refresh_products_data()
        self.refresh_prices_data()
        self.refresh_data_view()

    def refresh_categories_data(self):
        """刷新目录数据显示"""
        try:
            # 清空现有数据
            for item in self.categories_tree.get_children():
                self.categories_tree.delete(item)

            # 从数据库获取数据
            db_path = "novoline_spider_v2.db" if self.has_new_database else self.db.db_path
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                # 获取主目录及其产品统计
                cursor.execute('''
                    SELECT
                        mc.id,
                        mc.category_name,
                        mc.category_url,
                        COUNT(DISTINCT sc.id) as sub_count,
                        COUNT(pu.id) as total_products
                    FROM main_categories mc
                    LEFT JOIN sub_categories sc ON mc.id = sc.main_category_id
                    LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                    GROUP BY mc.id, mc.category_name, mc.category_url
                    ORDER BY mc.id
                ''')
                main_categories = cursor.fetchall()

                for main_id, main_name, main_url, sub_count, total_products in main_categories:
                    # 插入主目录，显示产品总数
                    main_display = f"{main_name} ({total_products}个产品)"
                    main_item = self.categories_tree.insert("", "end", text="主目录",
                                                           values=(main_display, main_url, f"{sub_count}个子目录"))

                    # 获取子目录及其产品统计
                    cursor.execute('''
                        SELECT
                            sc.sub_category_name,
                            sc.sub_category_url,
                            sc.product_count,
                            sc.page_count,
                            COUNT(pu.id) as actual_count,
                            sc.last_crawled_at
                        FROM sub_categories sc
                        LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                        WHERE sc.main_category_id = ?
                        GROUP BY sc.id, sc.sub_category_name, sc.sub_category_url,
                                 sc.product_count, sc.page_count, sc.last_crawled_at
                        ORDER BY sc.id
                    ''', (main_id,))
                    sub_categories = cursor.fetchall()

                    for sub_name, sub_url, recorded_count, page_count, actual_count, last_crawled in sub_categories:
                        # 显示产品数量和页面数
                        if actual_count > 0:
                            sub_display = f"{sub_name} ({actual_count}个产品"
                            if page_count:
                                sub_display += f", {page_count}页"
                            sub_display += ")"
                            status = "✅" if recorded_count == actual_count else "⚠️"
                        else:
                            sub_display = f"{sub_name} (未采集)"
                            status = "⏳"

                        self.categories_tree.insert(main_item, "end", text="子目录",
                                                   values=(f"{status} {sub_display}", sub_url,
                                                          last_crawled or "未采集"))
        except Exception as e:
            self.log_message(f"刷新目录数据失败: {e}", "ERROR")

    def refresh_products_data(self):
        """刷新产品数据显示"""
        try:
            # 清空现有数据
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            # 从数据库获取数据，包含第三阶段状态
            db_path = "novoline_spider_v2.db" if self.has_new_database else self.db.db_path
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                # 检查是否有新的数据库结构
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='product_details'")
                has_stage3_table = cursor.fetchone() is not None

                if has_stage3_table:
                    # 新数据库结构，包含第三阶段状态
                    cursor.execute('''
                        SELECT
                            pu.product_name,
                            mc.category_name || ' > ' || sc.sub_category_name as category,
                            CASE
                                WHEN pd.crawl_status IS NULL THEN '⏳ 待采集'
                                WHEN pd.crawl_status = 'pending' THEN '⏳ 待采集'
                                WHEN pd.crawl_status = 'retrying' THEN '🔄 采集中'
                                WHEN pd.crawl_status = 'success' THEN '✅ 已完成'
                                WHEN pd.crawl_status = 'failed' THEN '❌ 失败'
                                ELSE '❓ 未知'
                            END as stage3_status,
                            pu.product_url
                        FROM product_urls pu
                        JOIN sub_categories sc ON pu.sub_category_id = sc.id
                        JOIN main_categories mc ON sc.main_category_id = mc.id
                        LEFT JOIN product_details pd ON pu.id = pd.product_url_id
                        ORDER BY mc.id, sc.id, pu.id
                        LIMIT 200
                    ''')
                else:
                    # 旧数据库结构，显示待采集状态
                    cursor.execute('''
                        SELECT
                            pu.product_name,
                            mc.category_name || ' > ' || sc.sub_category_name as category,
                            '⏳ 待采集' as stage3_status,
                            pu.product_url
                        FROM product_urls pu
                        JOIN sub_categories sc ON pu.sub_category_id = sc.id
                        JOIN main_categories mc ON sc.main_category_id = mc.id
                        ORDER BY mc.id, sc.id, pu.id
                        LIMIT 200
                    ''')

                products = cursor.fetchall()

                for product_name, category, stage3_status, product_url in products:
                    self.products_tree.insert("", "end", values=(
                        product_name,
                        category,
                        stage3_status,
                        product_url
                    ))
        except Exception as e:
            self.log_message(f"刷新产品数据失败: {e}", "ERROR")

    def refresh_prices_data(self):
        """刷新第三阶段详情数据显示 - 显示真实数据库字段"""
        try:
            # 检查是否有新数据库
            if not self.has_new_database:
                return

            # 刷新产品详情表（包含所有阶梯价格信息）
            self.refresh_product_details_data()

            # 同时刷新统计信息
            self.refresh_stage3_stats()

        except Exception as e:
            self.log_message(f"刷新第三阶段数据失败: {e}", "ERROR")

    def refresh_stage3_stats(self):
        """刷新第三阶段统计信息"""
        try:
            if not self.has_new_database:
                return

            # 获取第三阶段统计信息
            if hasattr(self, 'stage3_crawler'):
                stats = self.stage3_crawler.get_stage3_statistics()
            else:
                from stage3_crawler import Stage3Crawler
                temp_crawler = Stage3Crawler("novoline_spider_v2.db")
                stats = temp_crawler.get_stage3_statistics()

            # 更新统计显示
            stats_text = f"总产品: {stats.get('total_products', 0)} | "
            stats_text += f"成功: {stats.get('success', 0)} | "
            stats_text += f"失败: {stats.get('failed', 0)} | "
            stats_text += f"待采集: {stats.get('pending', 0)} | "
            stats_text += f"重试中: {stats.get('retrying', 0)}"

            # 更新进度显示
            if hasattr(self, 'stage3_progress_var'):
                total = stats.get('total_products', 0)
                completed = stats.get('success', 0) + stats.get('failed', 0)
                if total > 0:
                    progress = (completed / total) * 100
                    self.stage3_progress_var.set(progress)
                    self.stage3_progress_label.config(text=f"进度: {completed}/{total} ({progress:.1f}%)")

            # 只在调试模式下记录日志，避免日志过多
            if hasattr(self, 'debug_mode') and self.debug_mode:
                self.log_message(f"统计已刷新: {stats_text}")

        except Exception as e:
            self.log_message(f"刷新统计失败: {e}", "ERROR")

    def auto_refresh_stage3_data(self):
        """自动刷新第三阶段数据"""
        try:
            # 检查自动刷新是否启用
            if (hasattr(self, 'stage3_auto_refresh_enabled') and self.stage3_auto_refresh_enabled and
                hasattr(self, 'auto_refresh_var') and self.auto_refresh_var.get()):

                # 静默刷新数据（不显示日志）
                self.refresh_prices_data_silent()

                # 继续下一次自动刷新（10秒后）
                self.root.after(10000, self.auto_refresh_stage3_data)

        except Exception as e:
            # 静默处理错误，避免干扰用户
            pass

    def refresh_prices_data_silent(self):
        """静默刷新第三阶段数据（不显示日志）"""
        try:
            if not self.has_new_database:
                return

            # 刷新产品详情表（包含所有阶梯价格信息）
            self.refresh_product_details_data_silent()

            # 刷新统计信息
            self.refresh_stage3_stats()

        except Exception as e:
            # 静默处理错误
            pass

    def refresh_product_details_data_silent(self):
        """静默刷新产品详情表数据"""
        try:
            # 清空现有数据
            for item in self.product_details_tree.get_children():
                self.product_details_tree.delete(item)

            # 从数据库获取产品详情数据
            with sqlite3.connect("novoline_spider_v2.db") as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT
                        pd.id, pd.product_url_id, pd.title, pd.sku,
                        pd.nav_level1, pd.nav_level2, pd.nav_level3, pd.nav_full,
                        pd.category_name, pd.category_url,
                        pd.tier1_quantity, pd.tier1_price, pd.tier1_discount,
                        pd.tier2_quantity, pd.tier2_price, pd.tier2_discount,
                        pd.tier3_quantity, pd.tier3_price, pd.tier3_discount,
                        pd.tier4_quantity, pd.tier4_price, pd.tier4_discount,
                        pd.tier5_quantity, pd.tier5_price, pd.tier5_discount,
                        pd.tier6_quantity, pd.tier6_price, pd.tier6_discount,
                        pd.all_tiers_combined, pd.crawl_status, pd.crawl_attempts, pd.error_message
                    FROM product_details pd
                    ORDER BY pd.id
                    LIMIT 100
                ''')
                details = cursor.fetchall()

                for row in details:
                    # 处理None值显示
                    display_row = []
                    for value in row:
                        if value is None:
                            display_row.append("NULL")
                        elif isinstance(value, str) and len(value) > 50:
                            # 长文本截断显示
                            display_row.append(value[:47] + "...")
                        else:
                            display_row.append(str(value))

                    self.product_details_tree.insert("", "end", values=display_row)

        except Exception as e:
            # 静默处理错误
            pass

    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        self.stage3_auto_refresh_enabled = self.auto_refresh_var.get()

        if self.stage3_auto_refresh_enabled:
            self.log_message("✅ 第三阶段自动刷新已启用（每10秒刷新一次）")
            # 立即开始自动刷新
            self.root.after(1000, self.auto_refresh_stage3_data)
        else:
            self.log_message("⏸️ 第三阶段自动刷新已禁用")

    def refresh_product_details_data(self):
        """刷新产品详情表数据"""
        try:
            # 清空现有数据
            for item in self.product_details_tree.get_children():
                self.product_details_tree.delete(item)

            # 从数据库获取产品详情数据 - 显示所有真实字段
            with sqlite3.connect("novoline_spider_v2.db") as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT
                        pd.id, pd.product_url_id, pd.title, pd.sku,
                        pd.nav_level1, pd.nav_level2, pd.nav_level3, pd.nav_full,
                        pd.category_name, pd.category_url,
                        pd.tier1_quantity, pd.tier1_price, pd.tier1_discount,
                        pd.tier2_quantity, pd.tier2_price, pd.tier2_discount,
                        pd.tier3_quantity, pd.tier3_price, pd.tier3_discount,
                        pd.tier4_quantity, pd.tier4_price, pd.tier4_discount,
                        pd.tier5_quantity, pd.tier5_price, pd.tier5_discount,
                        pd.tier6_quantity, pd.tier6_price, pd.tier6_discount,
                        pd.all_tiers_combined, pd.crawl_status, pd.crawl_attempts, pd.error_message
                    FROM product_details pd
                    ORDER BY pd.id
                    LIMIT 100
                ''')
                details = cursor.fetchall()

                for row in details:
                    # 处理None值显示
                    display_row = []
                    for value in row:
                        if value is None:
                            display_row.append("NULL")
                        elif isinstance(value, str) and len(value) > 50:
                            # 长文本截断显示
                            display_row.append(value[:47] + "...")
                        else:
                            display_row.append(str(value))

                    self.product_details_tree.insert("", "end", values=display_row)

        except Exception as e:
            self.log_message(f"刷新产品详情数据失败: {e}", "ERROR")



    def refresh_data_view(self):
        """刷新数据查看Tab的内容"""
        data_type = self.data_type_var.get()

        try:
            # 清空现有数据
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)

            # 根据数据类型设置列
            if data_type == "main_categories":
                # 动态检查表结构
                with sqlite3.connect(self.db.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA table_info(main_categories)")
                    table_columns = [row[1] for row in cursor.fetchall()]

                # 根据实际表结构确定显示的列
                if "created_at" in table_columns:
                    columns = ("id", "category_name", "category_url", "icon_url", "created_at")
                    query = "SELECT id, category_name, category_url, icon_url, created_at FROM main_categories ORDER BY id"
                else:
                    columns = ("id", "category_name", "category_url", "icon_url", "updated_at")
                    query = "SELECT id, category_name, category_url, icon_url, updated_at FROM main_categories ORDER BY id"

                self.data_tree["columns"] = columns
                for col in columns:
                    self.data_tree.heading(col, text=col.replace("_", " ").title())
                    self.data_tree.column(col, width=150)

                # 获取数据
                with sqlite3.connect(self.db.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute(query)
                    rows = cursor.fetchall()

                    for row in rows:
                        self.data_tree.insert("", "end", values=row)

            elif data_type == "sub_categories":
                # 动态检查表结构
                with sqlite3.connect(self.db.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA table_info(sub_categories)")
                    table_columns = [row[1] for row in cursor.fetchall()]

                # 根据实际表结构确定显示的列
                if "created_at" in table_columns:
                    columns = ("id", "main_category_id", "sub_category_name", "sub_category_url", "created_at")
                    query = "SELECT id, main_category_id, sub_category_name, sub_category_url, created_at FROM sub_categories ORDER BY main_category_id, id"
                else:
                    columns = ("id", "main_category_id", "sub_category_name", "sub_category_url", "updated_at")
                    query = "SELECT id, main_category_id, sub_category_name, sub_category_url, updated_at FROM sub_categories ORDER BY main_category_id, id"

                self.data_tree["columns"] = columns
                for col in columns:
                    self.data_tree.heading(col, text=col.replace("_", " ").title())
                    self.data_tree.column(col, width=150)

                # 获取数据
                with sqlite3.connect(self.db.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute(query)
                    rows = cursor.fetchall()

                    for row in rows:
                        self.data_tree.insert("", "end", values=row)

            elif data_type == "product_urls":
                # 检查数据库表结构，动态确定列
                db_path = "novoline_spider_v2.db" if self.has_new_database else self.db.db_path
                with sqlite3.connect(db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA table_info(product_urls)")
                    table_columns = [row[1] for row in cursor.fetchall()]

                # 根据实际表结构确定显示的列
                if "created_at" in table_columns:
                    columns = ("id", "sub_category_id", "product_name", "product_url", "created_at")
                    query = "SELECT id, sub_category_id, product_name, product_url, created_at FROM product_urls ORDER BY id LIMIT 500"
                else:
                    columns = ("id", "sub_category_id", "product_name", "product_url", "updated_at")
                    query = "SELECT id, sub_category_id, product_name, product_url, updated_at FROM product_urls ORDER BY id LIMIT 500"

                self.data_tree["columns"] = columns
                for col in columns:
                    self.data_tree.heading(col, text=col.replace("_", " ").title())
                    self.data_tree.column(col, width=150)

                # 获取数据
                with sqlite3.connect(db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute(query)
                    rows = cursor.fetchall()

                    for row in rows:
                        self.data_tree.insert("", "end", values=row)

            elif data_type == "product_details":
                if self.has_new_database:
                    # 新数据库结构 - 显示product_details表（横向阶梯价格）
                    columns = ("id", "product_url_id", "title", "sku", "nav_level1", "nav_level2", "nav_level3",
                              "nav_full", "category_name", "tier1_price", "crawl_status", "error_message")
                    self.data_tree["columns"] = columns
                    for col in columns:
                        self.data_tree.heading(col, text=col.replace("_", " ").title())
                        self.data_tree.column(col, width=120)

                    # 获取数据
                    with sqlite3.connect("novoline_spider_v2.db") as conn:
                        cursor = conn.cursor()
                        cursor.execute('''
                            SELECT id, product_url_id, title, sku, nav_level1, nav_level2, nav_level3,
                                   nav_full, category_name, tier1_price, crawl_status, error_message
                            FROM product_details
                            ORDER BY id LIMIT 500
                        ''')
                        rows = cursor.fetchall()

                        for row in rows:
                            # 处理长文本显示
                            display_row = []
                            for i, value in enumerate(row):
                                if value is None:
                                    display_row.append("NULL")
                                elif isinstance(value, str) and len(value) > 30:
                                    display_row.append(value[:27] + "...")
                                else:
                                    display_row.append(str(value))
                            self.data_tree.insert("", "end", values=display_row)
                else:
                    # 旧数据库没有product_details表
                    self.data_tree["columns"] = ("message",)
                    self.data_tree.heading("message", text="提示")
                    self.data_tree.column("message", width=400)
                    self.data_tree.insert("", "end", values=("需要新数据库结构才能查看产品详情",))

            elif data_type == "tier_prices":
                if self.has_new_database:
                    # 新数据库结构
                    columns = ("id", "product_detail_id", "quantity_min", "quantity_max", "quantity_display",
                              "unit_price", "total_price", "currency", "discount_percent", "discount_amount", "raw_data")
                    self.data_tree["columns"] = columns
                    for col in columns:
                        self.data_tree.heading(col, text=col.replace("_", " ").title())
                        self.data_tree.column(col, width=100)

                    # 获取数据
                    with sqlite3.connect("novoline_spider_v2.db") as conn:
                        cursor = conn.cursor()
                        cursor.execute('''
                            SELECT id, product_detail_id, quantity_min, quantity_max, quantity_display,
                                   unit_price, total_price, currency, discount_percent, discount_amount, raw_data
                            FROM tier_prices
                            ORDER BY product_detail_id, quantity_min
                            LIMIT 500
                        ''')
                        rows = cursor.fetchall()

                        for row in rows:
                            # 处理长文本显示
                            display_row = []
                            for value in row:
                                if value is None:
                                    display_row.append("NULL")
                                elif isinstance(value, str) and len(value) > 50:
                                    display_row.append(value[:47] + "...")
                                else:
                                    display_row.append(str(value))
                            self.data_tree.insert("", "end", values=display_row)
                else:
                    # 旧数据库结构
                    columns = ("id", "product_id", "quantity_min", "quantity_max", "price", "currency")
                    self.data_tree["columns"] = columns
                    for col in columns:
                        self.data_tree.heading(col, text=col.replace("_", " ").title())
                        self.data_tree.column(col, width=100)

                    # 获取数据 - 使用横向阶梯价格结构
                    with sqlite3.connect(self.db.db_path) as conn:
                        cursor = conn.cursor()
                        cursor.execute('''
                            SELECT id, product_url_id, title, tier1_quantity, tier1_price, tier1_discount,
                                   tier2_quantity, tier2_price, tier2_discount, all_tiers_combined
                            FROM product_details
                            WHERE crawl_status = 'success'
                            ORDER BY id LIMIT 500
                        ''')
                        rows = cursor.fetchall()

                        for row in rows:
                            self.data_tree.insert("", "end", values=row)

        except Exception as e:
            self.log_message(f"刷新数据查看失败: {e}", "ERROR")

    def on_data_type_changed(self, event):
        """数据类型改变时的处理"""
        self.refresh_data_view()

    def export_log(self):
        """导出日志"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"日志已导出到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出日志失败: {e}")
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] [{level}] {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def start_status_update_thread(self):
        """启动状态更新线程"""
        def update_status():
            while True:
                try:
                    status = self.spider.get_status()
                    self.root.after(0, lambda: self._update_status_display(status))
                    time.sleep(2)  # 每2秒更新一次
                except Exception as e:
                    print(f"状态更新错误: {e}")
                    time.sleep(5)
        
        thread = threading.Thread(target=update_status, daemon=True)
        thread.start()
    
    def _update_status_display(self, status):
        """更新状态显示"""
        # 更新统计信息
        stats = status.get('statistics', {})
        stats_text = (f"主目录: {stats.get('main_categories', 0)} | "
                     f"子目录: {stats.get('sub_categories', 0)} | "
                     f"产品: {stats.get('products', 0)} | "
                     f"价格记录: {stats.get('tier_prices', 0)} | "
                     f"错误: {stats.get('errors', 0)}")
        self.stats_text.set(stats_text)

        # 更新进度信息
        progress_data = status.get('progress', [])
        for progress in progress_data:
            stage = progress['stage']
            total = progress['total_items']
            completed = progress['completed_items']

            if stage == "categories":
                self.stage1_progress_text.set(f"{completed}/{total}")
                if total > 0:
                    self.stage1_progress_bar['value'] = (completed / total) * 100
            elif stage == "products":
                self.stage2_progress_text.set(f"{completed}/{total}")
                if total > 0:
                    self.stage2_progress_bar['value'] = (completed / total) * 100
            elif stage == "prices":
                self.stage3_progress_text.set(f"{completed}/{total}")
                if total > 0:
                    self.stage3_progress_bar['value'] = (completed / total) * 100

        # 更新按钮状态
        if not status['is_running']:
            if self.stage1_start_btn['state'] != tk.NORMAL:
                self.update_all_button_states(running=False)
    
    def run(self):
        """运行GUI"""
        self.log_message("Novoline网站爬虫已启动")

        # 延迟初始化数据显示，确保界面完全创建
        self.root.after(1000, self.refresh_all_data)

        self.root.mainloop()

def main():
    """主函数"""
    app = SpiderGUI()
    app.run()

if __name__ == "__main__":
    main()
