#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试图片提取问题
"""

from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin

def analyze_image_extraction():
    """分析图片提取问题"""
    print("🔍 分析图片提取问题...")
    
    # 读取HTML文件
    with open('docs/6.txt', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    base_url = "https://novolinepromo.com/"
    
    print(f"\n📋 HTML分析结果:")
    print(f"   HTML长度: {len(html_content):,} 字符")
    
    # 分析图片结构
    print(f"\n🖼️ 图片结构分析:")
    
    # 1. 查找所有img标签
    all_imgs = soup.find_all('img')
    print(f"   总img标签数: {len(all_imgs)}")
    
    # 2. 分析现有选择器
    current_selectors = [
        '.woocommerce-product-gallery img',
        '.product-images img', 
        '.product-gallery img',
        '.wp-post-image',
        '.attachment-woocommerce_single',
        '.product-image img'
    ]
    
    print(f"\n📊 当前选择器测试:")
    found_by_selector = {}
    
    for selector in current_selectors:
        images = soup.select(selector)
        found_by_selector[selector] = len(images)
        print(f"   {selector}: {len(images)} 张图片")
        
        if images:
            for i, img in enumerate(images[:3], 1):  # 只显示前3张
                src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                print(f"      图片{i}: {src}")
    
    # 3. 分析实际的图片容器结构
    print(f"\n🔍 实际图片容器分析:")
    
    # 查找包含图片的主要容器
    containers = [
        soup.find('div', class_='images'),
        soup.find('div', class_='nickx_product_images_with_video'),
        soup.find('div', class_='nickx-slider'),
        soup.find('div', class_='nswiper-wrapper')
    ]
    
    for i, container in enumerate(containers, 1):
        if container:
            imgs_in_container = container.find_all('img')
            print(f"   容器{i}: {container.get('class')} - {len(imgs_in_container)} 张图片")
            
            if imgs_in_container:
                for j, img in enumerate(imgs_in_container[:2], 1):
                    src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                    alt = img.get('alt', 'No alt')
                    print(f"      图片{j}: {src}")
                    print(f"              alt: {alt}")
    
    # 4. 提取所有可能的产品图片URL
    print(f"\n🎯 提取所有产品图片:")
    
    def is_valid_product_image(url: str) -> bool:
        """判断是否是有效的产品图片"""
        if not url:
            return False
            
        invalid_patterns = ['logo', 'icon', 'button', 'arrow', 'placeholder', 'loading', 'spinner']
        url_lower = url.lower()
        
        # 检查文件扩展名
        valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        if not any(ext in url_lower for ext in valid_extensions):
            return False
        
        # 检查是否包含无效模式
        if any(pattern in url_lower for pattern in invalid_patterns):
            return False
        
        return True
    
    # 提取所有图片URL
    all_product_images = set()
    
    for img in all_imgs:
        # 尝试多种src属性
        src_attrs = ['src', 'data-src', 'data-lazy-src', 'data-zoom-image', 'data-o_src']
        
        for attr in src_attrs:
            img_url = img.get(attr)
            if img_url:
                full_url = urljoin(base_url, img_url)
                if is_valid_product_image(full_url):
                    # 检查是否是产品图片（包含产品名称关键词）
                    if any(keyword in full_url.lower() for keyword in ['creative-outdoor', 'silicone', 'folding', 'water', 'bottle']):
                        all_product_images.add(full_url)
    
    print(f"   找到的产品图片数量: {len(all_product_images)}")
    for i, url in enumerate(sorted(all_product_images), 1):
        print(f"      图片{i}: {url}")
    
    # 5. 分析为什么现有选择器没有找到图片
    print(f"\n❓ 问题分析:")
    
    # 检查是否有woocommerce-product-gallery类
    gallery = soup.find(class_='woocommerce-product-gallery')
    if gallery:
        print(f"   ✅ 找到woocommerce-product-gallery容器")
        gallery_imgs = gallery.find_all('img')
        print(f"   📊 容器内图片数量: {len(gallery_imgs)}")
    else:
        print(f"   ❌ 未找到woocommerce-product-gallery容器")
    
    # 检查实际的图片容器类名
    main_container = soup.find('div', class_='images')
    if main_container:
        print(f"   ✅ 找到主图片容器: {main_container.get('class')}")
        
        # 查找所有子容器
        child_containers = main_container.find_all('div', class_=True)
        print(f"   📊 子容器类名:")
        for container in child_containers[:5]:  # 只显示前5个
            classes = container.get('class', [])
            if classes:
                print(f"      - {' '.join(classes)}")
    
    return len(all_product_images)

def suggest_improved_selectors():
    """建议改进的选择器"""
    print(f"\n💡 建议的改进选择器:")
    
    improved_selectors = [
        # 现有选择器
        '.woocommerce-product-gallery img',
        '.product-images img',
        '.product-gallery img',
        '.wp-post-image',
        '.attachment-woocommerce_single',
        
        # 新增选择器（基于实际HTML结构）
        '.images img',  # 主图片容器
        '.nickx_product_images_with_video img',  # 特定的图片视频容器
        '.nickx-slider img',  # 滑动器中的图片
        '.nswiper-slide img',  # Swiper滑动项中的图片
        '.nswiper-wrapper img',  # Swiper包装器中的图片
        'div[class*="slider"] img',  # 任何包含slider的div中的图片
        'div[class*="swiper"] img',  # 任何包含swiper的div中的图片
        'img[alt*="Creative Outdoor"]',  # 通过alt属性匹配产品名称
        'img[src*="novo6789C"]',  # 通过src匹配产品SKU
    ]
    
    for i, selector in enumerate(improved_selectors, 1):
        print(f"   {i:2d}. {selector}")
    
    return improved_selectors

def create_fix_patch():
    """创建修复补丁"""
    print(f"\n🔧 创建图片提取修复补丁...")
    
    patch_code = '''
# 在stage4_image_crawler.py中替换_extract_image_urls方法

def _extract_image_urls(self, soup: BeautifulSoup, base_url: str) -> List[str]:
    """
    提取产品图片URL（增强版本）
    
    Args:
        soup: BeautifulSoup对象
        base_url: 基础URL
        
    Returns:
        图片URL列表
    """
    image_urls = []
    
    try:
        # 增强的图片选择器（基于实际HTML结构分析）
        image_selectors = [
            # 标准WooCommerce选择器
            '.woocommerce-product-gallery img',
            '.product-images img',
            '.product-gallery img',
            '.wp-post-image',
            '.attachment-woocommerce_single',
            
            # 新增的选择器（针对Novoline网站的特殊结构）
            '.images img',  # 主图片容器
            '.nickx_product_images_with_video img',  # 特定的图片视频容器
            '.nickx-slider img',  # 滑动器中的图片
            '.nswiper-slide img',  # Swiper滑动项中的图片
            '.nswiper-wrapper img',  # Swiper包装器中的图片
            'div[class*="slider"] img',  # 任何包含slider的div中的图片
            'div[class*="swiper"] img',  # 任何包含swiper的div中的图片
        ]
        
        found_images = set()  # 使用set避免重复
        
        for selector in image_selectors:
            images = soup.select(selector)
            for img in images:
                # 尝试多种src属性
                src_attrs = ['src', 'data-src', 'data-lazy-src', 'data-zoom-image', 'data-o_src']
                
                for attr in src_attrs:
                    img_url = img.get(attr)
                    if img_url:
                        # 转换为绝对URL
                        full_url = urljoin(base_url, img_url)
                        
                        # 过滤掉明显不是产品图片的URL
                        if self._is_valid_product_image(full_url):
                            found_images.add(full_url)
                        break  # 找到一个有效URL就跳出
        
        # 如果通过标准选择器没找到图片，尝试通过产品信息匹配
        if not found_images:
            # 通过alt属性或src路径匹配产品相关图片
            all_imgs = soup.find_all('img')
            for img in all_imgs:
                alt = img.get('alt', '').lower()
                src = img.get('src', '').lower()
                
                # 检查是否包含产品相关关键词
                if any(keyword in alt or keyword in src for keyword in ['product', 'novo', 'creative', 'outdoor']):
                    for attr in ['src', 'data-src', 'data-lazy-src', 'data-zoom-image']:
                        img_url = img.get(attr)
                        if img_url:
                            full_url = urljoin(base_url, img_url)
                            if self._is_valid_product_image(full_url):
                                found_images.add(full_url)
                            break
        
        # 转换为列表并限制数量
        image_urls = list(found_images)[:10]  # 最多10张图片
        
    except Exception as e:
        self._notify_error(f"提取图片URL失败: {e}")
    
    return image_urls
'''
    
    with open("image_extraction_patch.py", "w", encoding="utf-8") as f:
        f.write(patch_code)
    
    print(f"   ✅ 补丁文件已创建: image_extraction_patch.py")

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 图片提取问题调试工具")
    print("=" * 60)
    print("分析为什么图片采集失败")
    print()
    
    # 分析图片提取
    image_count = analyze_image_extraction()
    
    # 建议改进选择器
    improved_selectors = suggest_improved_selectors()
    
    # 创建修复补丁
    create_fix_patch()
    
    print("\n" + "=" * 60)
    print("🎯 调试结果总结")
    print("=" * 60)
    
    print(f"✅ 实际找到的产品图片: {image_count} 张")
    print(f"✅ 建议的选择器: {len(improved_selectors)} 个")
    print(f"✅ 修复补丁已创建")
    
    print(f"\n🚀 下一步:")
    print(f"   1. 查看image_extraction_patch.py中的修复代码")
    print(f"   2. 手动应用补丁到stage4_image_crawler.py")
    print(f"   3. 重新测试图片采集功能")

if __name__ == "__main__":
    main()
