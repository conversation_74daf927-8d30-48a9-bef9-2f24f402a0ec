#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的横向阶梯价格结构
"""

import os
import sqlite3

def create_test_database():
    """创建测试数据库"""
    db_path = "test_horizontal_tiers.db"
    
    if os.path.exists(db_path):
        os.remove(db_path)
    
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()
        
        # 创建product_urls表
        cursor.execute('''
            CREATE TABLE product_urls (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sub_category_id INTEGER,
                product_name TEXT NOT NULL,
                product_url TEXT NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建新的product_details表
        cursor.execute('''
            CREATE TABLE product_details (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_url_id INTEGER NOT NULL UNIQUE,
                
                -- 基本信息
                title TEXT,
                sku TEXT,
                product_image_url TEXT,
                short_description TEXT,
                
                -- 导航信息
                nav_level1 TEXT,
                nav_level2 TEXT,
                nav_level3 TEXT,
                nav_full TEXT,
                
                -- 分类信息
                category_name TEXT,
                category_url TEXT,
                
                -- 阶梯价格1
                tier1_quantity TEXT,
                tier1_price DECIMAL(10,2),
                tier1_discount TEXT,
                
                -- 阶梯价格2
                tier2_quantity TEXT,
                tier2_price DECIMAL(10,2),
                tier2_discount TEXT,
                
                -- 阶梯价格3
                tier3_quantity TEXT,
                tier3_price DECIMAL(10,2),
                tier3_discount TEXT,
                
                -- 阶梯价格4
                tier4_quantity TEXT,
                tier4_price DECIMAL(10,2),
                tier4_discount TEXT,
                
                -- 阶梯价格5
                tier5_quantity TEXT,
                tier5_price DECIMAL(10,2),
                tier5_discount TEXT,
                
                -- 阶梯价格6
                tier6_quantity TEXT,
                tier6_price DECIMAL(10,2),
                tier6_discount TEXT,
                
                -- 合并字段
                all_tiers_combined TEXT,
                
                -- 状态字段
                crawl_status TEXT DEFAULT 'pending',
                crawl_attempts INTEGER DEFAULT 0,
                last_crawl_attempt TIMESTAMP,
                error_message TEXT,
                
                -- 时间戳
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (product_url_id) REFERENCES product_urls (id)
            )
        ''')
        
        conn.commit()
    
    return db_path

def test_insert_method():
    """测试插入方法"""
    db_path = create_test_database()
    
    print("🧪 测试横向阶梯价格结构")
    print(f"数据库: {db_path}")
    
    # 检查表结构
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(product_details)")
        columns = cursor.fetchall()
        
        print(f"\n📋 表结构 (共{len(columns)}列):")
        for i, col in enumerate(columns, 1):
            print(f"  {i:2d}. {col[1]} {col[2]}")
    
    # 插入测试产品URL
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO product_urls (sub_category_id, product_name, product_url)
            VALUES (1, 'Test Product', 'https://test.com/product')
        ''')
        product_url_id = cursor.lastrowid
        conn.commit()
    
    # 准备测试数据
    test_data = {
        'title': 'Fitness Shaking Cup Portable 15oz Sports Water Bottle',
        'sku': 'novo6428C',
        'nav_level1': 'Drinkware & Can Coolers',
        'nav_level2': 'Shaker & Blender Bottles',
        'nav_full': 'Drinkware & Can Coolers > Shaker & Blender Bottles',
        'category_name': 'Shaker & Blender Bottles',
        'tier_prices': [
            {'quantity_display': '1 piece', 'unit_price': 4.29, 'discount_percent': 0},
            {'quantity_display': '200 pieces', 'unit_price': 3.30, 'discount_percent': 23},
            {'quantity_display': '500 pieces', 'unit_price': 3.26, 'discount_percent': 24},
            {'quantity_display': '1,000 pieces', 'unit_price': 3.22, 'discount_percent': 24},
            {'quantity_display': '3,000 pieces', 'unit_price': 3.18, 'discount_percent': 25},
            {'quantity_display': '5,000+ pieces', 'unit_price': 3.13, 'discount_percent': 27}
        ]
    }
    
    # 手动构建插入语句
    tier_prices = test_data.get('tier_prices', [])
    
    # 准备阶梯价格字段
    tier_fields = {}
    for i in range(1, 7):  # tier1 到 tier6
        if i <= len(tier_prices):
            tier = tier_prices[i-1]
            tier_fields[f'tier{i}_quantity'] = tier.get('quantity_display', '')
            tier_fields[f'tier{i}_price'] = tier.get('unit_price', None)
            tier_fields[f'tier{i}_discount'] = f"{tier.get('discount_percent', 0)}% off" if tier.get('discount_percent') else "0% off"
        else:
            tier_fields[f'tier{i}_quantity'] = None
            tier_fields[f'tier{i}_price'] = None
            tier_fields[f'tier{i}_discount'] = None
    
    # 生成合并的阶梯价格信息
    all_tiers_parts = []
    for tier in tier_prices:
        quantity = tier.get('quantity_display', '')
        price = tier.get('unit_price', '')
        discount = tier.get('discount_percent', 0)
        discount_text = f"{discount}% off" if discount else "0% off"
        tier_part = f"{quantity}: ${price} ({discount_text})"
        all_tiers_parts.append(tier_part)
    all_tiers_combined = "||".join(all_tiers_parts)
    
    print(f"\n🔧 准备插入数据:")
    print(f"  产品URL ID: {product_url_id}")
    print(f"  标题: {test_data.get('title', '')}")
    print(f"  阶梯1: {tier_fields['tier1_quantity']} - ${tier_fields['tier1_price']} ({tier_fields['tier1_discount']})")
    print(f"  阶梯2: {tier_fields['tier2_quantity']} - ${tier_fields['tier2_price']} ({tier_fields['tier2_discount']})")
    print(f"  合并信息: {all_tiers_combined[:100]}...")
    
    # 执行插入
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 使用完整的字段列表
            cursor.execute('''
                INSERT INTO product_details
                (product_url_id, title, sku, product_image_url, short_description,
                 nav_level1, nav_level2, nav_level3, nav_full,
                 category_name, category_url,
                 tier1_quantity, tier1_price, tier1_discount,
                 tier2_quantity, tier2_price, tier2_discount,
                 tier3_quantity, tier3_price, tier3_discount,
                 tier4_quantity, tier4_price, tier4_discount,
                 tier5_quantity, tier5_price, tier5_discount,
                 tier6_quantity, tier6_price, tier6_discount,
                 all_tiers_combined, crawl_status, crawl_attempts)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                product_url_id,
                test_data.get('title', ''),
                test_data.get('sku', ''),
                test_data.get('product_image_url', ''),
                test_data.get('short_description', ''),
                test_data.get('nav_level1', ''),
                test_data.get('nav_level2', ''),
                test_data.get('nav_level3', ''),
                test_data.get('nav_full', ''),
                test_data.get('category_name', ''),
                test_data.get('category_url', ''),
                tier_fields['tier1_quantity'],
                tier_fields['tier1_price'],
                tier_fields['tier1_discount'],
                tier_fields['tier2_quantity'],
                tier_fields['tier2_price'],
                tier_fields['tier2_discount'],
                tier_fields['tier3_quantity'],
                tier_fields['tier3_price'],
                tier_fields['tier3_discount'],
                tier_fields['tier4_quantity'],
                tier_fields['tier4_price'],
                tier_fields['tier4_discount'],
                tier_fields['tier5_quantity'],
                tier_fields['tier5_price'],
                tier_fields['tier5_discount'],
                tier_fields['tier6_quantity'],
                tier_fields['tier6_price'],
                tier_fields['tier6_discount'],
                all_tiers_combined,
                'success',
                0
            ))
            
            detail_id = cursor.lastrowid
            conn.commit()
            
            print(f"\n✅ 插入成功，ID: {detail_id}")
            
            # 验证数据
            cursor.execute('''
                SELECT title, sku, tier1_quantity, tier1_price, tier1_discount,
                       tier2_quantity, tier2_price, tier2_discount, all_tiers_combined
                FROM product_details WHERE id = ?
            ''', (detail_id,))
            
            result = cursor.fetchone()
            if result:
                print(f"\n📊 验证结果:")
                print(f"  标题: {result[0]}")
                print(f"  SKU: {result[1]}")
                print(f"  阶梯1: {result[2]} - ${result[3]} ({result[4]})")
                print(f"  阶梯2: {result[5]} - ${result[6]} ({result[7]})")
                print(f"  合并信息: {result[8]}")
                
                print(f"\n🎉 测试成功！横向阶梯价格结构工作正常！")
                return True
            
    except Exception as e:
        print(f"\n❌ 插入失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试文件
        if os.path.exists(db_path):
            os.remove(db_path)

if __name__ == "__main__":
    test_insert_method()
