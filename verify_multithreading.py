#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证多线程功能
"""

print("🧪 验证多线程功能...")

try:
    from stage3_crawler import Stage3Crawler
    
    # 测试创建多线程爬虫
    crawler = Stage3Crawler('novoline_spider_v2.db', max_workers=5)
    print(f"✅ 创建5线程爬虫成功，线程数: {crawler.max_workers}")
    
    # 检查必要方法
    if hasattr(crawler, '_stage3_worker_multithreaded'):
        print("✅ 多线程工作方法存在")
    else:
        print("❌ 多线程工作方法缺失")
    
    if hasattr(crawler, '_crawl_single_product_thread_safe'):
        print("✅ 线程安全采集方法存在")
    else:
        print("❌ 线程安全采集方法缺失")
    
    # 检查GUI
    from gui import SpiderGUI
    gui = SpiderGUI()
    
    if hasattr(gui, 'thread_count_var'):
        print(f"✅ GUI线程配置存在，默认值: {gui.thread_count_var.get()}")
    else:
        print("❌ GUI线程配置缺失")
    
    print("✅ 多线程功能验证完成！")
    
except Exception as e:
    print(f"❌ 验证失败: {e}")
    import traceback
    traceback.print_exc()
