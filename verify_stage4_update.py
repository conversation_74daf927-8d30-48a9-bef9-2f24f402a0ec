#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证步骤四更新是否成功
"""

import sqlite3

def verify_update():
    """验证更新是否成功"""
    print("🔍 验证步骤四更新...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute('''
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='product_images'
            ''')
            
            if not cursor.fetchone():
                print("❌ product_images表不存在")
                return False
            
            # 检查字段
            cursor.execute('PRAGMA table_info(product_images)')
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            print(f"📋 表结构包含 {len(columns)} 个字段:")
            
            required_fields = [
                'additional_information',
                'description', 
                'breadcrumb_path',
                'image1_url', 'image1_local',
                'image10_url', 'image10_local'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field in column_names:
                    print(f"   ✅ {field}")
                else:
                    print(f"   ❌ {field} - 缺失")
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"\n❌ 缺失字段: {missing_fields}")
                
                # 尝试添加缺失字段
                if 'additional_information' in missing_fields:
                    print("🔧 添加additional_information字段...")
                    cursor.execute('''
                        ALTER TABLE product_images 
                        ADD COLUMN additional_information TEXT
                    ''')
                    conn.commit()
                    print("✅ additional_information字段添加成功")
            else:
                print(f"\n✅ 所有必需字段都存在")
            
            return len(missing_fields) == 0
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔍 步骤四更新验证")
    print("=" * 50)
    
    if verify_update():
        print("\n✅ 步骤四更新验证成功")
        print("🚀 可以开始使用步骤四功能")
    else:
        print("\n❌ 步骤四更新验证失败")

if __name__ == "__main__":
    main()
