#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采集新添加的缺失分类
"""

import sqlite3
import time
from spider_core import NovolineSpid<PERSON>

def get_uncrawled_categories():
    """获取未采集的分类"""
    uncrawled = []
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 查找没有产品URL的子分类（即未采集的）
            cursor.execute('''
                SELECT sc.id, sc.sub_category_name, sc.sub_category_url
                FROM sub_categories sc
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                WHERE pu.id IS NULL
                ORDER BY sc.id DESC
            ''')
            
            rows = cursor.fetchall()
            for row in rows:
                uncrawled.append({
                    'id': row[0],
                    'name': row[1],
                    'url': row[2]
                })
        
        return uncrawled
        
    except Exception as e:
        print(f"获取未采集分类失败: {e}")
        return []

def crawl_missing_categories():
    """采集缺失的分类"""
    print("=" * 60)
    print("🚀 采集新添加的缺失分类")
    print("=" * 60)
    
    # 获取未采集的分类
    uncrawled = get_uncrawled_categories()
    
    if not uncrawled:
        print("✅ 所有分类都已采集，无需补充")
        return
    
    print(f"📋 发现 {len(uncrawled)} 个未采集的分类:")
    for i, cat in enumerate(uncrawled, 1):
        print(f"   {i:2d}. {cat['name']} -> {cat['url']}")
    
    print(f"\n🔧 开始采集...")
    
    try:
        # 创建爬虫实例
        spider = NovolineSpider("novoline_spider_v2.db")
        
        success_count = 0
        failed_count = 0
        
        for i, cat in enumerate(uncrawled, 1):
            print(f"\n📂 [{i}/{len(uncrawled)}] 采集分类: {cat['name']}")
            print(f"   URL: {cat['url']}")
            
            try:
                # 采集该分类的产品URL
                products = spider._crawl_products_for_category(cat['id'], cat['url'], cat['name'])

                if products:
                    print(f"   ✅ 成功采集 {len(products)} 个产品URL")
                    success_count += 1
                else:
                    print(f"   ⚠️ 该分类没有产品或采集失败")
                    failed_count += 1

                # 短暂延迟，避免请求过快
                time.sleep(2)

            except Exception as e:
                print(f"   ❌ 采集失败: {e}")
                failed_count += 1
        
        print(f"\n" + "=" * 60)
        print("🎯 采集结果总结")
        print("=" * 60)
        print(f"   总分类数: {len(uncrawled)}")
        print(f"   成功采集: {success_count}")
        print(f"   采集失败: {failed_count}")
        
        if success_count > 0:
            print(f"\n🎉 成功补充了 {success_count} 个分类的产品URL！")
            print("\n📊 数据库更新:")
            
            # 显示更新后的统计
            with sqlite3.connect('novoline_spider_v2.db') as conn:
                cursor = conn.cursor()
                
                cursor.execute('SELECT COUNT(*) FROM sub_categories')
                total_categories = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM product_urls')
                total_products = cursor.fetchone()[0]
                
                print(f"   总分类数: {total_categories}")
                print(f"   总产品URL: {total_products}")
            
            print("\n🚀 现在可以:")
            print("   1. 运行第三阶段采集产品详情")
            print("   2. 享受更完整的产品数据")
        
    except Exception as e:
        print(f"❌ 采集过程发生错误: {e}")

def main():
    """主函数"""
    print("🔍 缺失分类补充采集工具")
    print("自动采集新添加的分类中的产品URL")
    print()
    
    crawl_missing_categories()

if __name__ == "__main__":
    main()
