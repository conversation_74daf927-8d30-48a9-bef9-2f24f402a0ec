#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
用于管理爬虫数据的SQLite数据库
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple

class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self, db_path: str = "novoline_spider.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库，创建所有必要的表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建主目录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS main_categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category_name TEXT NOT NULL UNIQUE,
                    category_url TEXT NOT NULL,
                    icon_url TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建子目录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sub_categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    main_category_id INTEGER,
                    sub_category_name TEXT NOT NULL,
                    sub_category_url TEXT NOT NULL,
                    product_count INTEGER DEFAULT 0,
                    page_count INTEGER DEFAULT 0,
                    last_crawled_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (main_category_id) REFERENCES main_categories (id)
                )
            ''')
            
            # 创建产品URL表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_urls (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sub_category_id INTEGER,
                    product_name TEXT NOT NULL,
                    product_url TEXT NOT NULL UNIQUE,
                    product_sku TEXT,
                    product_image_url TEXT,
                    product_price TEXT,
                    short_description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (sub_category_id) REFERENCES sub_categories (id)
                )
            ''')
            
            # 创建阶梯价格表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tier_prices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER,
                    quantity_min INTEGER NOT NULL,
                    quantity_max INTEGER,
                    price DECIMAL(10, 2) NOT NULL,
                    currency TEXT DEFAULT 'USD',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES product_urls (id)
                )
            ''')
            
            # 创建爬取进度表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_progress (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stage TEXT NOT NULL,  -- 'categories', 'products', 'prices'
                    status TEXT NOT NULL, -- 'pending', 'in_progress', 'completed', 'failed'
                    total_items INTEGER DEFAULT 0,
                    completed_items INTEGER DEFAULT 0,
                    error_message TEXT,
                    started_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建错误日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS error_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stage TEXT NOT NULL,
                    url TEXT,
                    error_type TEXT NOT NULL,
                    error_message TEXT NOT NULL,
                    stack_trace TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def insert_main_category(self, category_name: str, category_url: str, icon_url: str = None) -> int:
        """
        插入主目录
        
        Args:
            category_name: 目录名称
            category_url: 目录URL
            icon_url: 图标URL
            
        Returns:
            插入记录的ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO main_categories 
                (category_name, category_url, icon_url, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (category_name, category_url, icon_url))
            conn.commit()
            return cursor.lastrowid
    
    def insert_sub_category(self, main_category_id: int, sub_category_name: str, sub_category_url: str) -> int:
        """
        插入子目录
        
        Args:
            main_category_id: 主目录ID
            sub_category_name: 子目录名称
            sub_category_url: 子目录URL
            
        Returns:
            插入记录的ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO sub_categories 
                (main_category_id, sub_category_name, sub_category_url, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (main_category_id, sub_category_name, sub_category_url))
            conn.commit()
            return cursor.lastrowid
    
    def insert_product_url(self, sub_category_id: int, product_name: str, product_url: str,
                          product_sku: str = None, product_image_url: str = None,
                          product_price: str = None, short_description: str = None) -> int:
        """
        插入产品URL

        Args:
            sub_category_id: 子目录ID
            product_name: 产品名称
            product_url: 产品URL
            product_sku: 产品SKU (第二阶段暂不使用)
            product_image_url: 产品图片URL (第二阶段暂不使用)
            product_price: 产品价格 (第二阶段暂不使用)
            short_description: 简短描述 (第二阶段暂不使用)

        Returns:
            插入记录的ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 检查表结构，根据实际字段插入数据
            cursor.execute("PRAGMA table_info(product_urls)")
            columns = [row[1] for row in cursor.fetchall()]

            if 'product_sku' in columns:
                # 完整表结构 - 包含所有字段
                cursor.execute('''
                    INSERT OR REPLACE INTO product_urls
                    (sub_category_id, product_name, product_url, product_sku,
                     product_image_url, product_price, short_description, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (sub_category_id, product_name, product_url, product_sku,
                      product_image_url, product_price, short_description))
            else:
                # 简化表结构 - 只有基本字段（第二阶段使用）
                cursor.execute('''
                    INSERT OR REPLACE INTO product_urls
                    (sub_category_id, product_name, product_url, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                ''', (sub_category_id, product_name, product_url))

            conn.commit()
            return cursor.lastrowid
    
    def insert_tier_price(self, product_id: int, quantity_min: int, quantity_max: int, 
                         price: float, currency: str = 'USD') -> int:
        """
        插入阶梯价格
        
        Args:
            product_id: 产品ID
            quantity_min: 最小数量
            quantity_max: 最大数量
            price: 价格
            currency: 货币
            
        Returns:
            插入记录的ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO tier_prices 
                (product_id, quantity_min, quantity_max, price, currency, updated_at)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (product_id, quantity_min, quantity_max, price, currency))
            conn.commit()
            return cursor.lastrowid
    
    def update_crawl_progress(self, stage: str, status: str, total_items: int = None, 
                            completed_items: int = None, error_message: str = None):
        """
        更新爬取进度
        
        Args:
            stage: 阶段名称
            status: 状态
            total_items: 总项目数
            completed_items: 已完成项目数
            error_message: 错误信息
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 检查是否已存在该阶段的记录
            cursor.execute('SELECT id FROM crawl_progress WHERE stage = ?', (stage,))
            existing = cursor.fetchone()
            
            if existing:
                # 更新现有记录
                update_fields = ['status = ?', 'updated_at = CURRENT_TIMESTAMP']
                params = [status]
                
                if total_items is not None:
                    update_fields.append('total_items = ?')
                    params.append(total_items)
                
                if completed_items is not None:
                    update_fields.append('completed_items = ?')
                    params.append(completed_items)
                
                if error_message is not None:
                    update_fields.append('error_message = ?')
                    params.append(error_message)
                
                if status == 'in_progress' and completed_items == 0:
                    update_fields.append('started_at = CURRENT_TIMESTAMP')
                elif status == 'completed':
                    update_fields.append('completed_at = CURRENT_TIMESTAMP')
                
                params.append(stage)
                
                cursor.execute(f'''
                    UPDATE crawl_progress 
                    SET {', '.join(update_fields)}
                    WHERE stage = ?
                ''', params)
            else:
                # 插入新记录
                cursor.execute('''
                    INSERT INTO crawl_progress 
                    (stage, status, total_items, completed_items, error_message, started_at)
                    VALUES (?, ?, ?, ?, ?, 
                           CASE WHEN ? = 'in_progress' THEN CURRENT_TIMESTAMP ELSE NULL END)
                ''', (stage, status, total_items or 0, completed_items or 0, error_message, status))
            
            conn.commit()
    
    def log_error(self, stage: str, url: str, error_type: str, error_message: str, stack_trace: str = None):
        """
        记录错误日志
        
        Args:
            stage: 阶段名称
            url: 出错的URL
            error_type: 错误类型
            error_message: 错误信息
            stack_trace: 堆栈跟踪
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO error_logs 
                (stage, url, error_type, error_message, stack_trace)
                VALUES (?, ?, ?, ?, ?)
            ''', (stage, url, error_type, error_message, stack_trace))
            conn.commit()
    
    def get_crawl_progress(self, stage: str = None) -> List[Dict]:
        """
        获取爬取进度
        
        Args:
            stage: 阶段名称，如果为None则返回所有阶段
            
        Returns:
            进度信息列表
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            if stage:
                cursor.execute('SELECT * FROM crawl_progress WHERE stage = ?', (stage,))
            else:
                cursor.execute('SELECT * FROM crawl_progress ORDER BY created_at')
            
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
    
    def clear_stage_data(self, stage: str):
        """
        清空指定阶段的数据
        
        Args:
            stage: 阶段名称 ('categories', 'products', 'prices')
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            if stage == 'categories':
                cursor.execute('DELETE FROM tier_prices')
                cursor.execute('DELETE FROM product_urls')
                cursor.execute('DELETE FROM sub_categories')
                cursor.execute('DELETE FROM main_categories')
            elif stage == 'products':
                cursor.execute('DELETE FROM tier_prices')
                cursor.execute('DELETE FROM product_urls')
            elif stage == 'prices':
                cursor.execute('DELETE FROM tier_prices')
            
            # 清空对应的进度记录
            cursor.execute('DELETE FROM crawl_progress WHERE stage = ?', (stage,))
            
            conn.commit()
    
    def get_statistics(self) -> Dict:
        """
        获取数据库统计信息
        
        Returns:
            统计信息字典
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            stats = {}
            
            # 主目录数量
            cursor.execute('SELECT COUNT(*) FROM main_categories')
            stats['main_categories'] = cursor.fetchone()[0]
            
            # 子目录数量
            cursor.execute('SELECT COUNT(*) FROM sub_categories')
            stats['sub_categories'] = cursor.fetchone()[0]
            
            # 产品数量
            cursor.execute('SELECT COUNT(*) FROM product_urls')
            stats['products'] = cursor.fetchone()[0]
            
            # 价格记录数量
            cursor.execute('SELECT COUNT(*) FROM tier_prices')
            stats['tier_prices'] = cursor.fetchone()[0]
            
            # 错误数量
            cursor.execute('SELECT COUNT(*) FROM error_logs')
            stats['errors'] = cursor.fetchone()[0]
            
            return stats

    def update_sub_category_stats(self, sub_category_id: int, product_count: int, page_count: int):
        """
        更新子目录的产品统计信息

        Args:
            sub_category_id: 子目录ID
            product_count: 产品数量
            page_count: 页面数量
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE sub_categories
                SET product_count = ?, page_count = ?, last_crawled_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (product_count, page_count, sub_category_id))
            conn.commit()

    def get_category_stats(self):
        """
        获取分类统计信息，包括产品数量

        Returns:
            分类统计信息列表
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT
                    mc.id as main_id,
                    mc.category_name as main_name,
                    sc.id as sub_id,
                    sc.sub_category_name as sub_name,
                    sc.product_count,
                    sc.page_count,
                    sc.last_crawled_at,
                    COUNT(pu.id) as actual_product_count
                FROM main_categories mc
                LEFT JOIN sub_categories sc ON mc.id = sc.main_category_id
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY mc.id, sc.id
                ORDER BY mc.id, sc.id
            ''')

            results = cursor.fetchall()
            stats = []

            for row in results:
                stats.append({
                    'main_id': row[0],
                    'main_name': row[1],
                    'sub_id': row[2],
                    'sub_name': row[3],
                    'recorded_product_count': row[4] or 0,
                    'page_count': row[5] or 0,
                    'last_crawled_at': row[6],
                    'actual_product_count': row[7]
                })

            return stats

    def reset_sub_category_crawl_status(self, sub_category_id: int):
        """
        重置子目录的采集状态，用于重新采集

        Args:
            sub_category_id: 子目录ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 删除该子目录下的所有产品
            cursor.execute('DELETE FROM product_urls WHERE sub_category_id = ?', (sub_category_id,))

            # 重置统计信息
            cursor.execute('''
                UPDATE sub_categories
                SET product_count = 0, page_count = 0, last_crawled_at = NULL, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (sub_category_id,))

            conn.commit()
            print(f"✅ 已重置子目录 {sub_category_id} 的采集状态")

    def reset_main_category_crawl_status(self, main_category_id: int):
        """
        重置主目录下所有子目录的采集状态

        Args:
            main_category_id: 主目录ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 获取该主目录下的所有子目录
            cursor.execute('SELECT id FROM sub_categories WHERE main_category_id = ?', (main_category_id,))
            sub_category_ids = [row[0] for row in cursor.fetchall()]

            # 删除所有相关产品
            for sub_id in sub_category_ids:
                cursor.execute('DELETE FROM product_urls WHERE sub_category_id = ?', (sub_id,))

            # 重置所有子目录的统计信息
            cursor.execute('''
                UPDATE sub_categories
                SET product_count = 0, page_count = 0, last_crawled_at = NULL, updated_at = CURRENT_TIMESTAMP
                WHERE main_category_id = ?
            ''', (main_category_id,))

            conn.commit()
            print(f"✅ 已重置主目录 {main_category_id} 下所有子目录的采集状态")

    def get_crawl_status_detail(self):
        """
        获取详细的采集状态信息

        Returns:
            详细状态信息列表
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT
                    mc.id as main_id,
                    mc.category_name as main_name,
                    sc.id as sub_id,
                    sc.sub_category_name as sub_name,
                    sc.sub_category_url,
                    sc.product_count as recorded_count,
                    sc.page_count,
                    sc.last_crawled_at,
                    COUNT(pu.id) as actual_count,
                    CASE
                        WHEN sc.last_crawled_at IS NULL THEN 'not_started'
                        WHEN sc.product_count = COUNT(pu.id) AND sc.product_count > 0 THEN 'completed'
                        WHEN sc.product_count != COUNT(pu.id) THEN 'inconsistent'
                        ELSE 'unknown'
                    END as status
                FROM main_categories mc
                LEFT JOIN sub_categories sc ON mc.id = sc.main_category_id
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY mc.id, sc.id
                ORDER BY mc.id, sc.id
            ''')

            results = cursor.fetchall()
            status_list = []

            for row in results:
                if row[2]:  # 有子目录
                    status_list.append({
                        'main_id': row[0],
                        'main_name': row[1],
                        'sub_id': row[2],
                        'sub_name': row[3],
                        'sub_url': row[4],
                        'recorded_count': row[5] or 0,
                        'page_count': row[6] or 0,
                        'last_crawled_at': row[7],
                        'actual_count': row[8],
                        'status': row[9]
                    })

            return status_list
