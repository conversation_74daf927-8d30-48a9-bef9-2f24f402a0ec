#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
用于管理爬虫数据的SQLite数据库
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple

class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self, db_path: str = "novoline_spider.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库，创建所有必要的表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建主目录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS main_categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category_name TEXT NOT NULL UNIQUE,
                    category_url TEXT NOT NULL,
                    icon_url TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建子目录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sub_categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    main_category_id INTEGER,
                    sub_category_name TEXT NOT NULL,
                    sub_category_url TEXT NOT NULL,
                    product_count INTEGER DEFAULT 0,
                    page_count INTEGER DEFAULT 0,
                    last_crawled_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (main_category_id) REFERENCES main_categories (id)
                )
            ''')
            
            # 创建产品URL表（第二阶段：只收集基本信息）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_urls (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sub_category_id INTEGER,
                    product_name TEXT NOT NULL,
                    product_url TEXT NOT NULL UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (sub_category_id) REFERENCES sub_categories (id)
                )
            ''')
            
            # 创建产品详情表（第三阶段：存储详细信息，包含横向阶梯价格）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_details (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_url_id INTEGER NOT NULL UNIQUE,

                    -- 基本信息
                    title TEXT,
                    sku TEXT,
                    product_image_url TEXT,
                    short_description TEXT,

                    -- 导航信息
                    nav_level1 TEXT,
                    nav_level2 TEXT,
                    nav_level3 TEXT,
                    nav_full TEXT,

                    -- 分类信息
                    category_name TEXT,
                    category_url TEXT,

                    -- 阶梯价格1 (通常是1件的价格)
                    tier1_quantity TEXT,        -- "1 piece"
                    tier1_price DECIMAL(10,2),  -- 4.29
                    tier1_discount TEXT,        -- "0% off"

                    -- 阶梯价格2
                    tier2_quantity TEXT,        -- "200 pieces"
                    tier2_price DECIMAL(10,2),  -- 3.30
                    tier2_discount TEXT,        -- "23% off"

                    -- 阶梯价格3
                    tier3_quantity TEXT,        -- "500 pieces"
                    tier3_price DECIMAL(10,2),  -- 3.26
                    tier3_discount TEXT,        -- "24% off"

                    -- 阶梯价格4
                    tier4_quantity TEXT,        -- "1,000 pieces"
                    tier4_price DECIMAL(10,2),  -- 3.22
                    tier4_discount TEXT,        -- "24% off"

                    -- 阶梯价格5
                    tier5_quantity TEXT,        -- "3,000 pieces"
                    tier5_price DECIMAL(10,2),  -- 3.18
                    tier5_discount TEXT,        -- "25% off"

                    -- 阶梯价格6
                    tier6_quantity TEXT,        -- "5,000+ pieces"
                    tier6_price DECIMAL(10,2),  -- 3.13
                    tier6_discount TEXT,        -- "27% off"

                    -- 合并的完整阶梯价格信息
                    all_tiers_combined TEXT,    -- "1 piece: $4.29 (0% off)||200 pieces: $3.30 (23% off)||..."

                    -- 采集状态管理
                    crawl_status TEXT DEFAULT 'pending',
                    crawl_attempts INTEGER DEFAULT 0,
                    last_crawl_attempt TIMESTAMP,
                    error_message TEXT,

                    -- 时间戳
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

                    FOREIGN KEY (product_url_id) REFERENCES product_urls (id)
                )
            ''')

            
            # 创建爬取进度表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_progress (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stage TEXT NOT NULL,  -- 'categories', 'products', 'prices'
                    status TEXT NOT NULL, -- 'pending', 'in_progress', 'completed', 'failed'
                    total_items INTEGER DEFAULT 0,
                    completed_items INTEGER DEFAULT 0,
                    error_message TEXT,
                    started_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建错误日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS error_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stage TEXT NOT NULL,
                    url TEXT,
                    error_type TEXT NOT NULL,
                    error_message TEXT NOT NULL,
                    stack_trace TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def insert_main_category(self, category_name: str, category_url: str, icon_url: str = None) -> int:
        """
        插入主目录
        
        Args:
            category_name: 目录名称
            category_url: 目录URL
            icon_url: 图标URL
            
        Returns:
            插入记录的ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO main_categories 
                (category_name, category_url, icon_url, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (category_name, category_url, icon_url))
            conn.commit()
            return cursor.lastrowid
    
    def insert_sub_category(self, main_category_id: int, sub_category_name: str, sub_category_url: str) -> int:
        """
        插入子目录
        
        Args:
            main_category_id: 主目录ID
            sub_category_name: 子目录名称
            sub_category_url: 子目录URL
            
        Returns:
            插入记录的ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO sub_categories 
                (main_category_id, sub_category_name, sub_category_url, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (main_category_id, sub_category_name, sub_category_url))
            conn.commit()
            return cursor.lastrowid
    
    def insert_product_url(self, sub_category_id: int, product_name: str, product_url: str) -> int:
        """
        插入产品URL（第二阶段：只收集基本信息）

        Args:
            sub_category_id: 子目录ID
            product_name: 产品名称
            product_url: 产品URL

        Returns:
            插入记录的ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO product_urls
                (sub_category_id, product_name, product_url, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (sub_category_id, product_name, product_url))
            conn.commit()
            return cursor.lastrowid

    def insert_product_detail(self, product_url_id: int, product_data: dict) -> int:
        """
        插入产品详情信息（第三阶段：包含横向阶梯价格）

        Args:
            product_url_id: 产品URL ID
            product_data: 产品详情数据字典

        Returns:
            插入记录的ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 提取阶梯价格信息（最多6个层级）
            tier_prices = product_data.get('tier_prices', [])

            # 准备阶梯价格字段
            tier_fields = {}
            for i in range(1, 7):  # tier1 到 tier6
                if i <= len(tier_prices):
                    tier = tier_prices[i-1]
                    tier_fields[f'tier{i}_quantity'] = tier.get('quantity_display', '')
                    tier_fields[f'tier{i}_price'] = tier.get('unit_price', None)
                    tier_fields[f'tier{i}_discount'] = f"{tier.get('discount_percent', 0)}% off" if tier.get('discount_percent') else "0% off"
                else:
                    tier_fields[f'tier{i}_quantity'] = None
                    tier_fields[f'tier{i}_price'] = None
                    tier_fields[f'tier{i}_discount'] = None

            # 生成合并的阶梯价格信息
            all_tiers_parts = []
            for tier in tier_prices:
                quantity = tier.get('quantity_display', '')
                price = tier.get('unit_price', '')
                discount = tier.get('discount_percent', 0)
                discount_text = f"{discount}% off" if discount else "0% off"
                tier_part = f"{quantity}: ${price} ({discount_text})"
                all_tiers_parts.append(tier_part)
            all_tiers_combined = "||".join(all_tiers_parts)

            # 使用完整的字段列表插入
            cursor.execute('''
                INSERT OR REPLACE INTO product_details
                (product_url_id, title, sku, product_image_url, short_description,
                 nav_level1, nav_level2, nav_level3, nav_full,
                 category_name, category_url,
                 tier1_quantity, tier1_price, tier1_discount,
                 tier2_quantity, tier2_price, tier2_discount,
                 tier3_quantity, tier3_price, tier3_discount,
                 tier4_quantity, tier4_price, tier4_discount,
                 tier5_quantity, tier5_price, tier5_discount,
                 tier6_quantity, tier6_price, tier6_discount,
                 all_tiers_combined, crawl_status, crawl_attempts,
                 last_crawl_attempt, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                product_url_id,
                product_data.get('title', ''),
                product_data.get('sku', ''),
                product_data.get('product_image_url', ''),
                product_data.get('short_description', ''),
                product_data.get('nav_level1', ''),
                product_data.get('nav_level2', ''),
                product_data.get('nav_level3', ''),
                product_data.get('nav_full', ''),
                product_data.get('category_name', ''),
                product_data.get('category_url', ''),
                tier_fields['tier1_quantity'],
                tier_fields['tier1_price'],
                tier_fields['tier1_discount'],
                tier_fields['tier2_quantity'],
                tier_fields['tier2_price'],
                tier_fields['tier2_discount'],
                tier_fields['tier3_quantity'],
                tier_fields['tier3_price'],
                tier_fields['tier3_discount'],
                tier_fields['tier4_quantity'],
                tier_fields['tier4_price'],
                tier_fields['tier4_discount'],
                tier_fields['tier5_quantity'],
                tier_fields['tier5_price'],
                tier_fields['tier5_discount'],
                tier_fields['tier6_quantity'],
                tier_fields['tier6_price'],
                tier_fields['tier6_discount'],
                all_tiers_combined,
                'success',
                0,  # crawl_attempts
                None,  # last_crawl_attempt
                None   # error_message
            ))

            conn.commit()
            return cursor.lastrowid


    
    def update_crawl_progress(self, stage: str, status: str, total_items: int = None, 
                            completed_items: int = None, error_message: str = None):
        """
        更新爬取进度
        
        Args:
            stage: 阶段名称
            status: 状态
            total_items: 总项目数
            completed_items: 已完成项目数
            error_message: 错误信息
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 检查是否已存在该阶段的记录
            cursor.execute('SELECT id FROM crawl_progress WHERE stage = ?', (stage,))
            existing = cursor.fetchone()
            
            if existing:
                # 更新现有记录
                update_fields = ['status = ?', 'updated_at = CURRENT_TIMESTAMP']
                params = [status]
                
                if total_items is not None:
                    update_fields.append('total_items = ?')
                    params.append(total_items)
                
                if completed_items is not None:
                    update_fields.append('completed_items = ?')
                    params.append(completed_items)
                
                if error_message is not None:
                    update_fields.append('error_message = ?')
                    params.append(error_message)
                
                if status == 'in_progress' and completed_items == 0:
                    update_fields.append('started_at = CURRENT_TIMESTAMP')
                elif status == 'completed':
                    update_fields.append('completed_at = CURRENT_TIMESTAMP')
                
                params.append(stage)
                
                cursor.execute(f'''
                    UPDATE crawl_progress 
                    SET {', '.join(update_fields)}
                    WHERE stage = ?
                ''', params)
            else:
                # 插入新记录
                cursor.execute('''
                    INSERT INTO crawl_progress 
                    (stage, status, total_items, completed_items, error_message, started_at)
                    VALUES (?, ?, ?, ?, ?, 
                           CASE WHEN ? = 'in_progress' THEN CURRENT_TIMESTAMP ELSE NULL END)
                ''', (stage, status, total_items or 0, completed_items or 0, error_message, status))
            
            conn.commit()
    
    def log_error(self, stage: str, url: str, error_type: str, error_message: str, stack_trace: str = None):
        """
        记录错误日志
        
        Args:
            stage: 阶段名称
            url: 出错的URL
            error_type: 错误类型
            error_message: 错误信息
            stack_trace: 堆栈跟踪
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO error_logs 
                (stage, url, error_type, error_message, stack_trace)
                VALUES (?, ?, ?, ?, ?)
            ''', (stage, url, error_type, error_message, stack_trace))
            conn.commit()
    
    def get_crawl_progress(self, stage: str = None) -> List[Dict]:
        """
        获取爬取进度

        Args:
            stage: 阶段名称，如果为None则返回所有阶段

        Returns:
            进度信息列表
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute('''
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='crawl_progress'
            ''')
            if not cursor.fetchone():
                return []  # 表不存在，返回空列表

            # 检查表结构，确定排序字段
            cursor.execute("PRAGMA table_info(crawl_progress)")
            table_columns = [row[1] for row in cursor.fetchall()]

            # 根据实际字段确定查询语句
            if 'created_at' in table_columns:
                order_by = 'ORDER BY created_at'
            elif 'updated_at' in table_columns:
                order_by = 'ORDER BY updated_at'
            else:
                order_by = 'ORDER BY id'

            if stage:
                cursor.execute(f'SELECT * FROM crawl_progress WHERE stage = ? {order_by}', (stage,))
            else:
                cursor.execute(f'SELECT * FROM crawl_progress {order_by}')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
    
    def clear_stage_data(self, stage: str):
        """
        清空指定阶段的数据
        
        Args:
            stage: 阶段名称 ('categories', 'products', 'prices')
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            if stage == 'categories':
                cursor.execute('DELETE FROM tier_prices')
                cursor.execute('DELETE FROM product_urls')
                cursor.execute('DELETE FROM sub_categories')
                cursor.execute('DELETE FROM main_categories')
            elif stage == 'products':
                cursor.execute('DELETE FROM tier_prices')
                cursor.execute('DELETE FROM product_urls')
            elif stage == 'prices':
                cursor.execute('DELETE FROM tier_prices')
            
            # 清空对应的进度记录
            cursor.execute('DELETE FROM crawl_progress WHERE stage = ?', (stage,))
            
            conn.commit()
    
    def get_statistics(self) -> Dict:
        """
        获取数据库统计信息
        
        Returns:
            统计信息字典
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            stats = {}
            
            # 主目录数量
            cursor.execute('SELECT COUNT(*) FROM main_categories')
            stats['main_categories'] = cursor.fetchone()[0]
            
            # 子目录数量
            cursor.execute('SELECT COUNT(*) FROM sub_categories')
            stats['sub_categories'] = cursor.fetchone()[0]
            
            # 产品数量
            cursor.execute('SELECT COUNT(*) FROM product_urls')
            stats['products'] = cursor.fetchone()[0]
            
            # 价格记录数量
            cursor.execute('SELECT COUNT(*) FROM tier_prices')
            stats['tier_prices'] = cursor.fetchone()[0]
            
            # 错误数量
            cursor.execute('SELECT COUNT(*) FROM error_logs')
            stats['errors'] = cursor.fetchone()[0]
            
            return stats

    def update_sub_category_stats(self, sub_category_id: int, product_count: int, page_count: int):
        """
        更新子目录的产品统计信息

        Args:
            sub_category_id: 子目录ID
            product_count: 产品数量
            page_count: 页面数量
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE sub_categories
                SET product_count = ?, page_count = ?, last_crawled_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (product_count, page_count, sub_category_id))
            conn.commit()

    def get_category_stats(self):
        """
        获取分类统计信息，包括产品数量

        Returns:
            分类统计信息列表
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT
                    mc.id as main_id,
                    mc.category_name as main_name,
                    sc.id as sub_id,
                    sc.sub_category_name as sub_name,
                    sc.product_count,
                    sc.page_count,
                    sc.last_crawled_at,
                    COUNT(pu.id) as actual_product_count
                FROM main_categories mc
                LEFT JOIN sub_categories sc ON mc.id = sc.main_category_id
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY mc.id, sc.id
                ORDER BY mc.id, sc.id
            ''')

            results = cursor.fetchall()
            stats = []

            for row in results:
                stats.append({
                    'main_id': row[0],
                    'main_name': row[1],
                    'sub_id': row[2],
                    'sub_name': row[3],
                    'recorded_product_count': row[4] or 0,
                    'page_count': row[5] or 0,
                    'last_crawled_at': row[6],
                    'actual_product_count': row[7]
                })

            return stats

    def reset_sub_category_crawl_status(self, sub_category_id: int):
        """
        重置子目录的采集状态，用于重新采集

        Args:
            sub_category_id: 子目录ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 删除该子目录下的所有产品
            cursor.execute('DELETE FROM product_urls WHERE sub_category_id = ?', (sub_category_id,))

            # 重置统计信息
            cursor.execute('''
                UPDATE sub_categories
                SET product_count = 0, page_count = 0, last_crawled_at = NULL, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (sub_category_id,))

            conn.commit()
            print(f"✅ 已重置子目录 {sub_category_id} 的采集状态")

    def reset_main_category_crawl_status(self, main_category_id: int):
        """
        重置主目录下所有子目录的采集状态

        Args:
            main_category_id: 主目录ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 获取该主目录下的所有子目录
            cursor.execute('SELECT id FROM sub_categories WHERE main_category_id = ?', (main_category_id,))
            sub_category_ids = [row[0] for row in cursor.fetchall()]

            # 删除所有相关产品
            for sub_id in sub_category_ids:
                cursor.execute('DELETE FROM product_urls WHERE sub_category_id = ?', (sub_id,))

            # 重置所有子目录的统计信息
            cursor.execute('''
                UPDATE sub_categories
                SET product_count = 0, page_count = 0, last_crawled_at = NULL, updated_at = CURRENT_TIMESTAMP
                WHERE main_category_id = ?
            ''', (main_category_id,))

            conn.commit()
            print(f"✅ 已重置主目录 {main_category_id} 下所有子目录的采集状态")

    def get_crawl_status_detail(self):
        """
        获取详细的采集状态信息

        Returns:
            详细状态信息列表
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT
                    mc.id as main_id,
                    mc.category_name as main_name,
                    sc.id as sub_id,
                    sc.sub_category_name as sub_name,
                    sc.sub_category_url,
                    sc.product_count as recorded_count,
                    sc.page_count,
                    sc.last_crawled_at,
                    COUNT(pu.id) as actual_count,
                    CASE
                        WHEN sc.last_crawled_at IS NULL THEN 'not_started'
                        WHEN sc.product_count = COUNT(pu.id) AND sc.product_count > 0 THEN 'completed'
                        WHEN sc.product_count != COUNT(pu.id) THEN 'inconsistent'
                        ELSE 'unknown'
                    END as status
                FROM main_categories mc
                LEFT JOIN sub_categories sc ON mc.id = sc.main_category_id
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY mc.id, sc.id
                ORDER BY mc.id, sc.id
            ''')

            results = cursor.fetchall()
            status_list = []

            for row in results:
                if row[2]:  # 有子目录
                    status_list.append({
                        'main_id': row[0],
                        'main_name': row[1],
                        'sub_id': row[2],
                        'sub_name': row[3],
                        'sub_url': row[4],
                        'recorded_count': row[5] or 0,
                        'page_count': row[6] or 0,
                        'last_crawled_at': row[7],
                        'actual_count': row[8],
                        'status': row[9]
                    })

            return status_list
