#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的批量采集查询
"""

import sqlite3

def test_optimized_query():
    """测试优化后的查询"""
    print("=" * 60)
    print("🧪 测试优化后的批量采集查询")
    print("=" * 60)
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 原始查询：所有未采集的子分类
            cursor.execute('''
                SELECT COUNT(*) FROM sub_categories sc
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                WHERE pu.id IS NULL
            ''')
            total_uncrawled = cursor.fetchone()[0]
            
            # 优化查询：排除与主目录重复的
            cursor.execute('''
                SELECT COUNT(*) FROM sub_categories sc
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                WHERE pu.id IS NULL
                  AND NOT EXISTS (
                      SELECT 1 FROM main_categories mc 
                      WHERE mc.category_url = sc.sub_category_url
                  )
            ''')
            optimized_uncrawled = cursor.fetchone()[0]
            
            # 被排除的重复URL数量
            cursor.execute('''
                SELECT COUNT(*) FROM sub_categories sc
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                WHERE pu.id IS NULL
                  AND EXISTS (
                      SELECT 1 FROM main_categories mc 
                      WHERE mc.category_url = sc.sub_category_url
                  )
            ''')
            excluded_count = cursor.fetchone()[0]
            
            print(f"📊 查询结果对比:")
            print(f"   原始未采集数量: {total_uncrawled}")
            print(f"   优化后需采集: {optimized_uncrawled}")
            print(f"   排除的重复URL: {excluded_count}")
            print(f"   优化效果: 减少了 {excluded_count} 个无效采集")
            
            # 显示被排除的具体分类
            if excluded_count > 0:
                print(f"\n❌ 被排除的重复分类:")
                cursor.execute('''
                    SELECT sc.id, sc.sub_category_name, sc.sub_category_url, mc.category_name
                    FROM sub_categories sc
                    LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                    INNER JOIN main_categories mc ON mc.category_url = sc.sub_category_url
                    WHERE pu.id IS NULL
                    ORDER BY sc.id
                ''')
                
                excluded_categories = cursor.fetchall()
                for sub_id, sub_name, sub_url, main_name in excluded_categories:
                    print(f"   [ID:{sub_id}] {sub_name}")
                    print(f"      与主目录重复: {main_name}")
                    print(f"      URL: {sub_url}")
                    print()
            
            # 显示真正需要采集的分类
            if optimized_uncrawled > 0:
                print(f"✅ 真正需要采集的分类:")
                cursor.execute('''
                    SELECT sc.id, sc.sub_category_name, sc.sub_category_url
                    FROM sub_categories sc
                    LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                    WHERE pu.id IS NULL
                      AND NOT EXISTS (
                          SELECT 1 FROM main_categories mc 
                          WHERE mc.category_url = sc.sub_category_url
                      )
                    ORDER BY sc.id
                    LIMIT 10
                ''')
                
                real_uncrawled = cursor.fetchall()
                for sub_id, sub_name, sub_url in real_uncrawled:
                    print(f"   [ID:{sub_id}] {sub_name}")
                    print(f"      URL: {sub_url}")
                    print()
                
                if optimized_uncrawled > 10:
                    print(f"   ... 还有 {optimized_uncrawled - 10} 个分类")
            
            return optimized_uncrawled, excluded_count
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return 0, 0

def check_main_category_structure():
    """检查主目录结构"""
    print(f"\n" + "=" * 60)
    print("🔍 检查主目录结构")
    print("=" * 60)
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 检查主目录数量
            cursor.execute('SELECT COUNT(*) FROM main_categories')
            main_count = cursor.fetchone()[0]
            
            # 检查子目录数量
            cursor.execute('SELECT COUNT(*) FROM sub_categories')
            sub_count = cursor.fetchone()[0]
            
            print(f"📊 目录结构:")
            print(f"   主目录数量: {main_count}")
            print(f"   子目录数量: {sub_count}")
            
            # 显示主目录列表
            cursor.execute('''
                SELECT id, category_name, category_url
                FROM main_categories
                ORDER BY id
            ''')
            
            main_categories = cursor.fetchall()
            print(f"\n📋 主目录列表:")
            for main_id, name, url in main_categories:
                print(f"   [ID:{main_id}] {name}")
                print(f"      URL: {url}")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🧪 优化后的批量采集测试")
    print("验证排除主目录重复URL的效果")
    print()
    
    # 检查主目录结构
    check_main_category_structure()
    
    # 测试优化查询
    optimized_count, excluded_count = test_optimized_query()
    
    print("\n" + "=" * 60)
    print("🎯 测试结果总结")
    print("=" * 60)
    
    if excluded_count > 0:
        print(f"✅ 优化成功!")
        print(f"   • 排除了 {excluded_count} 个与主目录重复的URL")
        print(f"   • 只需采集 {optimized_count} 个真正的子目录")
        print(f"   • 避免了无效采集，提高效率")
        
        print(f"\n💡 说明:")
        print(f"   • 主目录URL通常是分类页面，不包含具体产品")
        print(f"   • 子目录URL才包含真正的产品列表")
        print(f"   • 排除重复可以避免采集空的分类页面")
        
    if optimized_count > 0:
        print(f"\n🚀 下一步:")
        print(f"   1. 重新启动程序")
        print(f"   2. 使用优化后的批量采集功能")
        print(f"   3. 只采集 {optimized_count} 个真正需要的分类")
    else:
        print(f"\n🎉 所有需要的分类都已采集完成!")

if __name__ == "__main__":
    main()
