#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整功能测试脚本
测试所有修正后的功能
"""

import os
import sys
import sqlite3

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_gui_improvements():
    """测试GUI改进"""
    print("🖥️ GUI改进测试")
    print("=" * 50)
    
    print("✅ 已修正的功能:")
    print("1. 移除第二阶段的SKU和价格显示")
    print("2. 添加第三阶段状态显示")
    print("3. 产品右键菜单功能")
    print("4. 第三阶段控制按钮")
    print("5. 数据库升级功能")
    
    print(f"\n🚀 启动GUI测试:")
    print("python gui.py")
    
    print(f"\n📋 测试项目:")
    print("1. 查看产品表格 - 应该显示'第三阶段状态'列")
    print("2. 右键点击产品 - 应该有重新采集等选项")
    print("3. 第三阶段Tab - 应该有过滤选项和控制按钮")
    print("4. 点击'升级数据库' - 应该能升级到新结构")

def test_database_structure():
    """测试数据库结构"""
    print(f"\n🗄️ 数据库结构测试")
    print("=" * 50)
    
    # 检查新数据库
    if os.path.exists("novoline_spider_v2.db"):
        print("✅ 新数据库存在: novoline_spider_v2.db")
        
        with sqlite3.connect("novoline_spider_v2.db") as conn:
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = [
                'main_categories', 'sub_categories', 'product_urls', 
                'product_details', 'tier_prices', 'crawl_progress', 'error_logs'
            ]
            
            print(f"📊 数据库表:")
            for table in expected_tables:
                if table in tables:
                    print(f"   ✅ {table}")
                else:
                    print(f"   ❌ {table} (缺失)")
            
            # 检查数据
            cursor.execute("SELECT COUNT(*) FROM product_urls")
            product_count = cursor.fetchone()[0]
            print(f"\n📈 数据统计:")
            print(f"   产品URL数量: {product_count}")
            
            if product_count > 0:
                # 检查第三阶段状态
                cursor.execute("""
                    SELECT 
                        COUNT(CASE WHEN pd.crawl_status IS NULL THEN 1 END) as pending,
                        COUNT(CASE WHEN pd.crawl_status = 'success' THEN 1 END) as success,
                        COUNT(CASE WHEN pd.crawl_status = 'failed' THEN 1 END) as failed
                    FROM product_urls pu
                    LEFT JOIN product_details pd ON pu.id = pd.product_url_id
                """)
                pending, success, failed = cursor.fetchone()
                
                print(f"   第三阶段状态:")
                print(f"     待采集: {pending}")
                print(f"     成功: {success}")
                print(f"     失败: {failed}")
    else:
        print("❌ 新数据库不存在，需要运行升级")

def test_stage3_functionality():
    """测试第三阶段功能"""
    print(f"\n🎯 第三阶段功能测试")
    print("=" * 50)
    
    try:
        from product_detail_parser import ProductDetailParser
        from stage3_crawler import Stage3Crawler
        
        print("✅ 第三阶段模块导入成功")
        
        # 测试解析器
        parser = ProductDetailParser()
        print("✅ 产品详情解析器创建成功")
        
        # 测试爬虫
        if os.path.exists("novoline_spider_v2.db"):
            crawler = Stage3Crawler("novoline_spider_v2.db")
            stats = crawler.get_stage3_statistics()
            
            print("✅ 第三阶段爬虫创建成功")
            print(f"📊 第三阶段统计:")
            for key, value in stats.items():
                print(f"   {key}: {value}")
        else:
            print("⚠️ 需要新数据库结构")
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_right_click_menu():
    """测试右键菜单功能"""
    print(f"\n🖱️ 右键菜单功能测试")
    print("=" * 50)
    
    print("📋 右键菜单功能:")
    print("1. 重新采集此产品详情 - 单独重新采集产品")
    print("2. 查看产品详情 - 显示详细信息")
    print("3. 标记为失败 - 手动标记失败状态")
    print("4. 重置采集状态 - 重置为待采集状态")
    
    print(f"\n🧪 测试方法:")
    print("1. 启动GUI: python gui.py")
    print("2. 切换到'数据查看'Tab")
    print("3. 在产品表格中右键点击任意产品")
    print("4. 测试各个菜单选项")

def show_feature_summary():
    """显示功能总结"""
    print(f"\n🎉 功能修正总结")
    print("=" * 50)
    
    print("✅ 已修正的问题:")
    print("1. 移除第二阶段的SKU和价格显示")
    print("2. 添加第三阶段状态列显示")
    print("3. 实现产品右键菜单功能")
    print("4. 添加第三阶段控制按钮")
    print("5. 支持状态过滤和批量操作")
    
    print(f"\n🆕 新增功能:")
    print("1. 完整的导航信息存储 (分级)")
    print("2. 详细的阶梯价格系统")
    print("3. 原始数据保存 (分隔符格式)")
    print("4. 强大的状态管理系统")
    print("5. 重试机制和错误处理")
    print("6. 数据库自动升级功能")
    
    print(f"\n🎯 使用方法:")
    print("1. 启动GUI: python gui.py")
    print("2. 如果提示升级数据库，点击'升级数据库'按钮")
    print("3. 在第三阶段Tab中选择采集范围")
    print("4. 点击'开始第三阶段'开始采集")
    print("5. 在数据查看Tab中右键产品进行操作")
    
    print(f"\n📊 状态说明:")
    print("⏳ 待采集 - 尚未开始第三阶段采集")
    print("🔄 采集中 - 正在采集产品详情")
    print("✅ 已完成 - 成功采集完成")
    print("❌ 失败 - 采集失败，可重试")

def main():
    """主函数"""
    print("🧪 Novoline爬虫完整功能测试")
    print("测试所有修正后的功能")
    
    while True:
        print(f"\n请选择测试项目:")
        print("1. GUI改进测试")
        print("2. 数据库结构测试")
        print("3. 第三阶段功能测试")
        print("4. 右键菜单功能测试")
        print("5. 功能总结")
        print("6. 启动GUI")
        print("7. 退出")
        
        choice = input("\n请选择 (1-7): ").strip()
        
        if choice == "1":
            test_gui_improvements()
        elif choice == "2":
            test_database_structure()
        elif choice == "3":
            test_stage3_functionality()
        elif choice == "4":
            test_right_click_menu()
        elif choice == "5":
            show_feature_summary()
        elif choice == "6":
            print("🚀 启动GUI...")
            os.system("python gui.py")
        elif choice == "7":
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选择，请输入 1-7")

if __name__ == "__main__":
    main()
