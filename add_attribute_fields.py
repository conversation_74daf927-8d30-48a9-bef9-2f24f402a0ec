#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为product_images表添加属性字段
"""

import sqlite3

def add_attribute_fields():
    """为product_images表添加4个属性字段"""
    print("🔧 为product_images表添加属性字段...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute('''
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='product_images'
            ''')
            
            if not cursor.fetchone():
                print("❌ product_images表不存在，请先运行 create_stage4_table.py")
                return False
            
            # 检查现有字段
            cursor.execute('PRAGMA table_info(product_images)')
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            # 需要添加的字段
            new_fields = [
                'attribute1_name',
                'attribute1_value', 
                'attribute2_name',
                'attribute2_value',
                'attribute3_name',
                'attribute3_value',
                'attribute4_name',
                'attribute4_value'
            ]
            
            # 检查并添加缺失的字段
            added_fields = []
            for field in new_fields:
                if field not in column_names:
                    print(f"📝 添加字段: {field}")
                    cursor.execute(f'''
                        ALTER TABLE product_images 
                        ADD COLUMN {field} TEXT
                    ''')
                    added_fields.append(field)
                else:
                    print(f"✅ 字段已存在: {field}")
            
            if added_fields:
                conn.commit()
                print(f"✅ 成功添加 {len(added_fields)} 个字段")
            else:
                print("✅ 所有属性字段都已存在")
            
            # 验证字段添加
            cursor.execute('PRAGMA table_info(product_images)')
            columns = cursor.fetchall()
            print(f"📋 更新后的表结构包含 {len(columns)} 个字段")
            
            # 显示属性相关字段
            attribute_fields = [col for col in columns if 'attribute' in col[1]]
            if attribute_fields:
                print(f"\n📊 属性字段:")
                for col in attribute_fields:
                    print(f"   {col[1]} ({col[2]})")
            
            return True
            
    except Exception as e:
        print(f"❌ 添加属性字段失败: {e}")
        return False

def test_attribute_parsing():
    """测试属性解析功能"""
    print(f"\n🧪 测试属性解析功能...")
    
    # 测试HTML示例
    test_html = '''
    <h2>Additional Information</h2>
    <table class="woocommerce-product-attributes shop_attributes" aria-label="Product Details">
        <tbody>
            <tr class="woocommerce-product-attributes-item woocommerce-product-attributes-item--attribute_pa_material">
                <th class="woocommerce-product-attributes-item__label" scope="row">Material</th>
                <td class="woocommerce-product-attributes-item__value"><p>Fabric</p></td>
            </tr>
            <tr class="woocommerce-product-attributes-item woocommerce-product-attributes-item--attribute_pa_size">
                <th class="woocommerce-product-attributes-item__label" scope="row">Size</th>
                <td class="woocommerce-product-attributes-item__value"><p>19.69*19.69 inches</p></td>
            </tr>
            <tr class="woocommerce-product-attributes-item woocommerce-product-attributes-item--attribute_pa_color">
                <th class="woocommerce-product-attributes-item__label" scope="row">Color</th>
                <td class="woocommerce-product-attributes-item__value"><p>Blue</p></td>
            </tr>
        </tbody>
    </table>
    '''
    
    try:
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(test_html, 'html.parser')
        
        # 查找属性表格
        attributes = []
        
        # 查找表格中的行
        table = soup.find('table', class_='woocommerce-product-attributes')
        if table:
            rows = table.find_all('tr')
            for row in rows:
                th = row.find('th')
                td = row.find('td')
                
                if th and td:
                    name = th.get_text(strip=True)
                    value = td.get_text(strip=True)
                    attributes.append((name, value))
        
        print(f"📋 解析结果:")
        for i, (name, value) in enumerate(attributes[:4], 1):  # 最多4个
            print(f"   属性{i}: {name} = {value}")
        
        if len(attributes) > 4:
            print(f"   ⚠️ 发现 {len(attributes)} 个属性，只保留前4个")
        
        return len(attributes) > 0
        
    except Exception as e:
        print(f"❌ 测试属性解析失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 产品属性字段添加工具")
    print("=" * 60)
    print("为product_images表添加4个属性字段用于存储解析后的属性信息")
    print()
    
    # 添加属性字段
    if add_attribute_fields():
        print("\n✅ 属性字段添加成功")
    else:
        print("\n❌ 属性字段添加失败")
        return
    
    # 测试属性解析
    if test_attribute_parsing():
        print("\n✅ 属性解析测试成功")
    else:
        print("\n❌ 属性解析测试失败")
    
    print("\n" + "=" * 60)
    print("🎯 添加完成")
    print("=" * 60)
    print("✅ 数据库结构已更新")
    print("✅ 支持存储4个属性字段")
    print("✅ 属性解析功能已就绪")
    
    print(f"\n🚀 下一步:")
    print(f"   1. 更新图片采集器的属性解析逻辑")
    print(f"   2. 重新运行步骤四采集图片和属性")
    print(f"   3. 检查属性字段数据")

if __name__ == "__main__":
    main()
