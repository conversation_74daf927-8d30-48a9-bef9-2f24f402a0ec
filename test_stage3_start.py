#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第三阶段启动功能
"""

def test_stage3_callbacks():
    """测试第三阶段回调方法"""
    print("🧪 测试第三阶段回调方法...")
    
    try:
        from gui import SpiderGUI
        from stage3_crawler import Stage3Crawler
        
        # 创建GUI实例
        gui = SpiderGUI()
        print("   ✅ GUI初始化成功")
        
        # 检查必要的回调方法
        required_methods = ['on_progress_update', 'on_error', 'on_completion']
        for method in required_methods:
            if hasattr(gui, method):
                print(f"   ✅ {method} 方法存在")
            else:
                print(f"   ❌ {method} 方法缺失")
                return False
        
        # 测试创建Stage3Crawler并设置回调
        crawler = Stage3Crawler("novoline_spider_v2.db")
        print("   ✅ Stage3Crawler创建成功")
        
        # 测试设置回调（这是之前失败的地方）
        crawler.set_callbacks(
            gui.on_progress_update,
            gui.on_error,
            gui.on_completion
        )
        print("   ✅ 回调设置成功")
        
        print("✅ 第三阶段回调测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 第三阶段回调测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stage3_start_logic():
    """测试第三阶段启动逻辑"""
    print("\n🧪 测试第三阶段启动逻辑...")
    
    try:
        from gui import SpiderGUI
        
        # 创建GUI实例
        gui = SpiderGUI()
        
        # 检查是否有新数据库
        if gui.has_new_database:
            print("   ✅ 检测到新数据库")
        else:
            print("   ❌ 未检测到新数据库")
            return False
        
        # 检查start_stage3_new方法是否存在
        if hasattr(gui, 'start_stage3_new'):
            print("   ✅ start_stage3_new 方法存在")
        else:
            print("   ❌ start_stage3_new 方法缺失")
            return False
        
        print("✅ 第三阶段启动逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 第三阶段启动逻辑测试失败: {e}")
        return False

def test_database_content():
    """测试数据库内容"""
    print("\n🧪 测试数据库内容...")
    
    try:
        import sqlite3
        
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 检查产品URL数量
            cursor.execute('SELECT COUNT(*) FROM product_urls')
            product_count = cursor.fetchone()[0]
            
            print(f"   📊 产品URL数量: {product_count}")
            
            if product_count > 0:
                print("   ✅ 有产品URL可供第三阶段采集")
                
                # 检查已采集的产品详情数量
                cursor.execute('SELECT COUNT(*) FROM product_details')
                detail_count = cursor.fetchone()[0]
                
                print(f"   📊 已采集产品详情: {detail_count}")
                
                pending_count = product_count - detail_count
                print(f"   📊 待采集产品: {pending_count}")
                
                return True
            else:
                print("   ❌ 没有产品URL，需要先完成第二阶段")
                return False
                
    except Exception as e:
        print(f"❌ 数据库内容测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 第三阶段启动功能测试")
    print("=" * 60)
    print("验证第三阶段是否可以正常启动")
    print()
    
    results = []
    
    # 执行各项测试
    results.append(test_stage3_callbacks())
    results.append(test_stage3_start_logic())
    results.append(test_database_content())
    
    print("\n" + "=" * 60)
    print("🎯 测试结果总结")
    print("=" * 60)
    
    if all(results):
        print("🎉 所有测试通过！第三阶段启动功能已修复！")
        print("\n📋 修复总结:")
        print("   ✅ 修复了回调方法名称错误")
        print("   ✅ GUI回调方法正确设置")
        print("   ✅ Stage3Crawler可以正常创建")
        print("   ✅ 数据库有足够的数据供采集")
        print("\n🚀 现在可以:")
        print("   1. 重新启动程序")
        print("   2. 点击'开始第三阶段'按钮")
        print("   3. 选择采集范围（全部/待采集/失败的）")
        print("   4. 开始采集产品详情和阶梯价格")
    else:
        failed_tests = [i for i, result in enumerate(results, 1) if not result]
        print(f"❌ 部分测试失败: 测试 {failed_tests}")
        print("需要进一步检查")

if __name__ == "__main__":
    main()
