#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复步骤四的问题
"""

import sqlite3
import os
import re

def fix_database_constraints():
    """修复数据库约束问题"""
    print("🔧 修复数据库约束问题...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 检查product_images表结构
            cursor.execute('PRAGMA table_info(product_images)')
            columns = cursor.fetchall()
            
            # 查找product_url字段的约束
            product_url_column = None
            for col in columns:
                if col[1] == 'product_url':
                    product_url_column = col
                    break
            
            if product_url_column:
                print(f"   product_url字段: {product_url_column[1]} ({product_url_column[2]}) NOT NULL: {product_url_column[3]}")
                
                # 如果product_url字段有NOT NULL约束，我们需要确保所有插入都包含这个字段
                if product_url_column[3] == 1:  # NOT NULL
                    print("   ✅ product_url字段确实有NOT NULL约束")
                    
                    # 检查是否有空的product_url记录
                    cursor.execute('''
                        SELECT COUNT(*) FROM product_images 
                        WHERE product_url IS NULL OR product_url = ''
                    ''')
                    null_count = cursor.fetchone()[0]
                    
                    if null_count > 0:
                        print(f"   ⚠️ 发现 {null_count} 个空的product_url记录")
                        
                        # 尝试修复这些记录
                        cursor.execute('''
                            UPDATE product_images 
                            SET product_url = (
                                SELECT pu.product_url 
                                FROM product_urls pu 
                                WHERE pu.id = product_images.product_url_id
                            )
                            WHERE product_url IS NULL OR product_url = ''
                        ''')
                        
                        fixed_count = cursor.rowcount
                        print(f"   ✅ 修复了 {fixed_count} 个记录")
                        
                        conn.commit()
                    else:
                        print("   ✅ 没有发现空的product_url记录")
            
            # 检查当前数据状态
            cursor.execute('SELECT COUNT(*) FROM product_images')
            total_images = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM product_urls')
            total_urls = cursor.fetchone()[0]
            
            print(f"\n📊 当前数据状态:")
            print(f"   product_urls: {total_urls:,}")
            print(f"   product_images: {total_images:,}")
            
            return True
            
    except Exception as e:
        print(f"❌ 修复数据库约束失败: {e}")
        return False

def test_path_cleaning():
    """测试路径清理功能"""
    print(f"\n🧪 测试路径清理功能...")
    
    # 测试问题路径
    problem_paths = [
        "Drinkware & Can Coolers",
        "Bottle Sleeves",
        "\xa0Insulated Neoprene Beer Wine Carrier Bag wOpener ",
        "Insulated Neoprene Beer Wine Carrier Bag wOpener (",
        "Product Name (with parentheses)",
        "Name with\u00a0non-breaking\u2000spaces",
        "Very Long Product Name That Exceeds Normal Length Limits"
    ]
    
    def clean_path_part(part: str) -> str:
        # 移除或替换非法字符，包括特殊的Unicode字符
        cleaned = re.sub(r'[<>:"/\\|?*\xa0\u00a0\u2000-\u200f\u2028-\u202f]', '', part)
        # 移除多余的空格和特殊字符
        cleaned = re.sub(r'\s+', ' ', cleaned)  # 多个空格替换为单个空格
        cleaned = cleaned.strip('. ')  # 移除首尾的点和空格
        # 移除括号中的内容如果路径太长
        if len(cleaned) > 40:
            cleaned = re.sub(r'\([^)]*\)', '', cleaned).strip()
        return cleaned[:40]  # 限制长度
    
    print(f"   测试路径清理:")
    for path in problem_paths:
        cleaned = clean_path_part(path)
        print(f"      原始: '{path}'")
        print(f"      清理: '{cleaned}'")
        print()
    
    return True

def clean_existing_failed_records():
    """清理现有的失败记录"""
    print(f"\n🧹 清理现有的失败记录...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 查找失败的记录
            cursor.execute('''
                SELECT COUNT(*) FROM product_images 
                WHERE crawl_status = 'failed'
            ''')
            failed_count = cursor.fetchone()[0]
            
            if failed_count > 0:
                print(f"   发现 {failed_count} 个失败记录")
                
                # 可以选择删除失败记录，让它们重新采集
                response = input("   是否删除失败记录以便重新采集？(y/n): ")
                if response.lower() == 'y':
                    cursor.execute('''
                        DELETE FROM product_images 
                        WHERE crawl_status = 'failed'
                    ''')
                    deleted_count = cursor.rowcount
                    conn.commit()
                    print(f"   ✅ 删除了 {deleted_count} 个失败记录")
                else:
                    print("   跳过删除失败记录")
            else:
                print("   ✅ 没有失败记录")
            
            return True
            
    except Exception as e:
        print(f"❌ 清理失败记录失败: {e}")
        return False

def create_test_directory():
    """创建测试目录结构"""
    print(f"\n📁 测试目录创建...")
    
    try:
        # 测试创建一个清理后的路径
        test_path = os.path.join("images", "Test Category", "Test Subcategory", "Test Product")
        
        print(f"   测试路径: {test_path}")
        
        os.makedirs(test_path, exist_ok=True)
        
        if os.path.exists(test_path):
            print(f"   ✅ 测试目录创建成功")
            
            # 清理测试目录
            import shutil
            shutil.rmtree("images/Test Category", ignore_errors=True)
            print(f"   🧹 测试目录已清理")
            
            return True
        else:
            print(f"   ❌ 测试目录创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 目录创建测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 步骤四问题修复工具")
    print("=" * 60)
    print("修复数据库约束和路径创建问题")
    print()
    
    # 修复数据库约束
    db_ok = fix_database_constraints()
    
    # 测试路径清理
    path_ok = test_path_cleaning()
    
    # 清理失败记录
    clean_ok = clean_existing_failed_records()
    
    # 测试目录创建
    dir_ok = create_test_directory()
    
    print("\n" + "=" * 60)
    print("🎯 修复结果总结")
    print("=" * 60)
    
    print(f"数据库约束修复: {'✅ 成功' if db_ok else '❌ 失败'}")
    print(f"路径清理测试: {'✅ 成功' if path_ok else '❌ 失败'}")
    print(f"失败记录清理: {'✅ 成功' if clean_ok else '❌ 失败'}")
    print(f"目录创建测试: {'✅ 成功' if dir_ok else '❌ 失败'}")
    
    if all([db_ok, path_ok, clean_ok, dir_ok]):
        print(f"\n🎉 所有问题修复完成！")
        print(f"\n🚀 下一步:")
        print(f"   1. 重新启动GUI程序")
        print(f"   2. 进入步骤四重新开始采集")
        print(f"   3. 观察错误是否已解决")
    else:
        print(f"\n⚠️ 部分问题未能修复，请检查错误信息")

if __name__ == "__main__":
    main()
