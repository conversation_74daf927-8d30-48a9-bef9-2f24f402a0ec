#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比3.txt中的分类URL和数据库中已有的分类URL，找出缺失的URL
"""

import re
import sqlite3
from urllib.parse import urljoin, urlparse

def extract_urls_from_3txt():
    """从3.txt中提取所有分类URL"""
    urls = set()
    
    try:
        with open('docs/3.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式提取所有href链接
        pattern = r'href="(https://novolinepromo\.com/[^"]+/)"'
        matches = re.findall(pattern, content)
        
        for url in matches:
            # 确保URL以/结尾
            if not url.endswith('/'):
                url += '/'
            urls.add(url)
        
        print(f"从3.txt中提取到 {len(urls)} 个分类URL")
        return urls
        
    except Exception as e:
        print(f"读取3.txt失败: {e}")
        return set()

def get_existing_urls_from_db():
    """从数据库中获取已有的分类URL"""
    urls = set()
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 从子分类表获取URL
            cursor.execute('SELECT DISTINCT sub_category_url FROM sub_categories')
            rows = cursor.fetchall()
            
            for row in rows:
                url = row[0]
                # 确保URL以/结尾
                if not url.endswith('/'):
                    url += '/'
                urls.add(url)
        
        print(f"从数据库中获取到 {len(urls)} 个分类URL")
        return urls
        
    except Exception as e:
        print(f"读取数据库失败: {e}")
        return set()

def compare_urls():
    """对比URL，找出缺失的"""
    print("=" * 60)
    print("🔍 对比分析分类URL")
    print("=" * 60)
    
    # 获取两个数据源的URL
    txt_urls = extract_urls_from_3txt()
    db_urls = get_existing_urls_from_db()
    
    # 找出缺失的URL
    missing_urls = txt_urls - db_urls
    extra_urls = db_urls - txt_urls
    common_urls = txt_urls & db_urls
    
    print(f"\n📊 对比结果:")
    print(f"   3.txt中的URL: {len(txt_urls)}")
    print(f"   数据库中的URL: {len(db_urls)}")
    print(f"   共同的URL: {len(common_urls)}")
    print(f"   缺失的URL: {len(missing_urls)}")
    print(f"   多余的URL: {len(extra_urls)}")
    
    if missing_urls:
        print(f"\n❌ 缺失的URL ({len(missing_urls)}个):")
        for i, url in enumerate(sorted(missing_urls), 1):
            print(f"   {i:2d}. {url}")
    
    if extra_urls:
        print(f"\n➕ 数据库中多余的URL ({len(extra_urls)}个):")
        for i, url in enumerate(sorted(extra_urls), 1):
            print(f"   {i:2d}. {url}")
    
    return missing_urls, extra_urls

def add_missing_urls_to_db(missing_urls):
    """将缺失的URL添加到数据库"""
    if not missing_urls:
        print("\n✅ 没有缺失的URL需要添加")
        return
    
    print(f"\n🔧 准备添加 {len(missing_urls)} 个缺失的URL到数据库...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 获取主分类信息
            cursor.execute('SELECT id, category_name, category_url FROM main_categories')
            categories = cursor.fetchall()
            
            added_count = 0
            
            for url in sorted(missing_urls):
                # 尝试匹配主分类
                matched_category = None
                
                for cat_id, cat_name, cat_url in categories:
                    if cat_url and url.startswith(cat_url):
                        matched_category = cat_id
                        break
                
                if not matched_category:
                    # 如果没有匹配的主分类，尝试根据URL路径推断
                    path_parts = urlparse(url).path.strip('/').split('/')
                    if len(path_parts) >= 1:
                        # 使用第一个路径部分作为主分类
                        main_category = path_parts[0]
                        
                        # 查找或创建主分类
                        cursor.execute('SELECT id FROM main_categories WHERE category_url LIKE ?',
                                     (f'%{main_category}%',))
                        result = cursor.fetchone()

                        if result:
                            matched_category = result[0]
                        else:
                            # 创建新的主分类
                            cursor.execute('''
                                INSERT INTO main_categories (category_name, category_url, created_at, updated_at)
                                VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                            ''', (main_category.replace('-', ' ').title(), f'https://novolinepromo.com/{main_category}/'))
                            matched_category = cursor.lastrowid
                            print(f"   📁 创建新主分类: {main_category}")
                
                if matched_category:
                    # 生成子分类名称
                    path_parts = urlparse(url).path.strip('/').split('/')
                    sub_category_name = path_parts[-1] if path_parts else 'Unknown'
                    sub_category_name = sub_category_name.replace('-', ' ').title()
                    
                    # 检查子分类是否已存在
                    cursor.execute('SELECT id FROM sub_categories WHERE sub_category_url = ?', (url,))
                    if not cursor.fetchone():
                        # 添加子分类
                        cursor.execute('''
                            INSERT INTO sub_categories
                            (main_category_id, sub_category_name, sub_category_url, created_at, updated_at)
                            VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                        ''', (matched_category, sub_category_name, url))
                        
                        added_count += 1
                        print(f"   ✅ 添加子分类: {sub_category_name} -> {url}")
                    else:
                        print(f"   ⚠️ 子分类已存在: {url}")
                else:
                    print(f"   ❌ 无法匹配主分类: {url}")
            
            conn.commit()
            print(f"\n🎉 成功添加 {added_count} 个新的子分类URL")
            
    except Exception as e:
        print(f"❌ 添加URL到数据库失败: {e}")

def main():
    """主函数"""
    print("🔍 分类URL对比分析工具")
    print("对比3.txt中的分类URL和数据库中已有的分类URL")
    print()
    
    # 对比URL
    missing_urls, extra_urls = compare_urls()
    
    if missing_urls:
        print(f"\n🤔 发现 {len(missing_urls)} 个缺失的URL")
        
        # 自动添加缺失的URL
        print("\n🔧 自动添加缺失的URL到数据库...")
        add_missing_urls_to_db(missing_urls)
            
        print("\n🔄 重新运行第二阶段采集建议:")
        print("   1. 运行程序: python main.py")
        print("   2. 进入第二阶段")
        print("   3. 选择'仅未采集'模式")
        print("   4. 开始采集新添加的分类")
    else:
        print("\n✅ 所有分类URL都已存在，无需补充")
    
    print("\n" + "=" * 60)
    print("分析完成")

if __name__ == "__main__":
    main()
