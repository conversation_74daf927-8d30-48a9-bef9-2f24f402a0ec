#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二阶段测试脚本 - 产品URL收集功能测试
测试改进的产品URL收集功能，包括分页处理和产品数量统计
"""

import os
import sys
import time
from spider_core import NovolineSpider
from database import DatabaseManager
from html_parser import NovolineParser

def progress_callback(stage, message):
    """进度回调函数"""
    print(f"[{stage.upper()}] {message}")

def error_callback(stage, error_message, traceback_info=None):
    """错误回调函数"""
    print(f"❌ [{stage.upper()}] 错误: {error_message}")
    if traceback_info:
        print(f"详细信息: {traceback_info}")

def completion_callback(stage, message):
    """完成回调函数"""
    print(f"✅ [{stage.upper()}] {message}")

def test_product_parsing():
    """测试产品页面解析功能"""
    print("=" * 60)
    print("测试产品页面解析功能")
    print("=" * 60)
    
    # 使用测试URL
    test_url = "https://novolinepromo.com/home-auto/storage-home-organization/"
    
    parser = NovolineParser("https://novolinepromo.com")
    
    print(f"正在解析测试URL: {test_url}")
    result = parser.parse_product_list(test_url)
    
    if result and result.get('products'):
        products = result['products']
        total_count = result.get('total_count', 0)
        page_count = result.get('page_count', 0)
        
        print(f"✅ 解析成功!")
        print(f"📊 统计信息:")
        print(f"   - 总产品数: {total_count}")
        print(f"   - 总页数: {page_count}")
        print(f"   - 平均每页: {total_count/page_count if page_count > 0 else 0:.1f} 个产品")
        
        print(f"\n📦 前5个产品示例:")
        for i, product in enumerate(products[:5]):
            print(f"   {i+1}. {product['name']}")
            print(f"      SKU: {product.get('sku', 'N/A')}")
            print(f"      价格: {product.get('price', 'N/A')}")
            print(f"      URL: {product['url']}")
            print()
        
        return True
    else:
        print("❌ 解析失败，未找到产品")
        return False

def test_database_stats():
    """测试数据库统计功能"""
    print("=" * 60)
    print("测试数据库统计功能")
    print("=" * 60)
    
    # 创建测试数据库
    test_db_path = "test_stage2.db"
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
    
    db = DatabaseManager(test_db_path)
    
    # 插入测试数据
    main_cat_id = db.insert_main_category("测试主目录", "https://example.com/main", "")
    sub_cat_id = db.insert_sub_category(main_cat_id, "测试子目录", "https://example.com/sub")
    
    # 插入一些产品
    for i in range(5):
        db.insert_product_url(
            sub_cat_id,
            f"测试产品{i+1}",
            f"https://example.com/product/{i+1}",
            f"SKU{i+1:03d}",
            f"https://example.com/image/{i+1}.jpg",
            f"${(i+1)*10}.99",
            f"这是测试产品{i+1}的描述"
        )
    
    # 更新统计信息
    db.update_sub_category_stats(sub_cat_id, 5, 2)
    
    # 获取统计信息
    stats = db.get_category_stats()
    
    print("📊 分类统计信息:")
    for stat in stats:
        if stat['sub_name']:
            print(f"   {stat['main_name']} > {stat['sub_name']}")
            print(f"      记录的产品数: {stat['recorded_product_count']}")
            print(f"      实际产品数: {stat['actual_product_count']}")
            print(f"      页面数: {stat['page_count']}")
            print(f"      最后爬取: {stat['last_crawled_at'] or '未爬取'}")
            print()
    
    # 清理测试数据库
    try:
        db = None
        time.sleep(0.1)
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        print("✅ 数据库统计功能测试完成")
        return True
    except Exception as e:
        print(f"❌ 清理测试数据库时出错: {e}")
        return False

def test_full_stage2():
    """测试完整的第二阶段流程"""
    print("=" * 60)
    print("测试完整的第二阶段流程")
    print("=" * 60)
    
    # 检查是否有第一阶段的数据
    stage1_db = "demo_stage1.db"
    if not os.path.exists(stage1_db):
        print("❌ 未找到第一阶段数据库，请先运行第一阶段")
        return False
    
    # 创建第二阶段测试数据库
    test_db_path = "test_stage2_full.db"
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
    
    # 复制第一阶段数据到测试数据库
    import shutil
    shutil.copy2(stage1_db, test_db_path)
    
    # 创建爬虫实例
    spider = NovolineSpider(test_db_path)
    spider.set_callbacks(
        progress_callback=progress_callback,
        error_callback=error_callback,
        completion_callback=completion_callback
    )
    
    print("🚀 开始第二阶段测试...")
    print("🎯 目标：收集产品URL并统计数量")
    print()
    
    # 开始产品URL爬取
    spider.start_crawling("products", restart=False)
    
    # 等待完成
    print("⏳ 爬取进行中，请等待...")
    while spider.is_running:
        time.sleep(1)
    
    # 检查结果
    db = DatabaseManager(test_db_path)
    stats = db.get_category_stats()
    
    print("\n📊 最终统计结果:")
    total_products = 0
    for stat in stats:
        if stat['sub_name'] and stat['actual_product_count'] > 0:
            print(f"   {stat['main_name']} > {stat['sub_name']}: {stat['actual_product_count']}个产品 ({stat['page_count']}页)")
            total_products += stat['actual_product_count']
    
    print(f"\n🎉 总计收集到 {total_products} 个产品URL")
    
    # 保留测试数据库供查看
    print(f"📁 测试数据库保存为: {test_db_path}")
    
    return total_products > 0

def main():
    """主测试函数"""
    print("Novoline爬虫第二阶段测试")
    print("测试产品URL收集和数量统计功能")
    print()
    
    success_count = 0
    total_tests = 3
    
    # 测试产品页面解析
    if test_product_parsing():
        print("✓ 产品页面解析测试通过")
        success_count += 1
    else:
        print("✗ 产品页面解析测试失败")
    
    print()
    
    # 测试数据库统计功能
    if test_database_stats():
        print("✓ 数据库统计功能测试通过")
        success_count += 1
    else:
        print("✗ 数据库统计功能测试失败")
    
    print()
    
    # 测试完整流程
    if test_full_stage2():
        print("✓ 第二阶段完整流程测试通过")
        success_count += 1
    else:
        print("✗ 第二阶段完整流程测试失败")
    
    print("=" * 60)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！第二阶段功能正常")
        return True
    else:
        print("❌ 部分测试失败，请检查代码")
        return False

if __name__ == "__main__":
    main()
