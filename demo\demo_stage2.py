#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二阶段演示脚本 - 产品URL收集功能演示
基于第一阶段的分类数据，收集所有产品URL并统计数量
"""

import os
import sys
import time
from spider_core import NovolineSpider
from database import DatabaseManager

def progress_callback(stage, message):
    """进度回调函数"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] [{stage.upper()}] {message}")

def error_callback(stage, error_message, traceback_info=None):
    """错误回调函数"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] ❌ [{stage.upper()}] 错误: {error_message}")
    if traceback_info:
        print(f"详细错误信息: {traceback_info}")

def completion_callback(stage, message):
    """完成回调函数"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] ✅ [{stage.upper()}] {message}")

def show_category_stats(db_path):
    """显示分类统计信息"""
    db = DatabaseManager(db_path)
    stats = db.get_category_stats()
    
    print("\n📊 分类产品统计:")
    print("=" * 80)
    
    main_totals = {}
    grand_total = 0
    
    for stat in stats:
        if stat['sub_name']:
            main_name = stat['main_name']
            sub_name = stat['sub_name']
            product_count = stat['actual_product_count']
            page_count = stat['page_count']
            recorded_count = stat['recorded_product_count']
            
            # 累计主目录统计
            if main_name not in main_totals:
                main_totals[main_name] = {'products': 0, 'subcategories': 0}
            main_totals[main_name]['products'] += product_count
            main_totals[main_name]['subcategories'] += 1
            grand_total += product_count
            
            # 显示子目录信息
            status = "✅" if product_count > 0 else "⏳"
            consistency = "✓" if product_count == recorded_count else "⚠"
            
            print(f"{status} {main_name} > {sub_name}")
            print(f"    产品数量: {product_count} {consistency}")
            if page_count > 0:
                print(f"    页面数量: {page_count}")
                print(f"    平均每页: {product_count/page_count:.1f} 个产品")
            if stat['last_crawled_at']:
                print(f"    最后爬取: {stat['last_crawled_at']}")
            print()
    
    print("📈 主目录汇总:")
    print("-" * 50)
    for main_name, totals in main_totals.items():
        print(f"{main_name}: {totals['products']} 个产品 ({totals['subcategories']} 个子目录)")
    
    print(f"\n🎯 总计: {grand_total} 个产品")
    print("=" * 80)

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查第一阶段数据库
    stage1_db = "demo_stage1.db"
    if not os.path.exists(stage1_db):
        print("❌ 未找到第一阶段数据库文件 (demo_stage1.db)")
        print("   请先运行 demo_stage1.py 完成第一阶段")
        return False
    
    # 检查数据库内容
    db = DatabaseManager(stage1_db)
    stats = db.get_statistics()
    
    if stats['main_categories'] == 0:
        print("❌ 第一阶段数据库中没有分类数据")
        print("   请先运行 demo_stage1.py 完成第一阶段")
        return False
    
    print(f"✅ 找到第一阶段数据:")
    print(f"   - 主目录: {stats['main_categories']} 个")
    print(f"   - 子目录: {stats['sub_categories']} 个")
    print(f"   - 已有产品: {stats['products']} 个")
    
    return True

def main():
    """主演示函数"""
    print("=" * 80)
    print("Novoline网站爬虫 - 第二阶段演示")
    print("产品URL收集与数量统计功能")
    print("=" * 80)
    
    # 检查前置条件
    if not check_prerequisites():
        return
    
    # 准备数据库
    demo_db = "demo_stage2.db"
    stage1_db = "demo_stage1.db"
    
    print(f"\n📋 准备第二阶段演示...")
    
    # 复制第一阶段数据
    import shutil
    if os.path.exists(demo_db):
        print(f"   删除旧的演示数据库...")
        os.remove(demo_db)
    
    print(f"   复制第一阶段数据到 {demo_db}...")
    shutil.copy2(stage1_db, demo_db)
    
    # 显示初始状态
    print(f"\n📊 第二阶段开始前的状态:")
    show_category_stats(demo_db)
    
    # 创建爬虫实例
    spider = NovolineSpider(demo_db)
    spider.set_callbacks(
        progress_callback=progress_callback,
        error_callback=error_callback,
        completion_callback=completion_callback
    )
    
    print("\n🚀 开始第二阶段爬取...")
    print("🎯 目标：收集所有产品URL并统计数量")
    print("📝 特点：")
    print("   - 支持多页面自动翻页")
    print("   - 提取产品SKU、价格等信息")
    print("   - 统计每个子目录的产品数量和页面数")
    print("   - 便于与网站显示的数量进行核对")
    print()
    
    input("按回车键开始爬取...")
    
    start_time = time.time()
    
    # 开始产品URL爬取
    spider.start_crawling("products", restart=False)
    
    # 等待完成
    print("⏳ 爬取进行中，请等待...")
    while spider.is_running:
        time.sleep(1)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n⏱️ 爬取完成，耗时: {duration:.1f} 秒")
    
    # 显示最终结果
    print(f"\n📊 第二阶段完成后的状态:")
    show_category_stats(demo_db)
    
    # 显示数据库文件信息
    file_size = os.path.getsize(demo_db) / 1024  # KB
    print(f"💾 数据库文件: {demo_db} ({file_size:.1f} KB)")
    
    print("\n🎉 第二阶段演示完成！")
    print("\n📋 后续步骤:")
    print("   1. 可以查看数据库文件了解详细数据")
    print("   2. 运行第三阶段收集产品价格信息")
    print("   3. 使用GUI界面进行可视化管理")
    
    # 询问是否查看详细数据
    print("\n" + "="*50)
    choice = input("是否查看前10个产品的详细信息？(y/n): ").lower().strip()
    
    if choice == 'y':
        db = DatabaseManager(demo_db)
        import sqlite3
        with sqlite3.connect(demo_db) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT 
                    mc.category_name,
                    sc.sub_category_name,
                    pu.product_name,
                    pu.product_sku,
                    pu.product_price,
                    pu.product_url
                FROM product_urls pu
                JOIN sub_categories sc ON pu.sub_category_id = sc.id
                JOIN main_categories mc ON sc.main_category_id = mc.id
                ORDER BY pu.id
                LIMIT 10
            ''')
            
            products = cursor.fetchall()
            
            print("\n📦 前10个产品详细信息:")
            print("-" * 80)
            for i, (main_cat, sub_cat, name, sku, price, url) in enumerate(products, 1):
                print(f"{i:2d}. {name}")
                print(f"     分类: {main_cat} > {sub_cat}")
                print(f"     SKU: {sku or 'N/A'}")
                print(f"     价格: {price or 'N/A'}")
                print(f"     URL: {url}")
                print()

if __name__ == "__main__":
    main()
