#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目目录整理脚本
将测试文件、演示文件、备份文件等移动到对应目录
"""

import os
import shutil
import glob
from datetime import datetime

def cleanup_project():
    """整理项目目录"""
    print("🧹 开始整理项目目录...")
    
    # 创建必要的目录
    directories = {
        'test': '测试文件',
        'demo': '演示文件', 
        'backup': '备份文件',
        'docs': '文档文件'
    }
    
    for dir_name, desc in directories.items():
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"📁 创建目录: {dir_name} ({desc})")
    
    # 移动文件的规则
    move_rules = [
        # 测试文件
        ('test_*.py', 'test'),
        ('test_*.db', 'test'),
        ('quick_test*.py', 'test'),
        ('check_crawl_status.py', 'test'),
        
        # 演示文件
        ('demo_*.py', 'demo'),
        ('demo_*.db', 'demo'),
        
        # 备份文件
        ('*.backup_*', 'backup'),
        
        # 文档文件
        ('*.md', 'docs'),
        ('*.txt', 'docs'),
    ]
    
    moved_files = []
    
    # 执行移动
    for pattern, target_dir in move_rules:
        files = glob.glob(pattern)
        for file in files:
            if os.path.isfile(file):
                target_path = os.path.join(target_dir, os.path.basename(file))
                
                # 如果目标文件已存在，添加时间戳
                if os.path.exists(target_path):
                    name, ext = os.path.splitext(os.path.basename(file))
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    target_path = os.path.join(target_dir, f"{name}_{timestamp}{ext}")
                
                try:
                    shutil.move(file, target_path)
                    moved_files.append((file, target_path))
                    print(f"📦 移动: {file} → {target_path}")
                except Exception as e:
                    print(f"❌ 移动失败: {file} - {e}")
    
    # 保留在根目录的核心文件
    core_files = [
        'main.py',
        'gui.py', 
        'spider_core.py',
        'database.py',
        'html_parser.py',
        'requirements.txt',
        'migrate_database.py',
        'view_stats.py'
    ]
    
    print(f"\n✅ 移动完成，共移动 {len(moved_files)} 个文件")
    print(f"\n📁 目录结构:")
    print(f"   根目录: 核心功能文件")
    for file in core_files:
        if os.path.exists(file):
            print(f"     ✓ {file}")
    
    for dir_name, desc in directories.items():
        files_in_dir = os.listdir(dir_name) if os.path.exists(dir_name) else []
        print(f"   {dir_name}/: {desc} ({len(files_in_dir)} 个文件)")
        for file in files_in_dir[:3]:  # 只显示前3个
            print(f"     - {file}")
        if len(files_in_dir) > 3:
            print(f"     ... 还有 {len(files_in_dir) - 3} 个文件")
    
    # 创建新的README
    create_project_readme()

def create_project_readme():
    """创建项目README"""
    readme_content = """# Novoline网站爬虫项目

## 📁 项目结构

```
spider-flow/
├── main.py                 # 主程序入口
├── gui.py                  # 图形用户界面
├── spider_core.py          # 爬虫核心逻辑
├── database.py             # 数据库管理
├── html_parser.py          # HTML解析器
├── migrate_database.py     # 数据库迁移工具
├── view_stats.py           # 数据统计查看
├── requirements.txt        # 依赖包列表
├── test/                   # 测试文件目录
├── demo/                   # 演示文件目录
├── backup/                 # 备份文件目录
└── docs/                   # 文档文件目录
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动GUI
```bash
python gui.py
```

### 3. 命令行使用
```bash
python main.py
```

## 📋 功能说明

### 第一阶段：分类URL收集
- 收集主目录和子目录的URL
- 建立分类层级结构

### 第二阶段：产品URL收集  
- 在分类页面翻页收集所有产品URL
- 只收集产品链接，不进入产品详情页

### 第三阶段：产品详情采集
- 访问产品详情页收集SKU、价格等信息
- 收集完整的产品详细信息

## 🔧 工具说明

- `view_stats.py` - 查看数据统计
- `migrate_database.py` - 数据库结构升级
- `test/` - 各种测试脚本
- `demo/` - 功能演示脚本

## 📊 数据库

项目使用SQLite数据库存储数据：
- `main_categories` - 主目录表
- `sub_categories` - 子目录表  
- `product_urls` - 产品URL表
- `tier_prices` - 阶梯价格表
"""
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"\n📝 已更新 README.md")

def main():
    """主函数"""
    print("🧹 Novoline项目目录整理工具")
    print("=" * 50)
    
    confirm = input("确定要整理项目目录吗？这将移动很多文件。(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 已取消")
        return
    
    cleanup_project()
    
    print(f"\n🎉 项目目录整理完成！")
    print(f"💡 建议:")
    print(f"   - 核心功能文件保留在根目录")
    print(f"   - 测试时使用 test/ 目录下的文件")
    print(f"   - 演示时使用 demo/ 目录下的文件")
    print(f"   - 文档查看 docs/ 目录")

if __name__ == "__main__":
    main()
