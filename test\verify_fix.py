#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单验证修复
"""

print("🧪 验证修复...")

try:
    # 测试导入
    from database import DatabaseManager
    from stage3_crawler import Stage3Crawler
    
    print("✅ 导入成功")
    
    # 测试DatabaseManager
    db = DatabaseManager('novoline_spider_v2.db')
    if hasattr(db, 'insert_product_detail'):
        print("✅ DatabaseManager有insert_product_detail方法")
    else:
        print("❌ DatabaseManager缺少insert_product_detail方法")
    
    # 测试Stage3Crawler
    crawler = Stage3Crawler('novoline_spider_v2.db')
    print(f"✅ Stage3Crawler使用的数据库类型: {type(crawler.db).__name__}")
    
    if hasattr(crawler.db, 'insert_product_detail'):
        print("✅ Stage3Crawler的数据库有insert_product_detail方法")
    else:
        print("❌ Stage3Crawler的数据库缺少insert_product_detail方法")
    
    print("🎉 修复验证完成！")
    
except Exception as e:
    print(f"❌ 验证失败: {e}")
    import traceback
    traceback.print_exc()
