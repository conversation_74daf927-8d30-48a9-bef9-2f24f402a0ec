#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版第二阶段测试
验证第二阶段只收集产品URL，不收集详细信息
"""

import os
import sys
import shutil
import sqlite3

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from spider_core import NovolineSpider
from database import DatabaseManager

def test_stage2_corrected():
    """测试修正版第二阶段功能"""
    print("=" * 80)
    print("修正版第二阶段测试 - 只收集产品URL")
    print("=" * 80)
    
    # 准备测试数据
    test_db = "test_stage2_corrected.db"
    
    # 查找第一阶段数据库
    stage1_db_paths = [
        "../demo/demo_stage1.db",
        "demo/demo_stage1.db",
        "../demo_stage1.db",
        "demo_stage1.db"
    ]

    stage1_db = None
    for path in stage1_db_paths:
        if os.path.exists(path):
            stage1_db = path
            break

    if stage1_db:
        if os.path.exists(test_db):
            os.remove(test_db)
        shutil.copy2(stage1_db, test_db)
        print(f"✅ 测试数据已准备: {test_db} (来源: {stage1_db})")
    else:
        print("❌ 未找到第一阶段数据")
        print("   查找路径:", stage1_db_paths)
        return
    
    print("\n🎯 第二阶段修正说明:")
    print("=" * 50)
    print("修正前: 第二阶段收集产品URL + SKU + 价格 (错误)")
    print("修正后: 第二阶段只收集产品URL + 产品名称 (正确)")
    print("说明: SKU、价格等详细信息应在第三阶段访问产品详情页时收集")
    
    # 创建爬虫实例
    spider = NovolineSpider()
    spider.db.db_path = test_db
    
    # 设置回调
    def on_progress(stage, message):
        print(f"[{stage}] {message}")
    
    def on_error(message, stack_trace=None):
        print(f"❌ 错误: {message}")
    
    def on_completion(message):
        print(f"✅ 完成: {message}")
    
    spider.set_callbacks(on_progress, on_error, on_completion)
    
    print(f"\n🚀 开始修正版第二阶段测试...")
    print("   只收集产品URL，不访问产品详情页")
    
    # 选择一个子目录进行测试
    try:
        with sqlite3.connect(test_db) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT sc.id, mc.category_name, sc.sub_category_name, sc.sub_category_url
                FROM sub_categories sc
                JOIN main_categories mc ON sc.main_category_id = mc.id
                LIMIT 1
            ''')
            result = cursor.fetchone()
            
            if not result:
                print("❌ 未找到子目录数据")
                return
            
            sub_id, main_name, sub_name, sub_url = result
            print(f"\n📁 测试目录: {main_name} > {sub_name}")
            print(f"   URL: {sub_url}")
            
            # 清空该子目录的产品数据
            cursor.execute('DELETE FROM product_urls WHERE sub_category_id = ?', (sub_id,))
            conn.commit()
            
            print(f"\n⏳ 开始采集...")
            
            # 单独采集这个子目录
            spider.crawl_single_sub_category(sub_id, reset_first=False)
            
            # 等待完成
            import time
            while spider.is_running:
                time.sleep(1)
            
            print(f"\n📊 采集结果验证:")
            print("=" * 50)
            
            # 检查采集结果
            cursor.execute('''
                SELECT product_name, product_url, product_sku, product_price
                FROM product_urls 
                WHERE sub_category_id = ?
                LIMIT 10
            ''', (sub_id,))
            
            products = cursor.fetchall()
            
            if products:
                print(f"✅ 成功采集 {len(products)} 个产品")
                print(f"\n📋 产品样例 (前10个):")
                print("-" * 80)
                print(f"{'产品名称':<40} {'URL':<30} {'SKU':<10} {'价格':<10}")
                print("-" * 80)
                
                for name, url, sku, price in products:
                    name_short = (name[:37] + "...") if len(name) > 40 else name
                    url_short = (url[:27] + "...") if len(url) > 30 else url
                    sku_display = sku if sku else "空"
                    price_display = price if price else "空"
                    
                    print(f"{name_short:<40} {url_short:<30} {sku_display:<10} {price_display:<10}")
                
                # 验证修正效果
                print(f"\n🔍 修正效果验证:")
                print("-" * 50)
                
                # 统计SKU和价格的情况
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total,
                        COUNT(CASE WHEN product_sku IS NOT NULL AND product_sku != '' THEN 1 END) as has_sku,
                        COUNT(CASE WHEN product_price IS NOT NULL AND product_price != '' THEN 1 END) as has_price
                    FROM product_urls 
                    WHERE sub_category_id = ?
                ''', (sub_id,))
                
                total, has_sku, has_price = cursor.fetchone()
                
                print(f"总产品数: {total}")
                print(f"有SKU的产品: {has_sku} ({has_sku/total*100:.1f}%)")
                print(f"有价格的产品: {has_price} ({has_price/total*100:.1f}%)")
                
                if has_sku == 0 and has_price == 0:
                    print("✅ 修正成功！第二阶段没有收集SKU和价格")
                    print("   这些信息将在第三阶段访问产品详情页时收集")
                else:
                    print("⚠️ 仍在收集SKU/价格，需要进一步修正")
                
            else:
                print("❌ 未采集到产品")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n📁 测试数据库: {test_db}")
    print("   可以使用数据库工具查看详细数据")

def main():
    """主函数"""
    print("🎯 修正版第二阶段测试")
    print("\n功能说明:")
    print("1. 验证第二阶段只收集产品URL和名称")
    print("2. 确认不收集SKU、价格等详细信息")
    print("3. 为第三阶段的详情页采集做准备")
    
    test_stage2_corrected()
    
    print(f"\n💡 阶段职责说明:")
    print("   第一阶段: 收集分类URL结构")
    print("   第二阶段: 在分类页面翻页，收集产品URL (不访问产品详情页)")
    print("   第三阶段: 访问产品详情页，收集SKU、价格等详细信息")

if __name__ == "__main__":
    main()
