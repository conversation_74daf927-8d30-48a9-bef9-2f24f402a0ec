#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试HTML解析器
"""

from bs4 import BeautifulSoup

def debug_parse():
    """调试解析过程"""
    with open("1.txt", 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    print(f"HTML内容长度: {len(html_content)}")
    
    # 查找所有表格
    tables = soup.find_all('table')
    print(f"找到 {len(tables)} 个表格")
    
    for i, table in enumerate(tables):
        print(f"\n=== 表格 {i+1} ===")
        
        # 查找所有行
        rows = table.find_all('tr')
        print(f"表格有 {len(rows)} 行")
        
        for j, row in enumerate(rows):
            style = row.get('style', '')
            print(f"行 {j+1}: style='{style}'")
            
            # 检查是否是主目录行
            if '#d85d4a' in style:
                print(f"  -> 这是主目录行!")
                
                # 查找链接
                links = row.find_all('a')
                print(f"  -> 找到 {len(links)} 个链接")
                
                for k, link in enumerate(links):
                    link_style = link.get('style', '')
                    text = link.get_text(strip=True)
                    href = link.get('href', '')
                    print(f"    链接 {k+1}: style='{link_style}', text='{text}', href='{href}'")
                    
                    if 'white' in link_style:
                        print(f"      -> 这是主目录链接!")
            
            # 检查是否是子目录行
            else:
                links = row.find_all('a')
                for link in links:
                    text = link.get_text(strip=True)
                    if text.startswith('>') or text.startswith('&gt;'):
                        href = link.get('href', '')
                        print(f"  -> 子目录: '{text}' -> '{href}'")

if __name__ == "__main__":
    debug_parse()
