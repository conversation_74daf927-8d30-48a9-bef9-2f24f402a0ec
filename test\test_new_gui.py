#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的GUI界面功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import DatabaseManager
from html_parser import NovolineParser

def setup_test_data():
    """设置测试数据"""
    print("设置测试数据...")
    
    # 创建数据库
    db = DatabaseManager("test_gui.db")
    
    # 如果1.txt存在，解析并插入数据
    if os.path.exists("../1.txt"):
        parser = NovolineParser()
        categories = parser.parse_categories_from_file("../1.txt")
        
        if categories:
            print(f"解析出 {len(categories)} 个主目录")
            
            for category_data in categories:
                main_cat = category_data['main_category']
                
                # 插入主目录
                main_cat_id = db.insert_main_category(
                    main_cat['name'],
                    main_cat['url'],
                    main_cat.get('icon_url', '')
                )
                
                # 插入子目录
                for sub_cat in category_data['sub_categories']:
                    db.insert_sub_category(
                        main_cat_id,
                        sub_cat['name'],
                        sub_cat['url']
                    )
            
            print("✅ 测试数据设置完成")
            return True
    
    print("❌ 未找到1.txt文件或解析失败")
    return False

def test_gui_with_data():
    """测试带有数据的GUI"""
    if setup_test_data():
        print("\n启动GUI测试...")
        print("请在GUI中测试以下功能：")
        print("1. 查看各个Tab页面")
        print("2. 检查第一步中的目录数据显示")
        print("3. 测试数据查看Tab")
        print("4. 尝试开始第一阶段爬取")
        
        # 启动GUI
        import tkinter as tk
        sys.path.append('..')
        from gui import SpiderGUI
        
        # 修改数据库路径
        app = SpiderGUI()
        app.db = DatabaseManager("test/test_gui.db")
        app.spider.db = DatabaseManager("test/test_gui.db")
        
        app.run()

if __name__ == "__main__":
    test_gui_with_data()
