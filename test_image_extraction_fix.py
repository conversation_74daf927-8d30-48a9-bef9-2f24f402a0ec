#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片提取修复效果
"""

from bs4 import BeautifulSoup
from urllib.parse import urljoin
import re

def test_improved_image_extraction():
    """测试改进的图片提取功能"""
    print("🧪 测试改进的图片提取功能...")
    
    # 读取HTML文件
    with open('docs/6.txt', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    base_url = "https://novolinepromo.com/"
    
    def is_valid_product_image(url: str) -> bool:
        """判断是否是有效的产品图片"""
        if not url:
            return False
            
        invalid_patterns = ['logo', 'icon', 'button', 'arrow', 'placeholder', 'loading', 'spinner']
        url_lower = url.lower()
        
        # 检查文件扩展名
        valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        if not any(ext in url_lower for ext in valid_extensions):
            return False
        
        # 检查是否包含无效模式
        if any(pattern in url_lower for pattern in invalid_patterns):
            return False
        
        return True
    
    def extract_image_urls_improved(soup, base_url):
        """改进的图片提取函数"""
        image_urls = []
        
        try:
            # 增强的图片选择器
            image_selectors = [
                # 标准WooCommerce选择器
                '.woocommerce-product-gallery img',
                '.product-images img',
                '.product-gallery img',
                '.wp-post-image',
                '.attachment-woocommerce_single',
                '.product-image img',
                
                # 新增的选择器（针对Novoline网站的特殊结构）
                '.images img',  # 主图片容器
                '.nickx_product_images_with_video img',  # 特定的图片视频容器
                '.nickx-slider img',  # 滑动器中的图片
                '.nswiper-slide img',  # Swiper滑动项中的图片
                '.nswiper-wrapper img',  # Swiper包装器中的图片
                'div[class*="slider"] img',  # 任何包含slider的div中的图片
                'div[class*="swiper"] img',  # 任何包含swiper的div中的图片
            ]
            
            found_images = set()  # 使用set避免重复
            
            for selector in image_selectors:
                images = soup.select(selector)
                for img in images:
                    # 尝试多种src属性
                    src_attrs = ['src', 'data-src', 'data-lazy-src', 'data-zoom-image', 'data-o_src']
                    
                    for attr in src_attrs:
                        img_url = img.get(attr)
                        if img_url:
                            # 转换为绝对URL
                            full_url = urljoin(base_url, img_url)
                            
                            # 过滤掉明显不是产品图片的URL
                            if is_valid_product_image(full_url):
                                found_images.add(full_url)
                            break  # 找到一个有效URL就跳出
            
            # 如果通过标准选择器没找到图片，尝试通过产品信息匹配
            if not found_images:
                all_imgs = soup.find_all('img')
                for img in all_imgs:
                    alt = img.get('alt', '').lower()
                    src = img.get('src', '').lower()
                    
                    # 检查是否包含产品相关关键词
                    if any(keyword in alt or keyword in src for keyword in ['product', 'novo', 'creative', 'outdoor']):
                        for attr in ['src', 'data-src', 'data-lazy-src', 'data-zoom-image']:
                            img_url = img.get(attr)
                            if img_url:
                                full_url = urljoin(base_url, img_url)
                                if is_valid_product_image(full_url):
                                    found_images.add(full_url)
                                break
            
            # 转换为列表并限制数量
            image_urls = list(found_images)[:10]  # 最多10张图片
            
        except Exception as e:
            print(f"提取图片URL失败: {e}")
        
        return image_urls
    
    # 测试改进的提取功能
    print(f"\n📊 测试结果:")
    
    # 1. 测试各个选择器的效果
    image_selectors = [
        '.woocommerce-product-gallery img',
        '.images img',
        '.nickx_product_images_with_video img',
        '.nickx-slider img',
        '.nswiper-slide img',
        '.nswiper-wrapper img',
    ]
    
    print(f"\n🔍 各选择器测试结果:")
    for selector in image_selectors:
        images = soup.select(selector)
        print(f"   {selector}: {len(images)} 张图片")
        
        if images:
            for i, img in enumerate(images[:2], 1):  # 只显示前2张
                src = img.get('src') or img.get('data-src') or img.get('data-zoom-image')
                print(f"      图片{i}: {src}")
    
    # 2. 测试完整的提取功能
    extracted_urls = extract_image_urls_improved(soup, base_url)
    
    print(f"\n🎯 完整提取结果:")
    print(f"   提取到的图片数量: {len(extracted_urls)}")
    
    for i, url in enumerate(extracted_urls, 1):
        print(f"   图片{i}: {url}")
    
    # 3. 分析图片质量
    print(f"\n📈 图片质量分析:")
    high_res_count = 0
    medium_res_count = 0
    low_res_count = 0
    
    for url in extracted_urls:
        if '600x600' in url or '768x768' in url or '800w' in url:
            high_res_count += 1
        elif '300x300' in url or '150x150' in url:
            medium_res_count += 1
        else:
            low_res_count += 1
    
    print(f"   高分辨率图片: {high_res_count} 张")
    print(f"   中分辨率图片: {medium_res_count} 张")
    print(f"   其他图片: {low_res_count} 张")
    
    return len(extracted_urls)

def compare_with_database():
    """与数据库记录对比"""
    print(f"\n📊 与数据库记录对比:")
    
    # 数据库中的记录显示
    print(f"   数据库记录:")
    print(f"      产品名称: Leather Beverage Holder with Lanyard")
    print(f"      采集状态: success")
    print(f"      图片数量: 0 (所有image字段都是NULL)")
    print(f"      属性解析: ✅ 成功 (Material: Leather, Size: 7.68*4.49 inches)")
    
    print(f"\n   问题分析:")
    print(f"      ✅ 属性解析正常工作")
    print(f"      ❌ 图片提取完全失败")
    print(f"      🔍 原因: 现有选择器无法匹配Swiper滑动组件")

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 图片提取修复效果测试")
    print("=" * 60)
    print("测试修复后的图片提取功能")
    print()
    
    # 测试改进的图片提取
    extracted_count = test_improved_image_extraction()
    
    # 与数据库对比
    compare_with_database()
    
    print("\n" + "=" * 60)
    print("🎯 测试结果总结")
    print("=" * 60)
    
    if extracted_count > 0:
        print(f"✅ 修复成功！")
        print(f"✅ 提取到 {extracted_count} 张产品图片")
        print(f"✅ 新选择器有效匹配Swiper组件")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重新启动步骤四采集")
        print(f"   2. 观察图片下载是否正常")
        print(f"   3. 检查数据库中的图片字段")
    else:
        print(f"❌ 修复失败")
        print(f"❌ 仍然无法提取图片")
        print(f"⚠️ 需要进一步调试")

if __name__ == "__main__":
    main()
