#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def main():
    try:
        conn = sqlite3.connect('novoline_spider_v2.db')
        cursor = conn.cursor()

        print('=== 数据库状态检查 ===')

        # 总分类数
        cursor.execute('SELECT COUNT(*) FROM sub_categories')
        total_cats = cursor.fetchone()[0]
        print(f'总分类数: {total_cats}')

        # 总产品数
        cursor.execute('SELECT COUNT(*) FROM product_urls')
        total_products = cursor.fetchone()[0]
        print(f'总产品URL: {total_products}')

        # 未采集分类
        cursor.execute('''
            SELECT COUNT(*) FROM sub_categories sc
            LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
            WHERE pu.id IS NULL
        ''')
        uncrawled = cursor.fetchone()[0]
        print(f'未采集分类: {uncrawled}')

        # 最近添加的产品
        cursor.execute('''
            SELECT COUNT(*) FROM product_urls
            WHERE created_at > datetime("now", "-1 hour")
        ''')
        recent = cursor.fetchone()[0]
        print(f'最近1小时新增: {recent}')

        # 显示未采集的分类名称
        if uncrawled > 0:
            print(f'\n未采集的分类:')
            cursor.execute('''
                SELECT sc.sub_category_name FROM sub_categories sc
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                WHERE pu.id IS NULL
                ORDER BY sc.id
                LIMIT 10
            ''')
            for row in cursor.fetchall():
                print(f'  - {row[0]}')

        conn.close()
        
    except Exception as e:
        print(f'错误: {e}')

if __name__ == "__main__":
    main()
