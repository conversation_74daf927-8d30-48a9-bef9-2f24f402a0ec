#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彻底修复所有created_at字段问题
确保数据库中所有表都有正确的字段结构
"""

import os
import sqlite3
import shutil
from datetime import datetime

def backup_database(db_path):
    """备份数据库"""
    if not os.path.exists(db_path):
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = "backup"
    os.makedirs(backup_dir, exist_ok=True)
    backup_path = f"{backup_dir}/{os.path.basename(db_path)}.backup_{timestamp}"
    
    shutil.copy2(db_path, backup_path)
    print(f"✅ 数据库已备份到: {backup_path}")
    return backup_path

def check_and_fix_table(cursor, table_name, expected_columns):
    """检查并修复表结构"""
    print(f"🔧 检查表: {table_name}")
    
    # 检查表是否存在
    cursor.execute('''
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
    ''', (table_name,))
    
    if not cursor.fetchone():
        print(f"   表 {table_name} 不存在，跳过")
        return
    
    # 获取当前表结构
    cursor.execute(f"PRAGMA table_info({table_name})")
    current_columns = {row[1]: row[2] for row in cursor.fetchall()}
    print(f"   当前列: {list(current_columns.keys())}")
    
    # 检查是否需要添加字段
    missing_columns = []
    for col_name, col_type in expected_columns.items():
        if col_name not in current_columns:
            missing_columns.append((col_name, col_type))
    
    if missing_columns:
        print(f"   需要添加字段: {missing_columns}")
        for col_name, col_type in missing_columns:
            try:
                if 'TIMESTAMP' in col_type.upper():
                    cursor.execute(f'''
                        ALTER TABLE {table_name} 
                        ADD COLUMN {col_name} {col_type} DEFAULT CURRENT_TIMESTAMP
                    ''')
                else:
                    cursor.execute(f'''
                        ALTER TABLE {table_name} 
                        ADD COLUMN {col_name} {col_type}
                    ''')
                print(f"   ✅ 添加字段: {col_name}")
            except Exception as e:
                print(f"   ❌ 添加字段失败 {col_name}: {e}")
    else:
        print(f"   ✅ 表结构正确")

def fix_all_tables(db_path):
    """修复所有表的结构"""
    print(f"🔧 开始修复数据库: {db_path}")
    
    # 备份数据库
    backup_path = backup_database(db_path)
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 定义所有表的期望结构
            table_structures = {
                'main_categories': {
                    'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
                    'category_name': 'TEXT NOT NULL UNIQUE',
                    'category_url': 'TEXT NOT NULL',
                    'icon_url': 'TEXT',
                    'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'updated_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
                },
                'sub_categories': {
                    'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
                    'main_category_id': 'INTEGER',
                    'sub_category_name': 'TEXT NOT NULL',
                    'sub_category_url': 'TEXT NOT NULL',
                    'product_count': 'INTEGER DEFAULT 0',
                    'page_count': 'INTEGER DEFAULT 0',
                    'last_crawled_at': 'TIMESTAMP',
                    'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'updated_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
                },
                'product_urls': {
                    'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
                    'sub_category_id': 'INTEGER',
                    'product_name': 'TEXT NOT NULL',
                    'product_url': 'TEXT NOT NULL UNIQUE',
                    'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'updated_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
                },
                'crawl_progress': {
                    'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
                    'stage': 'TEXT NOT NULL',
                    'status': 'TEXT NOT NULL',
                    'total_items': 'INTEGER DEFAULT 0',
                    'completed_items': 'INTEGER DEFAULT 0',
                    'error_message': 'TEXT',
                    'started_at': 'TIMESTAMP',
                    'completed_at': 'TIMESTAMP',
                    'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'updated_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
                },
                'error_logs': {
                    'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
                    'stage': 'TEXT NOT NULL',
                    'url': 'TEXT',
                    'error_type': 'TEXT NOT NULL',
                    'error_message': 'TEXT NOT NULL',
                    'stack_trace': 'TEXT',
                    'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
                }
            }
            
            # 修复每个表
            for table_name, expected_columns in table_structures.items():
                check_and_fix_table(cursor, table_name, expected_columns)
            
            # 确保product_details表存在（如果不存在则创建）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_details (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_url_id INTEGER NOT NULL UNIQUE,
                    product_sku TEXT,
                    product_image_url TEXT,
                    product_price TEXT,
                    short_description TEXT,
                    title TEXT,
                    nav_level1 TEXT,
                    nav_level2 TEXT,
                    nav_level3 TEXT,
                    nav_full TEXT,
                    category_name TEXT,
                    category_url TEXT,
                    regular_price DECIMAL(10,2),
                    sale_price DECIMAL(10,2),
                    currency TEXT DEFAULT 'USD',
                    price_data_raw TEXT,
                    crawl_status TEXT DEFAULT 'pending',
                    crawl_attempts INTEGER DEFAULT 0,
                    last_crawl_attempt TIMESTAMP,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_url_id) REFERENCES product_urls (id)
                )
            ''')
            
            # 确保tier_prices表存在（如果不存在则创建）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tier_prices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_detail_id INTEGER,
                    quantity_min INTEGER NOT NULL,
                    quantity_max INTEGER,
                    quantity_display TEXT,
                    unit_price DECIMAL(10, 2) NOT NULL,
                    currency TEXT DEFAULT 'USD',
                    discount_percent INTEGER,
                    raw_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_detail_id) REFERENCES product_details (id)
                )
            ''')
            
            conn.commit()
            
        print("✅ 数据库结构修复完成")
        
        # 验证修复结果
        print("\n🔍 验证修复结果:")
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            for table_name in table_structures.keys():
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [row[1] for row in cursor.fetchall()]
                print(f"   {table_name}: {columns}")
        
        # 测试关键操作
        print("\n🧪 测试关键操作:")
        from database import DatabaseManager
        db = DatabaseManager(db_path)
        
        # 测试get_crawl_progress
        progress = db.get_crawl_progress()
        print(f"   ✅ get_crawl_progress: {len(progress)} 条记录")
        
        # 测试insert_product_url
        test_id = db.insert_product_url(1, 'Test Product', 'https://test.com/test')
        print(f"   ✅ insert_product_url: ID {test_id}")
        
        # 清理测试数据
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM product_urls WHERE id = ?', (test_id,))
            conn.commit()
        print("   ✅ 测试数据已清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 恢复备份
        if backup_path and os.path.exists(backup_path):
            print("🔄 正在恢复备份...")
            shutil.copy2(backup_path, db_path)
            print("✅ 已恢复到备份版本")
        
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Novoline爬虫 - 彻底修复created_at字段问题")
    print("=" * 60)
    print("此工具将:")
    print("1. 检查所有表的字段结构")
    print("2. 添加缺失的created_at和updated_at字段")
    print("3. 确保所有表结构正确")
    print()
    
    db_path = "novoline_spider_v2.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        print("请确保您在正确的目录中运行此脚本")
        return
    
    print(f"📁 找到数据库文件: {db_path}")
    
    # 确认修复
    confirm = input(f"\n确认修复所有表结构? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 修复已取消")
        return
    
    # 执行修复
    if fix_all_tables(db_path):
        print(f"\n🎉 数据库修复成功！")
        print("\n📋 修复总结:")
        print("   - 所有表都包含正确的created_at和updated_at字段")
        print("   - 数据库结构完全兼容代码期望")
        print("   - 状态更新功能应该正常工作")
        print("\n🎯 现在可以正常运行所有功能了！")
    else:
        print(f"\n❌ 数据库修复失败")

if __name__ == "__main__":
    main()
