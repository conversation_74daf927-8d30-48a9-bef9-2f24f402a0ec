#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫核心模块
实现分阶段的网站爬取功能
"""

import os
import time
import threading
import sqlite3
from typing import Callable, Optional
from database import DatabaseManager
from html_parser import NovolineParser
import traceback

class NovolineSpider:
    """Novoline网站爬虫核心类"""
    
    def __init__(self, db_path: str = "novoline_spider.db"):
        """
        初始化爬虫
        
        Args:
            db_path: 数据库文件路径
        """
        self.db = DatabaseManager(db_path)
        self.parser = NovolineParser()
        self.is_running = False
        self.is_paused = False
        self.current_stage = None
        self.progress_callback = None
        self.error_callback = None
        self.completion_callback = None
        
    def set_callbacks(self, progress_callback: Callable = None, 
                     error_callback: Callable = None,
                     completion_callback: Callable = None):
        """
        设置回调函数
        
        Args:
            progress_callback: 进度更新回调
            error_callback: 错误回调
            completion_callback: 完成回调
        """
        self.progress_callback = progress_callback
        self.error_callback = error_callback
        self.completion_callback = completion_callback
    
    def start_crawling(self, stage: str = "categories", restart: bool = False):
        """
        开始爬取
        
        Args:
            stage: 开始阶段 ('categories', 'products', 'prices')
            restart: 是否重新开始
        """
        if self.is_running:
            return
        
        self.is_running = True
        self.is_paused = False
        
        # 如果是重新开始，清空相关数据
        if restart:
            if stage == "categories":
                self.db.clear_stage_data("categories")
            elif stage == "products":
                self.db.clear_stage_data("products")
            elif stage == "prices":
                self.db.clear_stage_data("prices")
        
        # 在新线程中运行爬虫
        thread = threading.Thread(target=self._run_crawler, args=(stage,))
        thread.daemon = True
        thread.start()
    
    def pause_crawling(self):
        """暂停爬取"""
        self.is_paused = True
    
    def resume_crawling(self):
        """恢复爬取"""
        self.is_paused = False
    
    def stop_crawling(self):
        """停止爬取"""
        self.is_running = False
        self.is_paused = False
    
    def _run_crawler(self, start_stage: str):
        """
        运行爬虫主逻辑
        
        Args:
            start_stage: 开始阶段
        """
        try:
            stages = ["categories", "products", "prices"]
            start_index = stages.index(start_stage)
            
            for stage in stages[start_index:]:
                if not self.is_running:
                    break
                
                self.current_stage = stage
                
                if stage == "categories":
                    self._crawl_categories()
                elif stage == "products":
                    self._crawl_products()
                elif stage == "prices":
                    self._crawl_prices()
                
                if not self.is_running:
                    break
            
            if self.is_running:
                self._notify_completion("所有阶段爬取完成")
            
        except Exception as e:
            error_msg = f"爬虫运行出错: {str(e)}"
            self._notify_error(error_msg, traceback.format_exc())
        finally:
            self.is_running = False
            self.current_stage = None
    
    def _crawl_categories(self):
        """爬取目录结构"""
        try:
            self._notify_progress("categories", "开始解析目录结构...")
            self.db.update_crawl_progress("categories", "in_progress")
            
            # 从本地文件解析目录结构
            categories = self.parser.parse_categories_from_file("1.txt")
            
            if not categories:
                raise Exception("未能从文件中解析出目录结构")
            
            total_categories = len(categories)
            self.db.update_crawl_progress("categories", "in_progress", total_categories, 0)
            
            completed = 0
            for category_data in categories:
                if not self.is_running:
                    break
                
                # 等待暂停状态结束
                while self.is_paused and self.is_running:
                    time.sleep(0.5)
                
                if not self.is_running:
                    break
                
                try:
                    main_cat = category_data['main_category']
                    
                    # 插入主目录
                    main_cat_id = self.db.insert_main_category(
                        main_cat['name'],
                        main_cat['url'],
                        main_cat.get('icon_url', '')
                    )
                    
                    # 插入子目录
                    for sub_cat in category_data['sub_categories']:
                        self.db.insert_sub_category(
                            main_cat_id,
                            sub_cat['name'],
                            sub_cat['url']
                        )
                    
                    completed += 1
                    self.db.update_crawl_progress("categories", "in_progress", total_categories, completed)
                    self._notify_progress("categories", f"已处理 {completed}/{total_categories} 个主目录")
                    
                except Exception as e:
                    error_msg = f"处理目录 {main_cat['name']} 时出错: {str(e)}"
                    self.db.log_error("categories", main_cat['url'], "ProcessError", error_msg)
                    self._notify_error(error_msg)
                    continue
            
            if self.is_running:
                self.db.update_crawl_progress("categories", "completed", total_categories, completed)
                self._notify_progress("categories", f"目录结构爬取完成，共处理 {completed} 个主目录")
            
        except Exception as e:
            error_msg = f"目录结构爬取失败: {str(e)}"
            self.db.update_crawl_progress("categories", "failed", error_message=error_msg)
            self.db.log_error("categories", "", "CrawlError", error_msg, traceback.format_exc())
            self._notify_error(error_msg)
    
    def _crawl_products(self):
        """爬取产品URL"""
        try:
            self._notify_progress("products", "开始爬取产品URL...")
            self.db.update_crawl_progress("products", "in_progress")
            
            # 获取所有子目录
            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT sc.id, sc.sub_category_name, sc.sub_category_url, mc.category_name
                    FROM sub_categories sc
                    JOIN main_categories mc ON sc.main_category_id = mc.id
                    ORDER BY mc.id, sc.id
                ''')
                sub_categories = cursor.fetchall()
            
            if not sub_categories:
                raise Exception("没有找到子目录，请先完成目录结构爬取")
            
            total_subcats = len(sub_categories)
            self.db.update_crawl_progress("products", "in_progress", total_subcats, 0)
            
            completed = 0
            total_products = 0
            
            for sub_cat_id, sub_cat_name, sub_cat_url, main_cat_name in sub_categories:
                if not self.is_running:
                    break
                
                # 等待暂停状态结束
                while self.is_paused and self.is_running:
                    time.sleep(0.5)
                
                if not self.is_running:
                    break
                
                try:
                    self._notify_progress("products", f"正在爬取 {main_cat_name} > {sub_cat_name}")

                    # 解析产品列表（新版本返回字典包含统计信息）
                    result = self.parser.parse_product_list(sub_cat_url)
                    products = result.get('products', [])
                    page_count = result.get('page_count', 0)
                    category_total = result.get('total_count', 0)

                    # 插入产品基本信息（第二阶段：只收集URL和名称）
                    category_products = 0
                    for product in products:
                        if not self.is_running:
                            break

                        self.db.insert_product_url(
                            sub_cat_id,
                            product['name'],
                            product['url']
                            # SKU、图片、价格等详细信息在第三阶段收集
                        )
                        category_products += 1
                        total_products += 1

                    # 更新子目录统计信息
                    self.db.update_sub_category_stats(sub_cat_id, category_products, page_count)

                    completed += 1
                    self.db.update_crawl_progress("products", "in_progress", total_subcats, completed)
                    self._notify_progress("products",
                                        f"已处理 {completed}/{total_subcats} 个子目录，{sub_cat_name}: {category_products}个产品({page_count}页)，总计 {total_products} 个产品")

                    # 添加延迟避免过于频繁的请求
                    time.sleep(2)
                    
                except Exception as e:
                    error_msg = f"处理子目录 {sub_cat_name} 时出错: {str(e)}"
                    self.db.log_error("products", sub_cat_url, "ProcessError", error_msg)
                    self._notify_error(error_msg)
                    continue
            
            if self.is_running:
                self.db.update_crawl_progress("products", "completed", total_subcats, completed)
                self._notify_progress("products", f"产品URL爬取完成，共处理 {completed} 个子目录，找到 {total_products} 个产品")
            
        except Exception as e:
            error_msg = f"产品URL爬取失败: {str(e)}"
            self.db.update_crawl_progress("products", "failed", error_message=error_msg)
            self.db.log_error("products", "", "CrawlError", error_msg, traceback.format_exc())
            self._notify_error(error_msg)
    
    def _crawl_prices(self):
        """爬取阶梯价格"""
        try:
            self._notify_progress("prices", "开始爬取阶梯价格...")
            self.db.update_crawl_progress("prices", "in_progress")
            
            # 获取所有产品URL
            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT id, product_name, product_url FROM product_urls ORDER BY id')
                products = cursor.fetchall()
            
            if not products:
                raise Exception("没有找到产品URL，请先完成产品URL爬取")
            
            total_products = len(products)
            self.db.update_crawl_progress("prices", "in_progress", total_products, 0)
            
            completed = 0
            total_prices = 0
            
            for product_id, product_name, product_url in products:
                if not self.is_running:
                    break
                
                # 等待暂停状态结束
                while self.is_paused and self.is_running:
                    time.sleep(0.5)
                
                if not self.is_running:
                    break
                
                try:
                    self._notify_progress("prices", f"正在爬取产品价格: {product_name}")
                    
                    # 解析产品详情页面
                    product_details = self.parser.parse_product_details(product_url)
                    
                    # 插入阶梯价格
                    for tier_price in product_details.get('tier_prices', []):
                        self.db.insert_tier_price(
                            product_id,
                            tier_price['quantity_min'],
                            tier_price.get('quantity_max'),
                            tier_price['price'],
                            tier_price.get('currency', 'USD')
                        )
                        total_prices += 1
                    
                    completed += 1
                    self.db.update_crawl_progress("prices", "in_progress", total_products, completed)
                    
                    if completed % 10 == 0:  # 每10个产品更新一次进度
                        self._notify_progress("prices", 
                                            f"已处理 {completed}/{total_products} 个产品，共找到 {total_prices} 条价格记录")
                    
                    # 添加延迟避免过于频繁的请求
                    time.sleep(3)
                    
                except Exception as e:
                    error_msg = f"处理产品 {product_name} 价格时出错: {str(e)}"
                    self.db.log_error("prices", product_url, "ProcessError", error_msg)
                    self._notify_error(error_msg)
                    continue
            
            if self.is_running:
                self.db.update_crawl_progress("prices", "completed", total_products, completed)
                self._notify_progress("prices", f"阶梯价格爬取完成，共处理 {completed} 个产品，找到 {total_prices} 条价格记录")
            
        except Exception as e:
            error_msg = f"阶梯价格爬取失败: {str(e)}"
            self.db.update_crawl_progress("prices", "failed", error_message=error_msg)
            self.db.log_error("prices", "", "CrawlError", error_msg, traceback.format_exc())
            self._notify_error(error_msg)
    
    def _notify_progress(self, stage: str, message: str):
        """通知进度更新"""
        if self.progress_callback:
            self.progress_callback(stage, message)
    
    def _notify_error(self, message: str, stack_trace: str = None):
        """通知错误"""
        if self.error_callback:
            self.error_callback(message, stack_trace)
    
    def _notify_completion(self, message: str):
        """通知完成"""
        if self.completion_callback:
            self.completion_callback(message)
    
    def get_status(self) -> dict:
        """
        获取当前状态
        
        Returns:
            状态信息字典
        """
        progress_data = self.db.get_crawl_progress()
        stats = self.db.get_statistics()
        
        return {
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'current_stage': self.current_stage,
            'progress': progress_data,
            'statistics': stats
        }

    def _crawl_products_for_category(self, sub_category_id: int, sub_url: str, sub_name: str):
        """
        为单个分类采集产品URL

        Args:
            sub_category_id: 子目录ID
            sub_url: 子目录URL
            sub_name: 子目录名称

        Returns:
            采集到的产品列表
        """
        try:
            # 解析产品列表（修正版：只收集URL和名称）
            result = self.parser.parse_product_list(sub_url)
            products = result.get('products', [])
            page_count = result.get('page_count', 0)

            # 插入产品基本信息
            category_products = 0
            for product in products:
                if not self.is_running:
                    break

                self.db.insert_product_url(
                    sub_category_id,
                    product['name'],
                    product['url']
                    # 第二阶段：不收集SKU、价格等详细信息
                )
                category_products += 1

            # 更新子目录统计信息
            self.db.update_sub_category_stats(sub_category_id, category_products, page_count)

            return products

        except Exception as e:
            self._notify_error(f"采集分类 {sub_name} 失败: {e}")
            return []

    def crawl_single_sub_category(self, sub_category_id: int, reset_first: bool = True):
        """
        单独采集指定的子目录

        Args:
            sub_category_id: 子目录ID
            reset_first: 是否先重置该子目录的数据
        """
        if self.is_running:
            self._notify_error("爬虫正在运行中，无法启动单独采集")
            return

        try:
            # 获取子目录信息
            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT sc.sub_category_name, sc.sub_category_url, mc.category_name
                    FROM sub_categories sc
                    JOIN main_categories mc ON sc.main_category_id = mc.id
                    WHERE sc.id = ?
                ''', (sub_category_id,))
                result = cursor.fetchone()

                if not result:
                    self._notify_error(f"未找到子目录 ID: {sub_category_id}")
                    return

                sub_name, sub_url, main_name = result

            self._notify_progress("products", f"开始单独采集: {main_name} > {sub_name}")

            # 重置数据（如果需要）
            if reset_first:
                self.db.reset_sub_category_crawl_status(sub_category_id)
                self._notify_progress("products", f"已重置 {sub_name} 的采集数据")

            # 设置运行状态
            self.is_running = True
            self.current_stage = "products"

            # 在新线程中运行
            thread = threading.Thread(target=self._crawl_single_sub_category_worker,
                                    args=(sub_category_id, sub_name, sub_url))
            thread.daemon = True
            thread.start()

        except Exception as e:
            self._notify_error(f"启动单独采集失败: {e}")

    def _crawl_single_sub_category_worker(self, sub_category_id: int, sub_name: str, sub_url: str):
        """单独采集子目录的工作线程"""
        try:
            self._notify_progress("products", f"正在采集 {sub_name}...")

            # 使用现有的产品采集逻辑
            products = self._crawl_products_for_category(sub_category_id, sub_url, sub_name)

            if products:
                self._notify_progress("products", f"✅ {sub_name} 采集完成: {len(products)}个产品")
            else:
                self._notify_progress("products", f"⚠️ {sub_name} 未采集到产品")

            self._notify_completion(f"单独采集完成: {sub_name}")

        except Exception as e:
            self._notify_error(f"单独采集 {sub_name} 失败: {e}")
        finally:
            self.is_running = False
            self.current_stage = None

    def crawl_single_main_category(self, main_category_id: int, reset_first: bool = True):
        """
        单独采集指定的主目录下所有子目录

        Args:
            main_category_id: 主目录ID
            reset_first: 是否先重置该主目录的数据
        """
        if self.is_running:
            self._notify_error("爬虫正在运行中，无法启动单独采集")
            return

        try:
            # 获取主目录信息
            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT category_name FROM main_categories WHERE id = ?', (main_category_id,))
                result = cursor.fetchone()

                if not result:
                    self._notify_error(f"未找到主目录 ID: {main_category_id}")
                    return

                main_name = result[0]

            self._notify_progress("products", f"开始单独采集主目录: {main_name}")

            # 重置数据（如果需要）
            if reset_first:
                self.db.reset_main_category_crawl_status(main_category_id)
                self._notify_progress("products", f"已重置 {main_name} 的采集数据")

            # 设置运行状态
            self.is_running = True
            self.current_stage = "products"

            # 在新线程中运行
            thread = threading.Thread(target=self._crawl_single_main_category_worker,
                                    args=(main_category_id, main_name))
            thread.daemon = True
            thread.start()

        except Exception as e:
            self._notify_error(f"启动主目录单独采集失败: {e}")

    def _crawl_single_main_category_worker(self, main_category_id: int, main_name: str):
        """单独采集主目录的工作线程"""
        try:
            # 获取该主目录下的所有子目录
            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, sub_category_name, sub_category_url
                    FROM sub_categories
                    WHERE main_category_id = ?
                    ORDER BY id
                ''', (main_category_id,))
                sub_categories = cursor.fetchall()

            if not sub_categories:
                self._notify_progress("products", f"⚠️ {main_name} 下没有子目录")
                return

            total_products = 0

            for i, (sub_id, sub_name, sub_url) in enumerate(sub_categories, 1):
                if self.should_stop:
                    break

                # 检查暂停
                while self.is_paused and not self.should_stop:
                    time.sleep(1)

                if self.should_stop:
                    break

                self._notify_progress("products",
                    f"正在采集 {main_name} > {sub_name} ({i}/{len(sub_categories)})")

                try:
                    products = self._crawl_products_for_category(sub_id, sub_url, sub_name)
                    if products:
                        total_products += len(products)
                        self._notify_progress("products",
                            f"✅ {sub_name}: {len(products)}个产品")
                    else:
                        self._notify_progress("products", f"⚠️ {sub_name}: 未采集到产品")

                except Exception as e:
                    self._notify_progress("products", f"❌ {sub_name}: 采集失败 - {e}")

            if not self.should_stop:
                self._notify_completion(f"主目录采集完成: {main_name}, 总计 {total_products} 个产品")
            else:
                self._notify_progress("products", f"主目录采集已停止: {main_name}")

        except Exception as e:
            self._notify_error(f"单独采集主目录 {main_name} 失败: {e}")
        finally:
            self.is_running = False
            self.current_stage = None
