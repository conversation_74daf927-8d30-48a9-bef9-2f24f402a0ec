# 🖼️ 步骤四：产品图片采集功能

## 📋 功能概述

步骤四是在步骤三（产品详情采集）完成后的图片采集阶段，主要功能包括：

1. **产品图片采集**：每个产品最多采集10张图片
2. **描述信息提取**：提取产品详细描述（保留table结构）
3. **目录结构创建**：按面包屑导航创建本地目录
4. **多线程并发**：支持1-10个线程并发采集
5. **进度监控**：实时显示采集进度和统计信息
6. **错误处理**：完善的重试和错误记录机制

## 🗄️ 数据库结构

### product_images 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 主键 |
| product_url_id | INTEGER | 产品URL ID（外键） |
| product_url | TEXT | 产品URL |
| product_title | TEXT | 产品标题 |
| product_sku | TEXT | 产品SKU |
| description | TEXT | 产品描述（HTML格式，保留table） |
| breadcrumb_path | TEXT | 面包屑路径 |
| local_directory | TEXT | 本地存储目录 |
| image1_url ~ image10_url | TEXT | 图片URL（10个字段） |
| image1_local ~ image10_local | TEXT | 本地图片路径（10个字段） |
| crawl_status | TEXT | 采集状态（pending/success/failed） |
| crawl_attempts | INTEGER | 采集尝试次数 |
| error_message | TEXT | 错误信息 |
| images_downloaded | INTEGER | 成功下载的图片数量 |
| last_crawl_attempt | TIMESTAMP | 最后采集时间 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 📁 目录结构

### 本地存储结构

根据面包屑导航创建目录结构，例如：

```
images/
├── Bags & Trade Show/
│   ├── Fanny Packs/
│   │   └── Multi-Pocket Large Capacity Fanny Pack/
│   │       ├── image_1.jpg
│   │       ├── image_2.jpg
│   │       └── ...
│   └── Backpacks/
│       └── Custom Logo Backpack/
│           ├── image_1.jpg
│           └── ...
├── Drinkware & Can Cooler/
│   ├── Barware/
│   │   └── Stainless Steel Tumbler/
│   │       ├── image_1.jpg
│   │       └── ...
│   └── ...
└── ...
```

### 面包屑解析

从HTML中提取面包屑导航：
```html
<nav class="woocommerce-breadcrumb">
    <a href="https://novolinepromo.com">Home</a>&nbsp;/&nbsp;
    <a href="https://novolinepromo.com/bags-trade-show/">Bags & Trade Show</a>&nbsp;/&nbsp;
    <a href="https://novolinepromo.com/bags-trade-show/fanny-packs/">Fanny Packs</a>&nbsp;/&nbsp;
    Multi-Pocket Large Capacity Fanny Pack
</nav>
```

解析为目录路径：`Bags & Trade Show / Fanny Packs / Multi-Pocket Large Capacity Fanny Pack`

## 🔧 核心功能

### 1. 图片提取

**支持的选择器：**
- `.woocommerce-product-gallery img`
- `.product-images img`
- `.product-gallery img`
- `.wp-post-image`
- `.attachment-woocommerce_single`
- `.product-image img`

**图片过滤规则：**
- 支持的格式：jpg, jpeg, png, gif, webp
- 排除：logo, icon, button, arrow, placeholder等
- 最小尺寸：100x100像素
- 最大数量：10张图片

### 2. 描述提取

**支持的选择器：**
- `#tab-description`
- `.woocommerce-Tabs-panel--description`
- `.product-description`
- `.entry-content`
- `.product-details`

**特殊处理：**
- 保留HTML结构
- 特别保留table标签和结构
- 完整保存描述内容

### 3. 多线程采集

**线程配置：**
- 可选线程数：1, 3, 5, 8, 10
- 默认线程数：5
- 支持动态调整

**并发控制：**
- 线程安全的统计更新
- 暂停/继续/停止控制
- 错误隔离处理

## 🚀 使用方法

### 1. 准备工作

确保步骤三已完成：
```bash
# 检查产品详情数据
python -c "
import sqlite3
conn = sqlite3.connect('novoline_spider_v2.db')
cursor = conn.cursor()
cursor.execute('SELECT COUNT(*) FROM product_details WHERE crawl_status = \"success\"')
print(f'已采集产品详情: {cursor.fetchone()[0]:,}')
conn.close()
"
```

### 2. 初始化数据库

```bash
python create_stage4_table.py
```

### 3. 测试功能

```bash
python test_stage4.py
```

### 4. 启动GUI

```bash
python main.py
```

### 5. 使用步骤四

1. **进入第四步标签页**
2. **选择采集范围**：
   - `all`：所有产品
   - `pending`：未采集的产品
   - `failed`：采集失败的产品
3. **设置线程数**：1-10个线程
4. **点击"开始第四阶段"**
5. **监控进度**：观察日志和统计信息

## 📊 监控和统计

### 实时统计信息

- **总产品数**：有详情数据的产品总数
- **待处理**：尚未采集图片的产品数
- **成功**：图片采集成功的产品数
- **失败**：图片采集失败的产品数
- **总图片**：成功下载的图片总数

### 进度监控

- **实时日志**：显示当前采集的产品
- **成功率统计**：每10个产品更新一次进度
- **错误记录**：详细的错误信息和重试次数

### 控制功能

- **暂停/继续**：可以随时暂停和继续采集
- **停止**：安全停止所有采集线程
- **重新采集**：可以重新采集失败的产品

## ⚠️ 注意事项

### 1. 网络和性能

- **网络稳定性**：图片下载需要稳定的网络连接
- **磁盘空间**：确保有足够的磁盘空间存储图片
- **线程数量**：根据网络和系统性能调整线程数

### 2. 错误处理

- **网络超时**：自动重试机制
- **文件权限**：确保有写入权限
- **目录创建**：自动创建必要的目录

### 3. 数据完整性

- **匹配条件**：基于product_url_id匹配步骤三的数据
- **重复处理**：使用REPLACE INTO避免重复记录
- **事务安全**：数据库操作使用事务保护

## 🔍 故障排除

### 常见问题

1. **图片下载失败**
   - 检查网络连接
   - 检查图片URL有效性
   - 调整线程数量

2. **目录创建失败**
   - 检查磁盘空间
   - 检查文件权限
   - 检查路径长度限制

3. **数据库错误**
   - 检查数据库文件权限
   - 确保步骤三数据完整
   - 重新初始化数据库表

### 调试方法

```bash
# 检查产品数据
python -c "
from stage4_image_crawler import Stage4ImageCrawler
crawler = Stage4ImageCrawler('novoline_spider_v2.db')
products = crawler.get_products_for_image_crawl('pending')
print(f'待采集产品: {len(products)}')
"

# 检查统计信息
python -c "
from stage4_image_crawler import Stage4ImageCrawler
crawler = Stage4ImageCrawler('novoline_spider_v2.db')
stats = crawler.get_stage4_stats()
print(f'统计信息: {stats}')
"
```

## 🎯 预期效果

完成步骤四后，您将获得：

1. **完整的产品图片库**：按分类组织的产品图片
2. **详细的产品描述**：包含table结构的完整描述
3. **清晰的目录结构**：便于管理和查找
4. **完整的数据记录**：图片URL和本地路径的完整映射

这为后续的产品展示、目录生成、电商平台上传等应用提供了完整的数据基础。
