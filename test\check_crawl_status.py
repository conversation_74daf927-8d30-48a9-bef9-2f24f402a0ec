#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采集状态检查工具
检查各分类的采集状态，识别需要重新采集的分类
"""

import sqlite3
from database import DatabaseManager
from datetime import datetime

def check_crawl_status():
    """检查采集状态"""
    print("=" * 80)
    print("Novoline 采集状态检查报告")
    print("=" * 80)
    
    db = DatabaseManager()
    
    try:
        with sqlite3.connect(db.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取详细状态
            cursor.execute('''
                SELECT 
                    mc.id as main_id,
                    mc.category_name as main_name,
                    sc.id as sub_id,
                    sc.sub_category_name as sub_name,
                    sc.sub_category_url,
                    sc.product_count as recorded_count,
                    sc.page_count,
                    sc.last_crawled_at,
                    COUNT(pu.id) as actual_count
                FROM main_categories mc
                LEFT JOIN sub_categories sc ON mc.id = sc.main_category_id
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY mc.id, sc.id
                ORDER BY mc.id, sc.id
            ''')
            
            results = cursor.fetchall()
            
            # 统计信息
            total_main = 0
            total_sub = 0
            completed_sub = 0
            inconsistent_sub = 0
            not_started_sub = 0
            
            current_main_id = None
            main_total_recorded = 0
            main_total_actual = 0
            
            for row in results:
                main_id, main_name, sub_id, sub_name, sub_url, recorded_count, page_count, last_crawled, actual_count = row
                
                if sub_id is None:  # 没有子目录的主目录
                    continue
                
                # 新的主目录
                if current_main_id != main_id:
                    if current_main_id is not None:
                        # 输出上一个主目录的汇总
                        print(f"   📊 主目录汇总: 记录 {main_total_recorded} 个，实际 {main_total_actual} 个")
                        if main_total_recorded != main_total_actual:
                            print(f"   ⚠️  主目录数量不一致！")
                        print()
                    
                    current_main_id = main_id
                    total_main += 1
                    main_total_recorded = 0
                    main_total_actual = 0
                    
                    print(f"🗂️  主目录: {main_name}")
                
                # 子目录统计
                total_sub += 1
                main_total_recorded += recorded_count or 0
                main_total_actual += actual_count
                
                # 判断状态
                if last_crawled is None:
                    status = "⏳ 未开始"
                    not_started_sub += 1
                elif recorded_count == actual_count and recorded_count > 0:
                    status = "✅ 已完成"
                    completed_sub += 1
                elif recorded_count != actual_count:
                    status = "⚠️  数量不一致"
                    inconsistent_sub += 1
                else:
                    status = "❓ 状态未知"
                
                # 输出子目录信息
                print(f"   📁 {sub_name}")
                print(f"      状态: {status}")
                print(f"      记录产品数: {recorded_count or 0}")
                print(f"      实际产品数: {actual_count}")
                if page_count:
                    print(f"      页面数: {page_count}")
                if last_crawled:
                    print(f"      最后采集: {last_crawled}")
                
                # 如果数量不一致，给出建议
                if recorded_count != actual_count:
                    print(f"      🔧 建议: 重新采集此子目录")
                
                print()
            
            # 输出最后一个主目录的汇总
            if current_main_id is not None:
                print(f"   📊 主目录汇总: 记录 {main_total_recorded} 个，实际 {main_total_actual} 个")
                if main_total_recorded != main_total_actual:
                    print(f"   ⚠️  主目录数量不一致！")
                print()
            
            # 总体统计
            print("=" * 80)
            print("📈 总体统计")
            print("=" * 80)
            print(f"主目录总数: {total_main}")
            print(f"子目录总数: {total_sub}")
            print(f"已完成采集: {completed_sub} ({completed_sub/total_sub*100:.1f}%)")
            print(f"数量不一致: {inconsistent_sub} ({inconsistent_sub/total_sub*100:.1f}%)")
            print(f"未开始采集: {not_started_sub} ({not_started_sub/total_sub*100:.1f}%)")
            
            # 需要处理的分类
            if inconsistent_sub > 0 or not_started_sub > 0:
                print("\n🔧 需要处理的分类:")
                
                cursor.execute('''
                    SELECT 
                        mc.category_name as main_name,
                        sc.sub_category_name as sub_name,
                        sc.id as sub_id,
                        sc.product_count as recorded_count,
                        COUNT(pu.id) as actual_count,
                        sc.last_crawled_at
                    FROM main_categories mc
                    LEFT JOIN sub_categories sc ON mc.id = sc.main_category_id
                    LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                    WHERE sc.id IS NOT NULL
                    GROUP BY mc.id, sc.id
                    HAVING (sc.product_count != COUNT(pu.id) OR sc.last_crawled_at IS NULL)
                    ORDER BY mc.id, sc.id
                ''')
                
                problem_categories = cursor.fetchall()
                
                for main_name, sub_name, sub_id, recorded_count, actual_count, last_crawled in problem_categories:
                    if last_crawled is None:
                        print(f"   ⏳ {main_name} > {sub_name} (ID: {sub_id}) - 未开始采集")
                    else:
                        print(f"   ⚠️  {main_name} > {sub_name} (ID: {sub_id}) - 记录:{recorded_count or 0}, 实际:{actual_count}")
                
                print(f"\n💡 使用方法:")
                print(f"   1. 在GUI中右键点击分类，选择'重新采集'")
                print(f"   2. 或使用命令: python -c \"from spider_core import NovolineSpider; s=NovolineSpider(); s.crawl_single_sub_category(子目录ID)\"")
            
            else:
                print("\n🎉 所有分类采集状态正常！")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    check_crawl_status()

if __name__ == "__main__":
    main()
