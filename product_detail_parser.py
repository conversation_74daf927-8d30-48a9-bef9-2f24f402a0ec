#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品详情页解析器 - 第三阶段
解析产品详情页面的完整信息
"""

import re
import json
from typing import Dict, List, Optional
from bs4 import BeautifulSoup
from html_parser import NovolineParser

class ProductDetailParser(NovolineParser):
    """产品详情页解析器"""
    
    def parse_product_detail(self, product_url: str) -> Optional[Dict]:
        """
        解析产品详情页面
        
        Args:
            product_url: 产品详情页URL
            
        Returns:
            产品详情信息字典
        """
        soup = self.fetch_page(product_url)
        if not soup:
            return None
        
        try:
            # 解析基本信息
            basic_info = self._parse_basic_info(soup)
            
            # 解析导航信息
            navigation = self._parse_navigation(soup)
            
            # 解析分类信息
            category_info = self._parse_category_info(soup)
            
            # 解析阶梯价格（不再解析基础价格信息）
            tier_prices = self._parse_tier_prices(soup)

            # 组合完整信息（移除price_info，使用横向阶梯价格结构）
            product_detail = {
                **basic_info,
                **navigation,
                **category_info,
                'tier_prices': tier_prices,
                'source_url': product_url
            }
            
            return product_detail
            
        except Exception as e:
            print(f"解析产品详情失败: {e}")
            return None
    
    def _parse_basic_info(self, soup: BeautifulSoup) -> Dict:
        """解析基本信息"""
        info = {}
        
        # 产品标题
        title_elem = soup.find('h1', class_='product_title')
        info['title'] = title_elem.get_text(strip=True) if title_elem else ""
        
        # SKU
        sku_elem = soup.find('span', class_='sku')
        info['sku'] = sku_elem.get_text(strip=True) if sku_elem else ""
        
        return info
    
    def _parse_navigation(self, soup: BeautifulSoup) -> Dict:
        """解析导航信息"""
        nav_info = {}
        
        # 查找面包屑导航
        breadcrumb = soup.find('nav', class_='woocommerce-breadcrumb')
        if breadcrumb:
            # 提取所有链接
            links = breadcrumb.find_all('a')
            nav_levels = []
            
            for link in links:
                nav_text = link.get_text(strip=True)
                if nav_text.lower() != 'home':  # 排除Home
                    nav_levels.append(nav_text)
            
            # 分级存储
            nav_info['nav_level1'] = nav_levels[0] if len(nav_levels) > 0 else ""
            nav_info['nav_level2'] = nav_levels[1] if len(nav_levels) > 1 else ""
            nav_info['nav_level3'] = nav_levels[2] if len(nav_levels) > 2 else ""
            
            # 完整路径
            nav_info['nav_full'] = " > ".join(nav_levels)
        
        return nav_info
    
    def _parse_category_info(self, soup: BeautifulSoup) -> Dict:
        """解析分类信息"""
        category_info = {}
        
        # 查找分类信息
        category_elem = soup.find('span', class_='posted_in')
        if category_elem:
            category_link = category_elem.find('a')
            if category_link:
                category_info['category_name'] = category_link.get_text(strip=True)
                category_info['category_url'] = category_link.get('href', '')
        
        return category_info
    
    def _parse_price_info(self, soup: BeautifulSoup) -> Dict:
        """解析基础价格信息"""
        price_info = {}
        
        # 查找价格容器
        price_container = soup.find('div', class_='tpt__tiered-pricing')
        if price_container:
            # 从data属性中提取价格信息
            data_settings = price_container.get('data-settings', '{}')
            try:
                settings = json.loads(data_settings)
                # 这里可以提取一些基础设置
            except:
                pass
            
            # 从data属性中提取基础价格
            price_blocks = price_container.find('div', class_='tiered-pricing-blocks')
            if price_blocks:
                regular_price = price_blocks.get('data-regular-price', '')
                sale_price = price_blocks.get('data-sale-price', '')
                
                # 注意：regular_price和sale_price字段已移除，不再使用
                # 价格信息现在通过阶梯价格系统处理
        
        return price_info
    
    def _parse_tier_prices(self, soup: BeautifulSoup) -> List[Dict]:
        """解析阶梯价格信息"""
        tier_prices = []
        
        # 查找所有价格层级块
        price_blocks = soup.find_all('div', class_='tiered-pricing-block')
        
        for block in price_blocks:
            try:
                tier_info = {}
                
                # 提取数量信息
                quantity_elem = block.find('div', class_='tiered-pricing-block__quantity')
                if quantity_elem:
                    quantity_text = quantity_elem.get_text(strip=True)
                    tier_info['quantity_display'] = quantity_text
                    
                    # 解析数量范围
                    quantity_min, quantity_max = self._parse_quantity_range(quantity_text)
                    tier_info['quantity_min'] = quantity_min
                    tier_info['quantity_max'] = quantity_max
                
                # 提取价格信息
                price_elem = block.find('div', class_='tiered-pricing-block__price')
                if price_elem:
                    # 单价
                    price_amount = price_elem.find('span', class_='woocommerce-Price-amount')
                    if price_amount:
                        price_text = price_amount.get_text(strip=True)
                        tier_info['unit_price'] = self._parse_price_value(price_text)
                    
                    # 折扣信息
                    discount_elem = price_elem.find('span', class_='tiered-pricing-block__price-discount')
                    if discount_elem:
                        discount_text = discount_elem.get_text(strip=True)
                        discount_percent = self._parse_discount_percent(discount_text)
                        tier_info['discount_percent'] = discount_percent
                
                # 从data属性提取精确数据
                tier_quantity = block.get('data-tiered-quantity')
                tier_price = block.get('data-tiered-price')
                
                if tier_quantity:
                    tier_info['quantity_min'] = int(tier_quantity)
                if tier_price:
                    tier_info['unit_price'] = float(tier_price)
                
                # 保存原始数据
                tier_info['raw_data'] = str(block)
                
                if tier_info:
                    tier_prices.append(tier_info)
                    
            except Exception as e:
                print(f"解析价格层级失败: {e}")
                continue
        
        return tier_prices
    
    def _parse_price_value(self, price_text: str) -> Optional[float]:
        """从价格文本中提取数值"""
        if not price_text:
            return None
        
        # 移除货币符号和其他字符，只保留数字和小数点
        price_clean = re.sub(r'[^\d.]', '', price_text)
        try:
            return float(price_clean)
        except:
            return None
    
    def _parse_quantity_range(self, quantity_text: str) -> tuple:
        """解析数量范围"""
        # 移除非数字字符，提取数字
        numbers = re.findall(r'\d+', quantity_text.replace(',', ''))
        
        if not numbers:
            return (1, 1)  # 默认单个
        
        quantity_min = int(numbers[0])
        
        # 检查是否有"+"号，表示无上限
        if '+' in quantity_text:
            quantity_max = None
        else:
            quantity_max = quantity_min
        
        return (quantity_min, quantity_max)
    
    def _parse_discount_percent(self, discount_text: str) -> Optional[int]:
        """解析折扣百分比"""
        # 提取百分比数字
        match = re.search(r'(\d+)%', discount_text)
        if match:
            return int(match.group(1))
        return None
    
    def generate_price_data_raw(self, tier_prices: List[Dict]) -> str:
        """生成原始价格数据字符串"""
        # 使用|分隔不同层级，#分隔同一层级的不同字段
        raw_parts = []
        
        for tier in tier_prices:
            parts = [
                str(tier.get('quantity_min', '')),
                str(tier.get('quantity_max', '') if tier.get('quantity_max') else ''),
                str(tier.get('unit_price', '')),
                str(tier.get('discount_percent', '') if tier.get('discount_percent') else ''),
                tier.get('quantity_display', '')
            ]
            raw_parts.append('#'.join(parts))
        
        return '|'.join(raw_parts)

def test_parser():
    """测试解析器"""
    parser = ProductDetailParser()
    
    # 测试URL
    test_url = "https://novolinepromo.com/fitness-shaking-cup-portable-15oz-sports-water-bottle/"
    
    print(f"🧪 测试产品详情解析器")
    print(f"URL: {test_url}")
    
    result = parser.parse_product_detail(test_url)
    
    if result:
        print(f"\n✅ 解析成功:")
        print(f"标题: {result.get('title', 'N/A')}")
        print(f"SKU: {result.get('sku', 'N/A')}")
        print(f"导航: {result.get('nav_full', 'N/A')}")
        print(f"分类: {result.get('category_name', 'N/A')}")
        print(f"起始价格: ${result.get('tier_prices', [{}])[0].get('unit_price', 'N/A') if result.get('tier_prices') else 'N/A'}")
        print(f"阶梯价格层级: {len(result.get('tier_prices', []))}")
        
        print(f"\n📊 阶梯价格详情:")
        for i, tier in enumerate(result.get('tier_prices', []), 1):
            print(f"  {i}. {tier.get('quantity_display', 'N/A')} - "
                  f"${tier.get('unit_price', 'N/A')} "
                  f"({tier.get('discount_percent', 0)}% off)")
        
        # 生成原始数据
        raw_data = parser.generate_price_data_raw(result.get('tier_prices', []))
        print(f"\n📝 原始价格数据: {raw_data}")
        
    else:
        print(f"❌ 解析失败")

if __name__ == "__main__":
    test_parser()
