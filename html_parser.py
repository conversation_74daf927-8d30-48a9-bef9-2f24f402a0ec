#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML解析模块
用于解析网站HTML内容，提取目录结构和产品信息
"""

import re
import requests
from bs4 import BeautifulSoup
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, urlparse
import time
import random

class NovolineParser:
    """Novoline网站解析器"""
    
    def __init__(self, base_url: str = "https://novolinepromo.com"):
        """
        初始化解析器
        
        Args:
            base_url: 网站基础URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def parse_categories_from_file(self, file_path: str) -> List[Dict]:
        """
        从本地HTML文件解析目录结构
        
        Args:
            file_path: HTML文件路径
            
        Returns:
            目录结构列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            categories = []
            
            # 查找所有表格
            tables = soup.find_all('table')
            
            for table in tables:
                # 查找主目录行（查找包含背景色的td元素的行）
                main_category_row = None
                for tr in table.find_all('tr'):
                    # 检查tr中的td元素是否有背景色
                    td = tr.find('td')
                    if td:
                        td_style = td.get('style', '')
                        if 'background-color: #d85d4a' in td_style or '#d85d4a' in td_style:
                            main_category_row = tr
                            break

                if main_category_row:
                    # 提取主目录信息
                    main_link = None
                    for a in main_category_row.find_all('a'):
                        style = a.get('style', '')
                        if 'color: white' in style or 'color:white' in style:
                            main_link = a
                            break

                    if main_link:
                        main_category_name = main_link.get_text(strip=True)
                        # 清理名称，移除特殊字符
                        main_category_name = main_category_name.replace('≡ ', '').replace('≡', '').strip()
                        main_category_url = main_link.get('href', '')

                        # 提取图标URL
                        icon_img = main_category_row.find('img')
                        icon_url = ''
                        if icon_img:
                            icon_url = icon_img.get('src', '') or icon_img.get('data-src', '')
                            if icon_url and not icon_url.startswith('http'):
                                icon_url = urljoin(self.base_url, icon_url)

                        # 查找子目录
                        sub_categories = []
                        all_rows = table.find_all('tr')

                        # 找到主目录行的索引
                        main_row_index = -1
                        for i, tr in enumerate(all_rows):
                            if tr == main_category_row:
                                main_row_index = i
                                break

                        # 处理主目录行之后的行
                        if main_row_index >= 0:
                            for sub_row in all_rows[main_row_index + 1:]:
                                sub_link = sub_row.find('a')
                                if sub_link:
                                    sub_text = sub_link.get_text(strip=True)
                                    if sub_text.startswith('>') or sub_text.startswith('&gt;'):
                                        sub_name = sub_text.replace('> ', '').replace('&gt; ', '').replace('>', '').replace('&gt;', '').strip()
                                        sub_url = sub_link.get('href', '')

                                        if sub_name and sub_url:
                                            sub_categories.append({
                                                'name': sub_name,
                                                'url': sub_url
                                            })

                        if main_category_name and main_category_url:
                            categories.append({
                                'main_category': {
                                    'name': main_category_name,
                                    'url': main_category_url,
                                    'icon_url': icon_url
                                },
                                'sub_categories': sub_categories
                            })
            
            return categories
            
        except Exception as e:
            print(f"解析文件时出错: {e}")
            return []
    
    def fetch_page(self, url: str, max_retries: int = 3) -> Optional[BeautifulSoup]:
        """
        获取网页内容
        
        Args:
            url: 目标URL
            max_retries: 最大重试次数
            
        Returns:
            BeautifulSoup对象或None
        """
        for attempt in range(max_retries):
            try:
                # 随机延迟，避免被反爬虫
                time.sleep(random.uniform(1, 3))
                
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                
                return BeautifulSoup(response.content, 'html.parser')
                
            except requests.RequestException as e:
                print(f"获取页面失败 (尝试 {attempt + 1}/{max_retries}): {url} - {e}")
                if attempt == max_retries - 1:
                    return None
                time.sleep(random.uniform(2, 5))
        
        return None
    
    def parse_product_list(self, category_url: str) -> Dict:
        """
        解析产品列表页面，支持分页和产品数量统计

        Args:
            category_url: 分类页面URL

        Returns:
            包含产品列表和统计信息的字典
        """
        all_products = []
        page_count = 0
        current_url = category_url

        while current_url and page_count < 100:  # 防止无限循环，最多100页
            page_count += 1
            print(f"正在解析第 {page_count} 页: {current_url}")

            soup = self.fetch_page(current_url)
            if not soup:
                break

            # 解析当前页面的产品
            page_products = self._parse_single_page_products(soup)
            all_products.extend(page_products)

            # 查找下一页
            next_url = self._find_next_page_url(soup, current_url)
            if next_url == current_url:  # 防止死循环
                break
            current_url = next_url

            # 添加延迟避免请求过快
            time.sleep(1)

        return {
            'products': all_products,
            'total_count': len(all_products),
            'page_count': page_count,
            'category_url': category_url
        }

    def _parse_single_page_products(self, soup: BeautifulSoup) -> List[Dict]:
        """
        解析单个页面的产品信息

        Args:
            soup: BeautifulSoup对象

        Returns:
            产品信息列表
        """
        products = []

        # 根据2.txt中的结构，查找产品列表容器
        product_list = soup.find('ul', class_='products')
        if not product_list:
            return products

        # 查找所有产品项
        product_items = product_list.find_all('li', class_=lambda x: x and 'product' in x)

        for item in product_items:
            try:
                product_info = self._extract_product_info(item)
                if product_info:
                    products.append(product_info)
            except Exception as e:
                print(f"解析产品信息时出错: {e}")
                continue

        return products

    def _extract_product_info(self, item) -> Optional[Dict]:
        """
        从产品项中提取产品基本信息（第二阶段：只收集URL和名称）

        Args:
            item: 产品项的BeautifulSoup元素

        Returns:
            产品基本信息字典或None
        """
        try:
            # 提取产品URL和名称
            product_link = item.find('a', class_='woocommerce-LoopProduct-link')
            if not product_link:
                product_link = item.find('a', href=True)

            if not product_link:
                return None

            product_url = product_link.get('href', '')
            if not product_url:
                return None

            # 提取产品名称
            title_elem = item.find('h2', class_='woocommerce-loop-product__title')
            if not title_elem:
                title_elem = item.find('a', class_='ast-loop-product__link')
                if title_elem:
                    title_elem = title_elem.find('h2')

            product_name = title_elem.get_text(strip=True) if title_elem else ""

            # 第二阶段只收集基本信息，不访问产品详情页
            if product_name and product_url:
                return {
                    'name': product_name,
                    'url': product_url
                    # SKU、价格、图片等详细信息在第三阶段收集
                }

        except Exception as e:
            print(f"提取产品基本信息时出错: {e}")
            return None

        return None
    
    def _find_next_page_url(self, soup: BeautifulSoup, current_url: str) -> Optional[str]:
        """
        查找下一页链接，基于2.txt中的分页结构

        Args:
            soup: BeautifulSoup对象
            current_url: 当前页面URL

        Returns:
            下一页URL或None
        """
        # 根据2.txt中的分页结构查找
        pagination_nav = soup.find('nav', class_='woocommerce-pagination')
        if not pagination_nav:
            return None

        # 查找下一页链接
        next_link = pagination_nav.find('a', class_='next')
        if next_link and next_link.get('href'):
            next_url = next_link.get('href')
            # 确保URL是完整的
            if not next_url.startswith('http'):
                next_url = urljoin(self.base_url, next_url)
            return next_url

        # 备用方法：查找页码链接
        page_links = pagination_nav.find_all('a', class_='page-numbers')
        current_page_elem = pagination_nav.find('span', class_='current')

        if current_page_elem and page_links:
            try:
                current_page = int(current_page_elem.get_text(strip=True))
                for link in page_links:
                    try:
                        page_num = int(link.get_text(strip=True))
                        if page_num == current_page + 1:
                            next_url = link.get('href')
                            if not next_url.startswith('http'):
                                next_url = urljoin(self.base_url, next_url)
                            return next_url
                    except ValueError:
                        continue
            except ValueError:
                pass

        return None

    def find_next_page(self, soup: BeautifulSoup) -> Optional[str]:
        """
        查找下一页链接（保持向后兼容）

        Args:
            soup: BeautifulSoup对象

        Returns:
            下一页URL或None
        """
        return self._find_next_page_url(soup, "")
    
    def parse_product_details(self, product_url: str) -> Dict:
        """
        解析产品详情页面，提取阶梯价格信息
        
        Args:
            product_url: 产品详情页URL
            
        Returns:
            产品详情信息，包含阶梯价格
        """
        soup = self.fetch_page(product_url)
        if not soup:
            return {}
        
        product_info = {
            'url': product_url,
            'tier_prices': []
        }
        
        # 查找价格表格或价格信息
        price_containers = soup.find_all(['table', 'div'], class_=lambda x: x and any(
            keyword in x.lower() for keyword in ['price', 'tier', 'quantity', 'bulk']
        ))
        
        for container in price_containers:
            tier_prices = self.extract_tier_prices(container)
            if tier_prices:
                product_info['tier_prices'].extend(tier_prices)
        
        # 如果没有找到价格表格，尝试查找其他价格信息
        if not product_info['tier_prices']:
            # 查找单个价格
            price_elem = soup.find(['span', 'div'], class_=lambda x: x and 'price' in x.lower())
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                price_match = re.search(r'\$?([\d,]+\.?\d*)', price_text)
                if price_match:
                    price = float(price_match.group(1).replace(',', ''))
                    product_info['tier_prices'].append({
                        'quantity_min': 1,
                        'quantity_max': None,
                        'price': price,
                        'currency': 'USD'
                    })
        
        return product_info
    
    def extract_tier_prices(self, container) -> List[Dict]:
        """
        从容器中提取阶梯价格信息
        
        Args:
            container: BeautifulSoup元素
            
        Returns:
            阶梯价格列表
        """
        tier_prices = []
        
        # 如果是表格
        if container.name == 'table':
            rows = container.find_all('tr')
            for row in rows[1:]:  # 跳过表头
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    try:
                        # 提取数量范围
                        qty_text = cells[0].get_text(strip=True)
                        qty_match = re.search(r'(\d+)(?:\s*-\s*(\d+))?', qty_text)
                        
                        if qty_match:
                            qty_min = int(qty_match.group(1))
                            qty_max = int(qty_match.group(2)) if qty_match.group(2) else None
                            
                            # 提取价格
                            price_text = cells[1].get_text(strip=True)
                            price_match = re.search(r'\$?([\d,]+\.?\d*)', price_text)
                            
                            if price_match:
                                price = float(price_match.group(1).replace(',', ''))
                                tier_prices.append({
                                    'quantity_min': qty_min,
                                    'quantity_max': qty_max,
                                    'price': price,
                                    'currency': 'USD'
                                })
                    except (ValueError, AttributeError):
                        continue
        
        # 如果是div容器，查找价格相关的文本
        else:
            text_content = container.get_text()
            # 使用正则表达式查找价格模式
            price_patterns = [
                r'(\d+)(?:\s*-\s*(\d+))?\s*(?:pcs?|pieces?)?\s*[:\-]\s*\$?([\d,]+\.?\d*)',
                r'\$?([\d,]+\.?\d*)\s*(?:for|@)\s*(\d+)(?:\s*-\s*(\d+))?'
            ]
            
            for pattern in price_patterns:
                matches = re.finditer(pattern, text_content, re.IGNORECASE)
                for match in matches:
                    try:
                        if len(match.groups()) == 3:  # 第一种模式
                            qty_min = int(match.group(1))
                            qty_max = int(match.group(2)) if match.group(2) else None
                            price = float(match.group(3).replace(',', ''))
                        else:  # 第二种模式
                            price = float(match.group(1).replace(',', ''))
                            qty_min = int(match.group(2))
                            qty_max = int(match.group(3)) if match.group(3) else None
                        
                        tier_prices.append({
                            'quantity_min': qty_min,
                            'quantity_max': qty_max,
                            'price': price,
                            'currency': 'USD'
                        })
                    except (ValueError, AttributeError):
                        continue
        
        return tier_prices
    
    def validate_url(self, url: str) -> bool:
        """
        验证URL是否有效
        
        Args:
            url: 要验证的URL
            
        Returns:
            是否有效
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
