#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单独重新采集功能演示
演示如何针对特定分类进行重新采集
"""

import os
import time
import sqlite3
from spider_core import NovolineSpider
from database import DatabaseManager

def demo_single_recrawl():
    """演示单独重新采集功能"""
    print("=" * 80)
    print("单独重新采集功能演示")
    print("=" * 80)
    
    # 检查数据库
    if not os.path.exists("demo_stage1.db"):
        print("❌ 未找到演示数据库，请先运行第一阶段")
        return
    
    spider = NovolineSpider()
    spider.db.db_path = "demo_stage1.db"
    
    # 设置回调
    def on_progress(stage, message):
        print(f"[{stage}] {message}")
    
    def on_error(message, stack_trace=None):
        print(f"❌ 错误: {message}")
    
    def on_completion(message):
        print(f"✅ 完成: {message}")
    
    spider.set_callbacks(on_progress, on_error, on_completion)
    
    # 获取一些子目录用于演示
    try:
        with sqlite3.connect(spider.db.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT 
                    sc.id, 
                    mc.category_name, 
                    sc.sub_category_name,
                    sc.product_count,
                    COUNT(pu.id) as actual_count
                FROM sub_categories sc
                JOIN main_categories mc ON sc.main_category_id = mc.id
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY sc.id
                LIMIT 5
            ''')
            
            sub_categories = cursor.fetchall()
            
            if not sub_categories:
                print("❌ 未找到子目录数据")
                return
            
            print("📋 可用的子目录:")
            for i, (sub_id, main_name, sub_name, recorded_count, actual_count) in enumerate(sub_categories, 1):
                status = "✅" if recorded_count == actual_count and recorded_count > 0 else "⚠️"
                print(f"   {i}. {status} {main_name} > {sub_name}")
                print(f"      ID: {sub_id}, 记录: {recorded_count or 0}, 实际: {actual_count}")
            
            print("\n🎯 演示功能:")
            print("1. 单独重新采集子目录")
            print("2. 查看采集状态")
            print("3. 退出")
            
            while True:
                choice = input("\n请选择功能 (1-3): ").strip()
                
                if choice == "1":
                    # 单独重新采集
                    try:
                        index = int(input(f"请选择要重新采集的子目录 (1-{len(sub_categories)}): ")) - 1
                        if 0 <= index < len(sub_categories):
                            sub_id, main_name, sub_name, _, _ = sub_categories[index]
                            
                            print(f"\n🚀 开始重新采集: {main_name} > {sub_name}")
                            print("   (这将删除现有数据并重新采集)")
                            
                            confirm = input("确认继续? (y/N): ").strip().lower()
                            if confirm == 'y':
                                spider.crawl_single_sub_category(sub_id, reset_first=True)
                                
                                # 等待完成
                                while spider.is_running:
                                    time.sleep(1)
                                
                                print("\n📊 重新采集完成，查看结果:")
                                show_category_status(spider.db, sub_id)
                            else:
                                print("❌ 已取消")
                        else:
                            print("❌ 无效选择")
                    except ValueError:
                        print("❌ 请输入有效数字")
                
                elif choice == "2":
                    # 查看状态
                    print("\n📊 当前采集状态:")
                    for sub_id, main_name, sub_name, recorded_count, actual_count in sub_categories:
                        show_category_status(spider.db, sub_id)
                
                elif choice == "3":
                    print("👋 演示结束")
                    break
                
                else:
                    print("❌ 无效选择，请输入 1-3")
    
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

def show_category_status(db, sub_category_id):
    """显示子目录状态"""
    try:
        with sqlite3.connect(db.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT 
                    mc.category_name,
                    sc.sub_category_name,
                    sc.product_count,
                    sc.page_count,
                    sc.last_crawled_at,
                    COUNT(pu.id) as actual_count
                FROM sub_categories sc
                JOIN main_categories mc ON sc.main_category_id = mc.id
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                WHERE sc.id = ?
                GROUP BY sc.id
            ''', (sub_category_id,))
            
            result = cursor.fetchone()
            if result:
                main_name, sub_name, recorded_count, page_count, last_crawled, actual_count = result
                
                status = "✅ 完成" if recorded_count == actual_count and recorded_count > 0 else "⚠️ 不一致"
                
                print(f"   📁 {main_name} > {sub_name}")
                print(f"      状态: {status}")
                print(f"      记录产品数: {recorded_count or 0}")
                print(f"      实际产品数: {actual_count}")
                print(f"      页面数: {page_count or 0}")
                print(f"      最后采集: {last_crawled or '未采集'}")
                
                if recorded_count != actual_count:
                    print(f"      🔧 建议: 需要重新采集")
                print()
    
    except Exception as e:
        print(f"❌ 获取状态失败: {e}")

def demo_main_category_recrawl():
    """演示主目录重新采集"""
    print("\n" + "=" * 80)
    print("主目录重新采集演示")
    print("=" * 80)
    
    spider = NovolineSpider()
    spider.db.db_path = "demo_stage1.db"
    
    # 设置回调
    def on_progress(stage, message):
        print(f"[{stage}] {message}")
    
    def on_error(message, stack_trace=None):
        print(f"❌ 错误: {message}")
    
    def on_completion(message):
        print(f"✅ 完成: {message}")
    
    spider.set_callbacks(on_progress, on_error, on_completion)
    
    try:
        with sqlite3.connect(spider.db.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT 
                    mc.id,
                    mc.category_name,
                    COUNT(sc.id) as sub_count,
                    SUM(sc.product_count) as total_recorded,
                    COUNT(pu.id) as total_actual
                FROM main_categories mc
                LEFT JOIN sub_categories sc ON mc.id = sc.main_category_id
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY mc.id
                LIMIT 3
            ''')
            
            main_categories = cursor.fetchall()
            
            print("📋 可用的主目录:")
            for i, (main_id, main_name, sub_count, total_recorded, total_actual) in enumerate(main_categories, 1):
                status = "✅" if total_recorded == total_actual and total_recorded > 0 else "⚠️"
                print(f"   {i}. {status} {main_name}")
                print(f"      ID: {main_id}, 子目录: {sub_count}, 记录: {total_recorded or 0}, 实际: {total_actual}")
            
            choice = input(f"\n选择要重新采集的主目录 (1-{len(main_categories)}, 或按回车跳过): ").strip()
            
            if choice and choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(main_categories):
                    main_id, main_name, _, _, _ = main_categories[index]
                    
                    print(f"\n🚀 开始重新采集主目录: {main_name}")
                    print("   (这将删除该主目录下所有子目录的产品数据)")
                    
                    confirm = input("确认继续? (y/N): ").strip().lower()
                    if confirm == 'y':
                        spider.crawl_single_main_category(main_id, reset_first=True)
                        
                        # 等待完成
                        while spider.is_running:
                            time.sleep(2)
                            print("   采集进行中...")
                        
                        print("✅ 主目录重新采集完成")
                    else:
                        print("❌ 已取消")
    
    except Exception as e:
        print(f"❌ 主目录演示失败: {e}")

def main():
    """主函数"""
    print("🎯 Novoline 单独重新采集功能演示")
    print("\n功能说明:")
    print("1. 支持单独重新采集特定子目录")
    print("2. 支持重新采集整个主目录")
    print("3. 自动检测数量不一致的分类")
    print("4. 提供断点续采功能")
    
    demo_single_recrawl()
    
    # 询问是否演示主目录重新采集
    if input("\n是否演示主目录重新采集? (y/N): ").strip().lower() == 'y':
        demo_main_category_recrawl()
    
    print("\n💡 在GUI中使用:")
    print("   1. 右键点击分类表格中的任意分类")
    print("   2. 选择'重新采集此子目录'或'重新采集此主目录'")
    print("   3. 确认后自动开始重新采集")
    
    print("\n📋 状态检查:")
    print("   运行 python check_crawl_status.py 查看详细采集状态")

if __name__ == "__main__":
    main()
