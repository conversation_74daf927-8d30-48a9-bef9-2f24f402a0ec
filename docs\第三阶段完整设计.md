# 第三阶段完整设计 - 产品详情采集系统

## 🎯 需求分析

根据您的要求，第三阶段需要实现：

### 核心功能
1. **完整导航信息**: 分级存储面包屑导航，除Home外的各级导航
2. **产品基本信息**: 标题、SKU、分类信息
3. **阶梯价格系统**: 不同数量对应的价格和折扣，独立存储
4. **原始数据保存**: 用分隔符保存完整的原始价格信息
5. **状态管理系统**: 成功/失败状态，支持重试和批量处理
6. **过滤和管理**: 可过滤成功/失败产品，支持批量重新采集

### 示例数据结构
基于您提供的产品页面：
```
URL: https://novolinepromo.com/fitness-shaking-cup-portable-15oz-sports-water-bottle/

导航: Home > Drinkware & Can Coolers > Shaker & Blender Bottles > 产品名
标题: Fitness Shaking Cup Portable 15oz Sports Water Bottle
SKU: novo6428C
分类: Shaker & Blender Bottles

阶梯价格:
1 piece: $4.29 (0% off)
200 pieces: $3.30 (23% off)
500 pieces: $3.26 (24% off)
1,000 pieces: $3.22 (24% off)
3,000 pieces: $3.18 (25% off)
5,000+ pieces: $3.13 (27% off)
```

## 🗄️ 数据库重新设计

### 1. 产品详情表 (product_details)
```sql
CREATE TABLE product_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_url_id INTEGER NOT NULL UNIQUE,
    
    -- 基本信息
    title TEXT,
    sku TEXT,
    
    -- 导航信息 (分级存储)
    nav_level1 TEXT,  -- Drinkware & Can Coolers
    nav_level2 TEXT,  -- Shaker & Blender Bottles
    nav_level3 TEXT,  -- 三级导航 (如果有)
    nav_full TEXT,    -- 完整导航路径
    
    -- 分类信息
    category_name TEXT,
    category_url TEXT,
    
    -- 基础价格
    regular_price DECIMAL(10,2),
    sale_price DECIMAL(10,2),
    currency TEXT DEFAULT 'USD',
    
    -- 原始价格数据 (完整保存)
    price_data_raw TEXT,  -- 格式: "1##4.29#0#1 piece|200##3.30#23#200 pieces|..."
    
    -- 采集状态
    crawl_status TEXT DEFAULT 'pending',  -- pending, success, failed, retrying
    crawl_attempts INTEGER DEFAULT 0,
    last_crawl_attempt TIMESTAMP,
    error_message TEXT,
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 阶梯价格表 (tier_prices)
```sql
CREATE TABLE tier_prices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_detail_id INTEGER NOT NULL,
    
    -- 数量层级
    quantity_min INTEGER NOT NULL,      -- 200
    quantity_max INTEGER,               -- NULL (表示5000+无上限)
    quantity_display TEXT,              -- "200 pieces"
    
    -- 价格信息
    unit_price DECIMAL(10,2) NOT NULL,  -- 3.30
    currency TEXT DEFAULT 'USD',
    
    -- 折扣信息
    discount_percent INTEGER,           -- 23
    
    -- 原始数据
    raw_data TEXT,                      -- 原始HTML片段
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 原始数据格式
使用 `|` 分隔不同层级，`#` 分隔同一层级的字段：
```
格式: quantity_min#quantity_max#unit_price#discount_percent#quantity_display
示例: 1##4.29#0#1 piece|200##3.30#23#200 pieces|500##3.26#24#500 pieces
```

## 🧩 核心组件设计

### 1. 产品详情解析器 (ProductDetailParser)

#### 主要功能
```python
class ProductDetailParser(NovolineParser):
    def parse_product_detail(self, product_url: str) -> Dict:
        """解析产品详情页面完整信息"""
        
    def _parse_navigation(self, soup: BeautifulSoup) -> Dict:
        """解析面包屑导航，分级存储"""
        
    def _parse_tier_prices(self, soup: BeautifulSoup) -> List[Dict]:
        """解析阶梯价格信息"""
        
    def generate_price_data_raw(self, tier_prices: List[Dict]) -> str:
        """生成原始价格数据字符串"""
```

#### 解析逻辑
1. **导航解析**: 从面包屑中提取各级导航，排除Home
2. **基本信息**: 提取标题、SKU、分类
3. **价格解析**: 从tiered-pricing结构中提取所有价格层级
4. **数据整合**: 组合成完整的产品详情数据

### 2. 第三阶段爬虫 (Stage3Crawler)

#### 状态管理
```python
class Stage3Crawler:
    def start_stage3(self, filter_status: str = None):
        """启动第三阶段，支持状态过滤"""
        
    def _get_products_to_crawl(self, filter_status: str) -> List:
        """获取需要采集的产品列表"""
        # filter_status: 'pending', 'failed', None(全部)
        
    def _handle_crawl_failure(self, product_url_id: int, error_msg: str) -> bool:
        """处理采集失败，支持自动重试"""
```

#### 重试机制
- **自动重试**: 失败后自动重试1次
- **重试延迟**: 5秒延迟避免频繁请求
- **状态跟踪**: 记录重试次数和错误信息
- **批量重试**: 支持批量重新处理失败的产品

### 3. 增强GUI界面 (EnhancedSpiderGUI)

#### 第三阶段Tab功能
```python
# 控制功能
- 开始/暂停/继续/停止第三阶段
- 过滤选项: 全部/仅未采集/仅失败的
- 批量操作: 重试失败的/清除失败状态

# 状态显示
- 实时统计: 总计/成功/失败/进行中/待采集
- 产品列表: 显示每个产品的采集状态
- 右键菜单: 重新采集/查看详情/标记失败/重置状态
```

#### 状态图标
- ⏳ 待采集 (pending)
- 🔄 采集中 (retrying)
- ✅ 成功 (success)
- ❌ 失败 (failed)

## 🔄 工作流程

### 1. 数据迁移流程
```
1. 创建新数据库结构 (novoline_spider_v2.db)
2. 从旧数据库迁移第一、二阶段数据
3. 保持产品URL表结构，为第三阶段做准备
```

### 2. 第三阶段采集流程
```
1. 获取待采集产品列表 (支持状态过滤)
2. 逐个访问产品详情页
3. 解析完整产品信息 (导航、SKU、价格等)
4. 保存到product_details和tier_prices表
5. 更新采集状态 (success/failed)
6. 失败时自动重试，记录错误信息
```

### 3. 状态管理流程
```
pending → retrying → success/failed
                  ↓
                 重试 (最多1次)
                  ↓
                success/failed (最终状态)
```

## 🎯 核心特性

### 1. 完整的导航信息
- **分级存储**: nav_level1, nav_level2, nav_level3
- **完整路径**: nav_full 存储完整导航
- **灵活查询**: 可按任意级别导航查询产品

### 2. 详细的价格系统
- **阶梯价格**: 独立表存储每个价格层级
- **原始数据**: 完整保存原始价格信息
- **折扣计算**: 自动计算折扣百分比
- **数量范围**: 支持开放式数量 (如5000+)

### 3. 强大的状态管理
- **状态跟踪**: 详细记录每个产品的采集状态
- **重试机制**: 自动重试失败的产品
- **错误记录**: 详细记录错误信息和堆栈
- **批量操作**: 支持批量重新处理

### 4. 灵活的过滤系统
- **状态过滤**: 可过滤特定状态的产品
- **批量处理**: 支持批量重新采集失败的产品
- **手动控制**: 支持手动标记和重置状态

## 🧪 测试验证

### 测试脚本
```bash
python test/test_stage3_complete.py
```

### 测试内容
1. **数据库迁移**: 验证数据迁移的完整性
2. **解析器测试**: 测试产品详情解析的准确性
3. **爬虫测试**: 测试第三阶段采集流程
4. **GUI测试**: 测试增强界面的功能

### 预期结果
```
✅ 成功解析产品详情
   标题: Fitness Shaking Cup Portable 15oz Sports Water Bottle
   SKU: novo6428C
   导航: Drinkware & Can Coolers > Shaker & Blender Bottles
   分类: Shaker & Blender Bottles
   基础价格: $4.29
   阶梯价格层级: 6

📊 阶梯价格详情:
   1. 1 piece - $4.29 (0% off)
   2. 200 pieces - $3.30 (23% off)
   3. 500 pieces - $3.26 (24% off)
   ...
```

## 🎉 完整解决方案

### 解决的问题
1. ✅ **完整导航信息**: 分级存储，便于管理和查询
2. ✅ **详细产品信息**: 标题、SKU、分类完整采集
3. ✅ **阶梯价格系统**: 独立存储，支持复杂价格结构
4. ✅ **原始数据保存**: 分隔符格式，完整保存原始信息
5. ✅ **状态管理系统**: 成功/失败状态，支持重试和批量处理
6. ✅ **过滤和管理**: 灵活的过滤和批量操作功能

### 技术优势
- **数据完整性**: 完整保存所有产品信息
- **状态可控性**: 精确控制每个产品的采集状态
- **错误处理**: 完善的错误处理和重试机制
- **用户友好**: 直观的GUI界面和操作流程
- **扩展性**: 灵活的数据结构，易于扩展

现在您拥有了一个完整的第三阶段产品详情采集系统，支持您提出的所有需求！🎉
