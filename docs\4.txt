<nav class="woocommerce-breadcrumb" aria-label="Breadcrumb"><a href="https://novolinepromo.com">Home</a>&nbsp;/&nbsp;<a href="https://novolinepromo.com/drinkware-can-cooler/">Drinkware &amp; Can Coolers</a>&nbsp;/&nbsp;<a href="https://novolinepromo.com/drinkware-can-cooler/shaker-blender-bottles/">Shaker &amp; Blender Bottles</a>&nbsp;/&nbsp;Fitness Shaking Cup Portable 15oz Sports Water Bottle</nav>

<h1 class="product_title entry-title">Fitness Shaking Cup Portable 15oz Sports Water Bottle</h1>

<div class="product_meta">

	
	
		<span class="sku_wrapper">SKU: <span class="sku">novo6428C</span></span>

	
	<span class="posted_in">Category: <a href="https://novolinepromo.com/drinkware-can-cooler/shaker-blender-bottles/" rel="tag">Shaker &amp; Blender Bottles</a></span>
	
	
</div>


<div class="tpt__tiered-pricing " data-settings="{&quot;display_context&quot;:&quot;product-page&quot;,&quot;display&quot;:true,&quot;display_type&quot;:&quot;blocks&quot;,&quot;title&quot;:&quot;&quot;,&quot;table_class&quot;:&quot;&quot;,&quot;quantity_column_title&quot;:&quot;Quantity&quot;,&quot;price_column_title&quot;:&quot;Price&quot;,&quot;discount_column_title&quot;:&quot;Discount (%)&quot;,&quot;quantity_type&quot;:&quot;static&quot;,&quot;show_discount_column&quot;:true,&quot;clickable_rows&quot;:true,&quot;active_tier_color&quot;:&quot;#96598a&quot;,&quot;tooltip_border&quot;:true,&quot;blocks_style&quot;:&quot;style-2&quot;,&quot;options_show_total&quot;:true,&quot;options_show_original_product_price&quot;:true,&quot;options_show_default_option&quot;:true,&quot;options_default_option_text&quot;:&quot;<strong>Buy {tp_quantity} pieces<\/strong>&quot;,&quot;options_option_text&quot;:&quot;<strong>Buy {tp_quantity} pieces and save {tp_rounded_discount}%<\/strong>&quot;,&quot;plain_text_show_default_option&quot;:true,&quot;plain_text_option_text&quot;:&quot;<strong>Buy {tp_quantity} pieces for {tp_price} each and save {tp_rounded_discount}%<\/strong>&quot;,&quot;plain_text_default_option_text&quot;:&quot;<strong>Buy {tp_quantity} pieces for {tp_price} each<\/strong>&quot;,&quot;update_price_on_product_page&quot;:true,&quot;show_tiered_price_as_discount&quot;:true,&quot;show_total_price&quot;:false,&quot;quantity_measurement_singular&quot;:&quot;piece&quot;,&quot;quantity_measurement_plural&quot;:&quot;pieces&quot;,&quot;product_id&quot;:null}" data-display-context="product-page" data-display-type="blocks" data-product-id="51026" data-product-type="simple">
			
			
    <div class="tiered-pricing-wrapper">
		
        <div class="tiered-pricing-blocks tiered-pricing-blocks--styled tiered-pricing-blocks--style-2" id="hrtqihqwmlzgeawta" data-product-id="51026" data-price-rules="{&quot;200&quot;:&quot;3.3&quot;,&quot;500&quot;:&quot;3.26&quot;,&quot;1000&quot;:&quot;3.22&quot;,&quot;3000&quot;:&quot;3.18&quot;,&quot;5000&quot;:&quot;3.13&quot;}" data-minimum="1" data-product-name="Fitness Shaking Cup Portable 15oz Sports Water Bottle" data-regular-price="4.29" data-sale-price="" data-price="4.29" data-product-price-suffix="">

            <div class="tiered-pricing-block tiered-pricing--active" data-tiered-quantity="1" data-tiered-price="
				4.29				" data-tiered-price-exclude-taxes="
				4.29				" data-tiered-price-include-taxes="
				4.29				 ">

                <div class="tiered-pricing-block__quantity">
					                        <span>1</span>
													piece											                </div>

                <div class="tiered-pricing-block__price">
					<span class="woocommerce-Price-amount amount"><span class="woocommerce-Price-currencySymbol">$</span>4.29</span>					
																						                </div>
            </div>
			
						
							
                <div class="tiered-pricing-block" data-tiered-quantity="200" data-tiered-price="3.3" data-tiered-price-exclude-taxes="3.3" data-tiered-price-include-taxes="3.3">

                    <div class="tiered-pricing-block__quantity">200 pieces</div>
                    <div class="tiered-pricing-block__price">
						<span>
							<span class="woocommerce-Price-amount amount"><span class="woocommerce-Price-currencySymbol">$</span>3.30</span>						</span>
						
						                            <span class="tiered-pricing-block__price-discount">
								(23% off)							</span>
						                    </div>
                </div>
							
                <div class="tiered-pricing-block" data-tiered-quantity="500" data-tiered-price="3.26" data-tiered-price-exclude-taxes="3.26" data-tiered-price-include-taxes="3.26">

                    <div class="tiered-pricing-block__quantity">500 pieces</div>
                    <div class="tiered-pricing-block__price">
						<span>
							<span class="woocommerce-Price-amount amount"><span class="woocommerce-Price-currencySymbol">$</span>3.26</span>						</span>
						
						                            <span class="tiered-pricing-block__price-discount">
								(24% off)							</span>
						                    </div>
                </div>
							
                <div class="tiered-pricing-block" data-tiered-quantity="1000" data-tiered-price="3.22" data-tiered-price-exclude-taxes="3.22" data-tiered-price-include-taxes="3.22">

                    <div class="tiered-pricing-block__quantity">1,000 pieces</div>
                    <div class="tiered-pricing-block__price">
						<span>
							<span class="woocommerce-Price-amount amount"><span class="woocommerce-Price-currencySymbol">$</span>3.22</span>						</span>
						
						                            <span class="tiered-pricing-block__price-discount">
								(24% off)							</span>
						                    </div>
                </div>
							
                <div class="tiered-pricing-block" data-tiered-quantity="3000" data-tiered-price="3.18" data-tiered-price-exclude-taxes="3.18" data-tiered-price-include-taxes="3.18">

                    <div class="tiered-pricing-block__quantity">3,000 pieces</div>
                    <div class="tiered-pricing-block__price">
						<span>
							<span class="woocommerce-Price-amount amount"><span class="woocommerce-Price-currencySymbol">$</span>3.18</span>						</span>
						
						                            <span class="tiered-pricing-block__price-discount">
								(25% off)							</span>
						                    </div>
                </div>
							
                <div class="tiered-pricing-block" data-tiered-quantity="5000" data-tiered-price="3.13" data-tiered-price-exclude-taxes="3.13" data-tiered-price-include-taxes="3.13">

                    <div class="tiered-pricing-block__quantity">5,000+ pieces</div>
                    <div class="tiered-pricing-block__price">
						<span>
							<span class="woocommerce-Price-amount amount"><span class="woocommerce-Price-currencySymbol">$</span>3.13</span>						</span>
						
						                            <span class="tiered-pricing-block__price-discount">
								(27% off)							</span>
						                    </div>
                </div>
						
			        </div>
		
		    </div>

    <style></style>
		</div>