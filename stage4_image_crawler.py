#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
步骤四：产品图片采集器
"""

import os
import re
import sqlite3
import requests
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin, urlparse
from pathlib import Path
from bs4 import BeautifulSoup
from typing import Dict, List, Tuple, Optional

class Stage4ImageCrawler:
    """步骤四图片采集器"""
    
    def __init__(self, db_path: str, max_workers: int = 5):
        """
        初始化图片采集器
        
        Args:
            db_path: 数据库路径
            max_workers: 最大线程数
        """
        self.db_path = db_path
        self.max_workers = max_workers
        self.is_running = False
        self.is_paused = False
        self.stop_requested = False
        
        # 统计信息
        self.total_count = 0
        self.completed_count = 0
        self.failed_count = 0
        self.stats_lock = threading.Lock()
        
        # 回调函数
        self.progress_callback = None
        self.error_callback = None
        self.completion_callback = None
        
        # 请求配置
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 图片根目录
        self.images_root = "images"
        os.makedirs(self.images_root, exist_ok=True)
    
    def set_callbacks(self, progress_callback=None, error_callback=None, completion_callback=None):
        """设置回调函数"""
        self.progress_callback = progress_callback
        self.error_callback = error_callback
        self.completion_callback = completion_callback
    
    def set_max_workers(self, max_workers: int):
        """设置最大线程数"""
        if max_workers < 1:
            max_workers = 1
        elif max_workers > 20:
            max_workers = 20
        
        self.max_workers = max_workers
        self._notify_progress(f"设置线程数为: {max_workers}")
    
    def get_max_workers(self) -> int:
        """获取最大线程数"""
        return self.max_workers
    
    def _notify_progress(self, message: str):
        """通知进度"""
        if self.progress_callback:
            self.progress_callback(message)
    
    def _notify_error(self, message: str):
        """通知错误"""
        if self.error_callback:
            self.error_callback(message)
    
    def _notify_completion(self, message: str):
        """通知完成"""
        if self.completion_callback:
            self.completion_callback(message)
    
    def get_products_for_image_crawl(self, filter_status: str = None) -> List[Dict]:
        """
        获取需要采集图片的产品列表
        
        Args:
            filter_status: 过滤状态 ('all', 'pending', 'failed', None)
            
        Returns:
            产品列表
        """
        products = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 查询需要采集图片的产品 - 简化逻辑，只判断product_urls与product_images的重复
                if filter_status == 'pending':
                    # 待处理：product_urls中存在但product_images中不存在的
                    query = '''
                        SELECT
                            pu.id as product_url_id,
                            pu.product_url,
                            pu.product_name,
                            pu.product_name as title,
                            '' as sku,
                            'pending' as crawl_status,
                            0 as crawl_attempts
                        FROM product_urls pu
                        LEFT JOIN product_images pi ON pu.id = pi.product_url_id
                        WHERE pi.product_url_id IS NULL
                        ORDER BY pu.id
                    '''
                elif filter_status == 'failed':
                    # 失败的：product_images中状态为failed的
                    query = '''
                        SELECT
                            pu.id as product_url_id,
                            pu.product_url,
                            pu.product_name,
                            pu.product_name as title,
                            pi.product_sku as sku,
                            pi.crawl_status,
                            pi.crawl_attempts
                        FROM product_urls pu
                        JOIN product_images pi ON pu.id = pi.product_url_id
                        WHERE pi.crawl_status = 'failed'
                        ORDER BY pu.id
                    '''
                else:  # 'all'
                    # 全部：product_urls中不存在于product_images中的，或者状态不是success的
                    query = '''
                        SELECT
                            pu.id as product_url_id,
                            pu.product_url,
                            pu.product_name,
                            pu.product_name as title,
                            COALESCE(pi.product_sku, '') as sku,
                            COALESCE(pi.crawl_status, 'pending') as crawl_status,
                            COALESCE(pi.crawl_attempts, 0) as crawl_attempts
                        FROM product_urls pu
                        LEFT JOIN product_images pi ON pu.id = pi.product_url_id
                        WHERE pi.product_url_id IS NULL OR pi.crawl_status != 'success'
                        ORDER BY pu.id
                    '''
                
                cursor.execute(query)
                rows = cursor.fetchall()
                
                for row in rows:
                    products.append({
                        'product_url_id': row[0],
                        'product_url': row[1],
                        'product_name': row[2],
                        'title': row[3],
                        'sku': row[4],
                        'crawl_status': row[5] or 'pending',
                        'crawl_attempts': row[6] or 0
                    })
                
        except Exception as e:
            self._notify_error(f"获取产品列表失败: {e}")
        
        return products
    
    def start_stage4(self, filter_status: str = None):
        """
        启动步骤四图片采集
        
        Args:
            filter_status: 过滤状态
        """
        if self.is_running:
            self._notify_error("图片采集已在运行中")
            return
        
        try:
            # 获取产品列表
            products = self.get_products_for_image_crawl(filter_status)
            
            if not products:
                self._notify_completion("没有需要采集图片的产品")
                return
            
            self.total_count = len(products)
            self.completed_count = 0
            self.failed_count = 0
            self.is_running = True
            self.is_paused = False
            self.stop_requested = False
            
            self._notify_progress(f"开始步骤四 - 多线程产品图片采集")
            self._notify_progress(f"找到 {self.total_count} 个产品待采集图片，使用 {self.max_workers} 个线程")
            
            # 启动多线程采集
            self._stage4_worker_multithreaded(products)
            
        except Exception as e:
            self._notify_error(f"启动步骤四失败: {e}")
            self.is_running = False
    
    def _stage4_worker_multithreaded(self, products: List[Dict]):
        """多线程图片采集工作器"""
        try:
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_product = {}
                
                for product in products:
                    if self.stop_requested:
                        break
                    
                    future = executor.submit(self._crawl_single_product_images, product)
                    future_to_product[future] = product
                
                # 处理完成的任务
                for future in as_completed(future_to_product):
                    if self.stop_requested:
                        break
                    
                    product = future_to_product[future]
                    product_name = product.get('title', product.get('product_name', 'Unknown'))
                    
                    try:
                        success = future.result()
                        
                        with self.stats_lock:
                            if success:
                                self.completed_count += 1
                            else:
                                self.failed_count += 1
                            
                            # 每完成10个产品更新一次进度
                            if (self.completed_count + self.failed_count) % 10 == 0:
                                progress_percent = (self.completed_count + self.failed_count) / self.total_count * 100
                                success_rate = self.completed_count / (self.completed_count + self.failed_count) * 100 if (self.completed_count + self.failed_count) > 0 else 0
                                progress_msg = f"已处理 {self.completed_count + self.failed_count}/{self.total_count} 个产品 ({progress_percent:.1f}%) - 成功: {self.completed_count}, 失败: {self.failed_count} (成功率: {success_rate:.1f}%)"
                                self._notify_progress(progress_msg)
                        
                    except Exception as e:
                        # 线程执行异常，也算失败
                        self._notify_error(f"处理产品 {product_name} 时发生异常: {str(e)}")
                        with self.stats_lock:
                            self.failed_count += 1
                            
                            # 更新进度
                            if (self.completed_count + self.failed_count) % 10 == 0:
                                progress_percent = (self.completed_count + self.failed_count) / self.total_count * 100
                                success_rate = self.completed_count / (self.completed_count + self.failed_count) * 100 if (self.completed_count + self.failed_count) > 0 else 0
                                progress_msg = f"已处理 {self.completed_count + self.failed_count}/{self.total_count} 个产品 ({progress_percent:.1f}%) - 成功: {self.completed_count}, 失败: {self.failed_count} (成功率: {success_rate:.1f}%)"
                                self._notify_progress(progress_msg)
            
            # 采集完成
            final_msg = f"步骤四图片采集完成！总计: {self.total_count}, 成功: {self.completed_count}, 失败: {self.failed_count}"
            self._notify_completion(final_msg)
            
        except Exception as e:
            self._notify_error(f"多线程图片采集失败: {e}")
        finally:
            self.is_running = False
    
    def _crawl_single_product_images(self, product: Dict) -> bool:
        """
        采集单个产品的图片
        
        Args:
            product: 产品信息
            
        Returns:
            是否成功
        """
        product_url_id = product['product_url_id']
        product_url = product['product_url']
        
        try:
            # 检查是否需要暂停
            while self.is_paused and not self.stop_requested:
                time.sleep(1)
            
            if self.stop_requested:
                return False
            
            # 更新采集尝试次数
            self._update_crawl_attempts(product_url_id)
            
            # 获取产品页面
            response = self.session.get(product_url, timeout=30)
            response.raise_for_status()
            
            # 解析页面
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 提取面包屑导航
            breadcrumb_path = self._extract_breadcrumb(soup)
            
            # 提取描述信息
            description = self._extract_description(soup)

            # 提取Additional Information
            additional_information = self._extract_additional_information(soup)

            # 解析属性信息
            attributes = self._parse_attributes_from_additional_info(additional_information)

            # 提取图片URL
            image_urls = self._extract_image_urls(soup, product_url)
            
            # 创建本地目录
            local_directory = self._create_local_directory(breadcrumb_path, product.get('title', ''))
            
            # 下载图片
            downloaded_images = self._download_images(image_urls, local_directory)
            
            # 保存到数据库
            self._save_product_images(
                product_url_id=product_url_id,
                product=product,
                breadcrumb_path=breadcrumb_path,
                description=description,
                additional_information=additional_information,
                attributes=attributes,
                local_directory=local_directory,
                downloaded_images=downloaded_images
            )
            
            return True
            
        except Exception as e:
            error_msg = f"采集产品图片失败: {str(e)}"
            self._notify_error(f"产品 {product_url}: {error_msg}")
            self._update_product_status(product_url_id, 'failed', error_msg)
            return False
    
    def pause(self):
        """暂停采集"""
        self.is_paused = True
        self._notify_progress("图片采集已暂停")
    
    def resume(self):
        """继续采集"""
        self.is_paused = False
        self._notify_progress("图片采集已继续")
    
    def stop(self):
        """停止采集"""
        self.stop_requested = True
        self.is_running = False
        self._notify_progress("图片采集已停止")

    def _extract_breadcrumb(self, soup: BeautifulSoup) -> str:
        """
        提取面包屑导航

        Args:
            soup: BeautifulSoup对象

        Returns:
            面包屑路径字符串
        """
        try:
            # 查找面包屑导航
            breadcrumb = soup.find('nav', class_='woocommerce-breadcrumb')
            if not breadcrumb:
                return ""

            # 提取所有链接文本
            links = breadcrumb.find_all('a')
            breadcrumb_parts = []

            for link in links:
                text = link.get_text(strip=True)
                if text and text.lower() != 'home':  # 跳过Home
                    breadcrumb_parts.append(text)

            # 添加当前页面标题（最后一个非链接文本）
            text_nodes = breadcrumb.get_text(separator='|').split('|')
            if text_nodes:
                last_part = text_nodes[-1].strip()
                if last_part and last_part not in breadcrumb_parts:
                    breadcrumb_parts.append(last_part)

            return ' / '.join(breadcrumb_parts)

        except Exception as e:
            self._notify_error(f"提取面包屑导航失败: {e}")
            return ""

    def _extract_description(self, soup: BeautifulSoup) -> str:
        """
        提取产品描述（保留table结构）

        Args:
            soup: BeautifulSoup对象

        Returns:
            描述HTML字符串
        """
        try:
            # 查找产品描述区域
            description_selectors = [
                '#tab-description',
                '.woocommerce-Tabs-panel--description',
                '.product-description',
                '.entry-content',
                '.product-details'
            ]

            description_content = ""

            for selector in description_selectors:
                desc_element = soup.select_one(selector)
                if desc_element:
                    # 保留HTML结构，特别是table
                    description_content = str(desc_element)
                    break

            return description_content

        except Exception as e:
            self._notify_error(f"提取产品描述失败: {e}")
            return ""

    def _extract_additional_information(self, soup: BeautifulSoup) -> str:
        """
        提取Additional Information部分（通常是h2标题下的内容）

        Args:
            soup: BeautifulSoup对象

        Returns:
            Additional Information HTML字符串
        """
        try:
            # 查找Additional Information标题
            additional_info_selectors = [
                'h2:contains("Additional Information")',
                'h2:contains("Additional information")',
                'h2:contains("ADDITIONAL INFORMATION")',
                '.additional-information',
                '#additional-information',
                '.product-additional-info'
            ]

            additional_content = ""

            # 尝试通过h2标题查找
            h2_elements = soup.find_all('h2')
            for h2 in h2_elements:
                h2_text = h2.get_text(strip=True).lower()
                if 'additional information' in h2_text:
                    # 找到h2后，获取其后的内容直到下一个h2或特定结束标签
                    content_parts = []
                    current = h2.next_sibling

                    while current:
                        if hasattr(current, 'name'):
                            # 如果遇到下一个h2，停止
                            if current.name == 'h2':
                                break
                            # 如果是有用的内容标签，添加到结果中
                            if current.name in ['p', 'div', 'table', 'ul', 'ol', 'dl']:
                                content_parts.append(str(current))
                        current = current.next_sibling

                    if content_parts:
                        additional_content = ''.join(content_parts)
                        break

            # 如果通过h2没找到，尝试其他选择器
            if not additional_content:
                for selector in additional_info_selectors[3:]:  # 跳过h2选择器
                    element = soup.select_one(selector)
                    if element:
                        additional_content = str(element)
                        break

            # 如果还是没找到，尝试查找包含"additional"的div或section
            if not additional_content:
                divs = soup.find_all(['div', 'section'], class_=lambda x: x and 'additional' in x.lower())
                for div in divs:
                    if div.get_text(strip=True):
                        additional_content = str(div)
                        break

            return additional_content

        except Exception as e:
            self._notify_error(f"提取Additional Information失败: {e}")
            return ""

    def _parse_attributes_from_additional_info(self, additional_info_html: str) -> List[Tuple[str, str]]:
        """
        从Additional Information HTML中解析属性信息

        Args:
            additional_info_html: Additional Information的HTML内容

        Returns:
            属性列表，每个元素是(name, value)元组，最多4个
        """
        attributes = []

        try:
            if not additional_info_html:
                return attributes

            soup = BeautifulSoup(additional_info_html, 'html.parser')

            # 查找属性表格的多种方式
            table_selectors = [
                'table.woocommerce-product-attributes',
                'table.shop_attributes',
                'table[aria-label*="Product Details"]',
                'table[aria-label*="Additional Information"]',
                '.woocommerce-product-attributes',
                '.product-attributes'
            ]

            table = None
            for selector in table_selectors:
                table = soup.select_one(selector)
                if table:
                    break

            if table:
                # 查找表格行
                rows = table.find_all('tr')
                for row in rows:
                    # 查找标签和值
                    th = row.find('th')
                    td = row.find('td')

                    if th and td:
                        # 提取属性名称
                        name = th.get_text(strip=True)

                        # 提取属性值，移除HTML标签
                        value = td.get_text(strip=True)

                        if name and value:
                            attributes.append((name, value))

                            # 最多保留4个属性
                            if len(attributes) >= 4:
                                break

            # 如果没有找到表格，尝试其他方式
            if not attributes:
                # 查找dl/dt/dd结构
                dl = soup.find('dl')
                if dl:
                    dts = dl.find_all('dt')
                    dds = dl.find_all('dd')

                    for dt, dd in zip(dts, dds):
                        name = dt.get_text(strip=True)
                        value = dd.get_text(strip=True)

                        if name and value:
                            attributes.append((name, value))

                            if len(attributes) >= 4:
                                break

                # 查找其他可能的属性结构
                if not attributes:
                    # 查找包含冒号的段落或div
                    elements = soup.find_all(['p', 'div', 'span'])
                    for element in elements:
                        text = element.get_text(strip=True)
                        if ':' in text:
                            parts = text.split(':', 1)
                            if len(parts) == 2:
                                name = parts[0].strip()
                                value = parts[1].strip()

                                if name and value:
                                    attributes.append((name, value))

                                    if len(attributes) >= 4:
                                        break

        except Exception as e:
            self._notify_error(f"解析属性信息失败: {e}")

        return attributes[:4]  # 确保最多返回4个属性

    def _extract_image_urls(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """
        提取产品图片URL

        Args:
            soup: BeautifulSoup对象
            base_url: 基础URL

        Returns:
            图片URL列表
        """
        image_urls = []

        try:
            # 查找产品图片的多种选择器
            image_selectors = [
                '.woocommerce-product-gallery img',
                '.product-images img',
                '.product-gallery img',
                '.wp-post-image',
                '.attachment-woocommerce_single',
                '.product-image img'
            ]

            found_images = set()  # 使用set避免重复

            for selector in image_selectors:
                images = soup.select(selector)
                for img in images:
                    # 获取图片URL
                    img_url = img.get('src') or img.get('data-src') or img.get('data-lazy-src')

                    if img_url:
                        # 转换为绝对URL
                        full_url = urljoin(base_url, img_url)

                        # 过滤掉明显不是产品图片的URL
                        if self._is_valid_product_image(full_url):
                            found_images.add(full_url)

            # 转换为列表并限制数量
            image_urls = list(found_images)[:10]  # 最多10张图片

        except Exception as e:
            self._notify_error(f"提取图片URL失败: {e}")

        return image_urls

    def _is_valid_product_image(self, url: str) -> bool:
        """
        判断是否是有效的产品图片

        Args:
            url: 图片URL

        Returns:
            是否有效
        """
        # 过滤条件
        invalid_patterns = [
            'logo',
            'icon',
            'button',
            'arrow',
            'placeholder',
            'loading',
            'spinner'
        ]

        url_lower = url.lower()

        # 检查文件扩展名
        valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        if not any(ext in url_lower for ext in valid_extensions):
            return False

        # 检查是否包含无效模式
        if any(pattern in url_lower for pattern in invalid_patterns):
            return False

        # 检查图片尺寸（通过URL判断，如果包含尺寸信息）
        if re.search(r'\d+x\d+', url_lower):
            # 如果尺寸太小，可能不是产品图片
            size_match = re.search(r'(\d+)x(\d+)', url_lower)
            if size_match:
                width, height = int(size_match.group(1)), int(size_match.group(2))
                if width < 100 or height < 100:
                    return False

        return True

    def _create_local_directory(self, breadcrumb_path: str, product_title: str) -> str:
        """
        创建本地目录结构（修复版本）

        Args:
            breadcrumb_path: 面包屑路径
            product_title: 产品标题

        Returns:
            本地目录路径
        """
        try:
            # 清理路径中的非法字符
            def clean_path_part(part: str) -> str:
                if not part:
                    return ""

                # 移除所有可能的问题字符
                cleaned = re.sub(r'[<>:"/\\|?*\xa0\u00a0\u2000-\u200f\u2028-\u202f\ufeff]', '', part)

                # 移除多余的空格
                cleaned = re.sub(r'\s+', ' ', cleaned)

                # 移除首尾的点、空格和特殊字符
                cleaned = cleaned.strip('. \t\n\r')

                # 移除括号内容
                cleaned = re.sub(r'\([^)]*\)', '', cleaned).strip()

                # 移除常见的后缀词
                cleaned = re.sub(r'\s+(with|w/|w)\s*$', '', cleaned, flags=re.IGNORECASE).strip()

                # 限制长度
                if len(cleaned) > 20:
                    cleaned = cleaned[:20].strip()

                return cleaned

            # 构建目录路径
            path_parts = [self.images_root]

            # 处理面包屑路径
            if breadcrumb_path:
                breadcrumb_parts = breadcrumb_path.split(' / ')
                # 只取前2级分类，避免路径过深
                for part in breadcrumb_parts[:2]:
                    cleaned_part = clean_path_part(part)
                    if cleaned_part and cleaned_part not in path_parts:
                        path_parts.append(cleaned_part)

            # 处理产品标题
            if product_title:
                cleaned_title = clean_path_part(product_title)
                # 避免重复
                if cleaned_title and cleaned_title not in path_parts:
                    path_parts.append(cleaned_title)

            # 如果路径太少，添加默认目录
            if len(path_parts) < 2:
                path_parts.append("products")

            # 构建路径
            full_path = os.path.join(*path_parts)

            # 检查路径长度（Windows限制260字符）
            if len(full_path) > 200:
                # 使用简化路径
                simplified_parts = [self.images_root]
                if len(path_parts) > 1:
                    simplified_parts.append(path_parts[1][:15])  # 主分类
                if len(path_parts) > 2:
                    simplified_parts.append(path_parts[-1][:20])  # 产品名
                else:
                    simplified_parts.append("item")

                full_path = os.path.join(*simplified_parts)

            # 创建目录
            os.makedirs(full_path, exist_ok=True)

            return full_path

        except Exception as e:
            self._notify_error(f"创建本地目录失败: {e}")
            # 返回默认路径
            try:
                default_path = os.path.join(self.images_root, "default")
                os.makedirs(default_path, exist_ok=True)
                return default_path
            except:
                # 如果连默认路径都创建失败，使用当前目录
                return "."

    def _download_images(self, image_urls: List[str], local_directory: str) -> List[Dict]:
        """
        下载图片到本地

        Args:
            image_urls: 图片URL列表
            local_directory: 本地目录

        Returns:
            下载结果列表
        """
        downloaded_images = []

        for i, img_url in enumerate(image_urls, 1):
            if i > 10:  # 最多10张图片
                break

            try:
                # 获取文件扩展名
                parsed_url = urlparse(img_url)
                file_extension = os.path.splitext(parsed_url.path)[1]
                if not file_extension:
                    file_extension = '.jpg'  # 默认扩展名

                # 生成本地文件名
                local_filename = f"image_{i}{file_extension}"
                local_filepath = os.path.join(local_directory, local_filename)

                # 下载图片
                response = self.session.get(img_url, timeout=30, stream=True)
                response.raise_for_status()

                # 保存图片
                with open(local_filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)

                # 使用相对路径存储（相对于项目根目录）
                relative_path = os.path.relpath(local_filepath)

                downloaded_images.append({
                    'index': i,
                    'url': img_url,
                    'local_path': relative_path,  # 存储相对路径
                    'absolute_path': local_filepath,  # 保留绝对路径用于验证
                    'success': True
                })

            except Exception as e:
                downloaded_images.append({
                    'index': i,
                    'url': img_url,
                    'local_path': None,
                    'success': False,
                    'error': str(e)
                })

        return downloaded_images

    def _save_product_images(self, product_url_id: int, product: Dict,
                           breadcrumb_path: str, description: str,
                           additional_information: str, attributes: List[Tuple[str, str]],
                           local_directory: str, downloaded_images: List[Dict]):
        """
        保存产品图片信息到数据库

        Args:
            product_url_id: 产品URL ID
            product: 产品信息
            breadcrumb_path: 面包屑路径
            description: 产品描述
            additional_information: Additional Information内容
            attributes: 解析后的属性列表
            local_directory: 本地目录
            downloaded_images: 下载的图片列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 准备图片字段数据
                image_data = {}
                images_downloaded = 0

                for img in downloaded_images:
                    if img['success']:
                        images_downloaded += 1

                    index = img['index']
                    image_data[f'image{index}_url'] = img['url']
                    image_data[f'image{index}_local'] = img['local_path'] if img['success'] else None

                # 准备属性数据
                attribute_data = {}
                for i in range(1, 5):  # 4个属性字段
                    if i <= len(attributes):
                        name, value = attributes[i-1]
                        attribute_data[f'attribute{i}_name'] = name
                        attribute_data[f'attribute{i}_value'] = value
                    else:
                        attribute_data[f'attribute{i}_name'] = None
                        attribute_data[f'attribute{i}_value'] = None

                # 构建SQL语句
                columns = [
                    'product_url_id', 'product_url', 'product_title', 'product_sku',
                    'description', 'additional_information',
                    'attribute1_name', 'attribute1_value',
                    'attribute2_name', 'attribute2_value',
                    'attribute3_name', 'attribute3_value',
                    'attribute4_name', 'attribute4_value',
                    'breadcrumb_path', 'local_directory',
                    'crawl_status', 'crawl_attempts', 'images_downloaded',
                    'last_crawl_attempt', 'updated_at'
                ]

                # 添加图片字段
                for i in range(1, 11):
                    columns.extend([f'image{i}_url', f'image{i}_local'])

                # 准备值
                values = [
                    product_url_id,
                    product['product_url'],
                    product.get('title', ''),
                    product.get('sku', ''),
                    description,
                    additional_information,
                    attribute_data['attribute1_name'],
                    attribute_data['attribute1_value'],
                    attribute_data['attribute2_name'],
                    attribute_data['attribute2_value'],
                    attribute_data['attribute3_name'],
                    attribute_data['attribute3_value'],
                    attribute_data['attribute4_name'],
                    attribute_data['attribute4_value'],
                    breadcrumb_path,
                    local_directory,
                    'success',
                    product.get('crawl_attempts', 0) + 1,
                    images_downloaded,
                    'CURRENT_TIMESTAMP',
                    'CURRENT_TIMESTAMP'
                ]

                # 添加图片数据
                for i in range(1, 11):
                    values.extend([
                        image_data.get(f'image{i}_url'),
                        image_data.get(f'image{i}_local')
                    ])

                # 使用REPLACE INTO处理重复记录
                placeholders = ', '.join(['?' for _ in values])
                columns_str = ', '.join(columns)

                sql = f'''
                    REPLACE INTO product_images ({columns_str})
                    VALUES ({placeholders})
                '''

                cursor.execute(sql, values)
                conn.commit()

        except Exception as e:
            self._notify_error(f"保存产品图片信息失败: {e}")
            self._update_product_status(product_url_id, 'failed', str(e))

    def _update_crawl_attempts(self, product_url_id: int):
        """更新采集尝试次数"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 先检查记录是否存在
                cursor.execute('''
                    SELECT product_url_id FROM product_images
                    WHERE product_url_id = ?
                ''', (product_url_id,))

                if cursor.fetchone():
                    # 记录存在，更新尝试次数
                    cursor.execute('''
                        UPDATE product_images
                        SET crawl_attempts = COALESCE(crawl_attempts, 0) + 1,
                            last_crawl_attempt = CURRENT_TIMESTAMP
                        WHERE product_url_id = ?
                    ''', (product_url_id,))
                else:
                    # 记录不存在，获取产品URL信息
                    cursor.execute('''
                        SELECT product_url, product_name
                        FROM product_urls
                        WHERE id = ?
                    ''', (product_url_id,))

                    url_info = cursor.fetchone()
                    if url_info:
                        product_url, product_name = url_info
                        # 创建新记录，包含必需的字段
                        cursor.execute('''
                            INSERT INTO product_images (
                                product_url_id, product_url, product_title,
                                crawl_attempts, last_crawl_attempt, crawl_status
                            ) VALUES (?, ?, ?, 1, CURRENT_TIMESTAMP, 'pending')
                        ''', (product_url_id, product_url, product_name))

                conn.commit()

        except Exception as e:
            self._notify_error(f"更新采集尝试次数失败: {e}")

    def _update_product_status(self, product_url_id: int, status: str, error_message: str = None):
        """更新产品状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE product_images
                    SET crawl_status = ?,
                        error_message = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE product_url_id = ?
                ''', (status, error_message, product_url_id))

                conn.commit()

        except Exception as e:
            self._notify_error(f"更新产品状态失败: {e}")

    def get_stage4_stats(self) -> Dict:
        """获取步骤四统计信息"""
        stats = {
            'total_products': 0,
            'pending': 0,
            'success': 0,
            'failed': 0,
            'total_images': 0
        }

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 总产品数（有详情的）
                cursor.execute('''
                    SELECT COUNT(*) FROM product_details
                    WHERE crawl_status = 'success' AND sku IS NOT NULL
                ''')
                stats['total_products'] = cursor.fetchone()[0]

                # 各状态统计
                cursor.execute('''
                    SELECT crawl_status, COUNT(*), SUM(images_downloaded)
                    FROM product_images
                    GROUP BY crawl_status
                ''')

                for status, count, images in cursor.fetchall():
                    if status in stats:
                        stats[status] = count
                        if images:
                            stats['total_images'] += images

                # 计算待处理数量
                stats['pending'] = stats['total_products'] - stats['success'] - stats['failed']

        except Exception as e:
            self._notify_error(f"获取统计信息失败: {e}")

        return stats
