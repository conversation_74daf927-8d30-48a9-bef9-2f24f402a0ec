#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
步骤四：产品图片采集器
"""

import os
import re
import sqlite3
import requests
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin, urlparse
from pathlib import Path
from bs4 import BeautifulSoup
from typing import Dict, List, Tuple, Optional

class Stage4ImageCrawler:
    """步骤四图片采集器"""
    
    def __init__(self, db_path: str, max_workers: int = 5):
        """
        初始化图片采集器
        
        Args:
            db_path: 数据库路径
            max_workers: 最大线程数
        """
        self.db_path = db_path
        self.max_workers = max_workers
        self.is_running = False
        self.is_paused = False
        self.stop_requested = False
        
        # 统计信息
        self.total_count = 0
        self.completed_count = 0
        self.failed_count = 0
        self.stats_lock = threading.Lock()
        
        # 回调函数
        self.progress_callback = None
        self.error_callback = None
        self.completion_callback = None
        
        # 请求配置
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 图片根目录
        self.images_root = "images"
        os.makedirs(self.images_root, exist_ok=True)
    
    def set_callbacks(self, progress_callback=None, error_callback=None, completion_callback=None):
        """设置回调函数"""
        self.progress_callback = progress_callback
        self.error_callback = error_callback
        self.completion_callback = completion_callback
    
    def set_max_workers(self, max_workers: int):
        """设置最大线程数"""
        if max_workers < 1:
            max_workers = 1
        elif max_workers > 20:
            max_workers = 20
        
        self.max_workers = max_workers
        self._notify_progress(f"设置线程数为: {max_workers}")
    
    def get_max_workers(self) -> int:
        """获取最大线程数"""
        return self.max_workers
    
    def _notify_progress(self, message: str):
        """通知进度"""
        if self.progress_callback:
            self.progress_callback(message)
    
    def _notify_error(self, message: str):
        """通知错误"""
        if self.error_callback:
            self.error_callback(message)
    
    def _notify_completion(self, message: str):
        """通知完成"""
        if self.completion_callback:
            self.completion_callback(message)
    
    def get_products_for_image_crawl(self, filter_status: str = None) -> List[Dict]:
        """
        获取需要采集图片的产品列表
        
        Args:
            filter_status: 过滤状态 ('all', 'pending', 'failed', None)
            
        Returns:
            产品列表
        """
        products = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 构建查询条件
                if filter_status == 'pending':
                    status_condition = "AND (pi.crawl_status IS NULL OR pi.crawl_status = 'pending')"
                elif filter_status == 'failed':
                    status_condition = "AND pi.crawl_status = 'failed'"
                else:  # 'all' or None
                    status_condition = ""
                
                # 查询需要采集图片的产品
                query = f'''
                    SELECT 
                        pu.id as product_url_id,
                        pu.product_url,
                        pu.product_name,
                        pd.title,
                        pd.sku,
                        pi.crawl_status,
                        pi.crawl_attempts
                    FROM product_urls pu
                    JOIN product_details pd ON pu.id = pd.product_url_id
                    LEFT JOIN product_images pi ON pu.id = pi.product_url_id
                    WHERE pd.crawl_status = 'success'
                      AND pd.sku IS NOT NULL 
                      AND pd.sku != ''
                      {status_condition}
                    ORDER BY pu.id
                '''
                
                cursor.execute(query)
                rows = cursor.fetchall()
                
                for row in rows:
                    products.append({
                        'product_url_id': row[0],
                        'product_url': row[1],
                        'product_name': row[2],
                        'title': row[3],
                        'sku': row[4],
                        'crawl_status': row[5] or 'pending',
                        'crawl_attempts': row[6] or 0
                    })
                
        except Exception as e:
            self._notify_error(f"获取产品列表失败: {e}")
        
        return products
    
    def start_stage4(self, filter_status: str = None):
        """
        启动步骤四图片采集
        
        Args:
            filter_status: 过滤状态
        """
        if self.is_running:
            self._notify_error("图片采集已在运行中")
            return
        
        try:
            # 获取产品列表
            products = self.get_products_for_image_crawl(filter_status)
            
            if not products:
                self._notify_completion("没有需要采集图片的产品")
                return
            
            self.total_count = len(products)
            self.completed_count = 0
            self.failed_count = 0
            self.is_running = True
            self.is_paused = False
            self.stop_requested = False
            
            self._notify_progress(f"开始步骤四 - 多线程产品图片采集")
            self._notify_progress(f"找到 {self.total_count} 个产品待采集图片，使用 {self.max_workers} 个线程")
            
            # 启动多线程采集
            self._stage4_worker_multithreaded(products)
            
        except Exception as e:
            self._notify_error(f"启动步骤四失败: {e}")
            self.is_running = False
    
    def _stage4_worker_multithreaded(self, products: List[Dict]):
        """多线程图片采集工作器"""
        try:
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_product = {}
                
                for product in products:
                    if self.stop_requested:
                        break
                    
                    future = executor.submit(self._crawl_single_product_images, product)
                    future_to_product[future] = product
                
                # 处理完成的任务
                for future in as_completed(future_to_product):
                    if self.stop_requested:
                        break
                    
                    product = future_to_product[future]
                    product_name = product.get('title', product.get('product_name', 'Unknown'))
                    
                    try:
                        success = future.result()
                        
                        with self.stats_lock:
                            if success:
                                self.completed_count += 1
                            else:
                                self.failed_count += 1
                            
                            # 每完成10个产品更新一次进度
                            if (self.completed_count + self.failed_count) % 10 == 0:
                                progress_percent = (self.completed_count + self.failed_count) / self.total_count * 100
                                success_rate = self.completed_count / (self.completed_count + self.failed_count) * 100 if (self.completed_count + self.failed_count) > 0 else 0
                                progress_msg = f"已处理 {self.completed_count + self.failed_count}/{self.total_count} 个产品 ({progress_percent:.1f}%) - 成功: {self.completed_count}, 失败: {self.failed_count} (成功率: {success_rate:.1f}%)"
                                self._notify_progress(progress_msg)
                        
                    except Exception as e:
                        # 线程执行异常，也算失败
                        self._notify_error(f"处理产品 {product_name} 时发生异常: {str(e)}")
                        with self.stats_lock:
                            self.failed_count += 1
                            
                            # 更新进度
                            if (self.completed_count + self.failed_count) % 10 == 0:
                                progress_percent = (self.completed_count + self.failed_count) / self.total_count * 100
                                success_rate = self.completed_count / (self.completed_count + self.failed_count) * 100 if (self.completed_count + self.failed_count) > 0 else 0
                                progress_msg = f"已处理 {self.completed_count + self.failed_count}/{self.total_count} 个产品 ({progress_percent:.1f}%) - 成功: {self.completed_count}, 失败: {self.failed_count} (成功率: {success_rate:.1f}%)"
                                self._notify_progress(progress_msg)
            
            # 采集完成
            final_msg = f"步骤四图片采集完成！总计: {self.total_count}, 成功: {self.completed_count}, 失败: {self.failed_count}"
            self._notify_completion(final_msg)
            
        except Exception as e:
            self._notify_error(f"多线程图片采集失败: {e}")
        finally:
            self.is_running = False
    
    def _crawl_single_product_images(self, product: Dict) -> bool:
        """
        采集单个产品的图片
        
        Args:
            product: 产品信息
            
        Returns:
            是否成功
        """
        product_url_id = product['product_url_id']
        product_url = product['product_url']
        
        try:
            # 检查是否需要暂停
            while self.is_paused and not self.stop_requested:
                time.sleep(1)
            
            if self.stop_requested:
                return False
            
            # 更新采集尝试次数
            self._update_crawl_attempts(product_url_id)
            
            # 获取产品页面
            response = self.session.get(product_url, timeout=30)
            response.raise_for_status()
            
            # 解析页面
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 提取面包屑导航
            breadcrumb_path = self._extract_breadcrumb(soup)
            
            # 提取描述信息
            description = self._extract_description(soup)
            
            # 提取图片URL
            image_urls = self._extract_image_urls(soup, product_url)
            
            # 创建本地目录
            local_directory = self._create_local_directory(breadcrumb_path, product.get('title', ''))
            
            # 下载图片
            downloaded_images = self._download_images(image_urls, local_directory)
            
            # 保存到数据库
            self._save_product_images(
                product_url_id=product_url_id,
                product=product,
                breadcrumb_path=breadcrumb_path,
                description=description,
                local_directory=local_directory,
                downloaded_images=downloaded_images
            )
            
            return True
            
        except Exception as e:
            error_msg = f"采集产品图片失败: {str(e)}"
            self._notify_error(f"产品 {product_url}: {error_msg}")
            self._update_product_status(product_url_id, 'failed', error_msg)
            return False
    
    def pause(self):
        """暂停采集"""
        self.is_paused = True
        self._notify_progress("图片采集已暂停")
    
    def resume(self):
        """继续采集"""
        self.is_paused = False
        self._notify_progress("图片采集已继续")
    
    def stop(self):
        """停止采集"""
        self.stop_requested = True
        self.is_running = False
        self._notify_progress("图片采集已停止")

    def _extract_breadcrumb(self, soup: BeautifulSoup) -> str:
        """
        提取面包屑导航

        Args:
            soup: BeautifulSoup对象

        Returns:
            面包屑路径字符串
        """
        try:
            # 查找面包屑导航
            breadcrumb = soup.find('nav', class_='woocommerce-breadcrumb')
            if not breadcrumb:
                return ""

            # 提取所有链接文本
            links = breadcrumb.find_all('a')
            breadcrumb_parts = []

            for link in links:
                text = link.get_text(strip=True)
                if text and text.lower() != 'home':  # 跳过Home
                    breadcrumb_parts.append(text)

            # 添加当前页面标题（最后一个非链接文本）
            text_nodes = breadcrumb.get_text(separator='|').split('|')
            if text_nodes:
                last_part = text_nodes[-1].strip()
                if last_part and last_part not in breadcrumb_parts:
                    breadcrumb_parts.append(last_part)

            return ' / '.join(breadcrumb_parts)

        except Exception as e:
            self._notify_error(f"提取面包屑导航失败: {e}")
            return ""

    def _extract_description(self, soup: BeautifulSoup) -> str:
        """
        提取产品描述（保留table结构）

        Args:
            soup: BeautifulSoup对象

        Returns:
            描述HTML字符串
        """
        try:
            # 查找产品描述区域
            description_selectors = [
                '#tab-description',
                '.woocommerce-Tabs-panel--description',
                '.product-description',
                '.entry-content',
                '.product-details'
            ]

            description_content = ""

            for selector in description_selectors:
                desc_element = soup.select_one(selector)
                if desc_element:
                    # 保留HTML结构，特别是table
                    description_content = str(desc_element)
                    break

            return description_content

        except Exception as e:
            self._notify_error(f"提取产品描述失败: {e}")
            return ""

    def _extract_image_urls(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """
        提取产品图片URL

        Args:
            soup: BeautifulSoup对象
            base_url: 基础URL

        Returns:
            图片URL列表
        """
        image_urls = []

        try:
            # 查找产品图片的多种选择器
            image_selectors = [
                '.woocommerce-product-gallery img',
                '.product-images img',
                '.product-gallery img',
                '.wp-post-image',
                '.attachment-woocommerce_single',
                '.product-image img'
            ]

            found_images = set()  # 使用set避免重复

            for selector in image_selectors:
                images = soup.select(selector)
                for img in images:
                    # 获取图片URL
                    img_url = img.get('src') or img.get('data-src') or img.get('data-lazy-src')

                    if img_url:
                        # 转换为绝对URL
                        full_url = urljoin(base_url, img_url)

                        # 过滤掉明显不是产品图片的URL
                        if self._is_valid_product_image(full_url):
                            found_images.add(full_url)

            # 转换为列表并限制数量
            image_urls = list(found_images)[:10]  # 最多10张图片

        except Exception as e:
            self._notify_error(f"提取图片URL失败: {e}")

        return image_urls

    def _is_valid_product_image(self, url: str) -> bool:
        """
        判断是否是有效的产品图片

        Args:
            url: 图片URL

        Returns:
            是否有效
        """
        # 过滤条件
        invalid_patterns = [
            'logo',
            'icon',
            'button',
            'arrow',
            'placeholder',
            'loading',
            'spinner'
        ]

        url_lower = url.lower()

        # 检查文件扩展名
        valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        if not any(ext in url_lower for ext in valid_extensions):
            return False

        # 检查是否包含无效模式
        if any(pattern in url_lower for pattern in invalid_patterns):
            return False

        # 检查图片尺寸（通过URL判断，如果包含尺寸信息）
        if re.search(r'\d+x\d+', url_lower):
            # 如果尺寸太小，可能不是产品图片
            size_match = re.search(r'(\d+)x(\d+)', url_lower)
            if size_match:
                width, height = int(size_match.group(1)), int(size_match.group(2))
                if width < 100 or height < 100:
                    return False

        return True

    def _create_local_directory(self, breadcrumb_path: str, product_title: str) -> str:
        """
        创建本地目录结构

        Args:
            breadcrumb_path: 面包屑路径
            product_title: 产品标题

        Returns:
            本地目录路径
        """
        try:
            # 清理路径中的非法字符
            def clean_path_part(part: str) -> str:
                # 移除或替换非法字符
                cleaned = re.sub(r'[<>:"/\\|?*]', '', part)
                cleaned = cleaned.strip('. ')
                return cleaned[:50]  # 限制长度

            # 构建目录路径
            path_parts = [self.images_root]

            # 添加面包屑路径
            if breadcrumb_path:
                breadcrumb_parts = breadcrumb_path.split(' / ')
                for part in breadcrumb_parts:
                    cleaned_part = clean_path_part(part)
                    if cleaned_part:
                        path_parts.append(cleaned_part)

            # 添加产品标题作为最终目录
            if product_title:
                cleaned_title = clean_path_part(product_title)
                if cleaned_title:
                    path_parts.append(cleaned_title)

            # 创建完整路径
            full_path = os.path.join(*path_parts)

            # 创建目录
            os.makedirs(full_path, exist_ok=True)

            return full_path

        except Exception as e:
            self._notify_error(f"创建本地目录失败: {e}")
            # 返回默认路径
            default_path = os.path.join(self.images_root, "default")
            os.makedirs(default_path, exist_ok=True)
            return default_path

    def _download_images(self, image_urls: List[str], local_directory: str) -> List[Dict]:
        """
        下载图片到本地

        Args:
            image_urls: 图片URL列表
            local_directory: 本地目录

        Returns:
            下载结果列表
        """
        downloaded_images = []

        for i, img_url in enumerate(image_urls, 1):
            if i > 10:  # 最多10张图片
                break

            try:
                # 获取文件扩展名
                parsed_url = urlparse(img_url)
                file_extension = os.path.splitext(parsed_url.path)[1]
                if not file_extension:
                    file_extension = '.jpg'  # 默认扩展名

                # 生成本地文件名
                local_filename = f"image_{i}{file_extension}"
                local_filepath = os.path.join(local_directory, local_filename)

                # 下载图片
                response = self.session.get(img_url, timeout=30, stream=True)
                response.raise_for_status()

                # 保存图片
                with open(local_filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)

                downloaded_images.append({
                    'index': i,
                    'url': img_url,
                    'local_path': local_filepath,
                    'success': True
                })

            except Exception as e:
                downloaded_images.append({
                    'index': i,
                    'url': img_url,
                    'local_path': None,
                    'success': False,
                    'error': str(e)
                })

        return downloaded_images

    def _save_product_images(self, product_url_id: int, product: Dict,
                           breadcrumb_path: str, description: str,
                           local_directory: str, downloaded_images: List[Dict]):
        """
        保存产品图片信息到数据库

        Args:
            product_url_id: 产品URL ID
            product: 产品信息
            breadcrumb_path: 面包屑路径
            description: 产品描述
            local_directory: 本地目录
            downloaded_images: 下载的图片列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 准备图片字段数据
                image_data = {}
                images_downloaded = 0

                for img in downloaded_images:
                    if img['success']:
                        images_downloaded += 1

                    index = img['index']
                    image_data[f'image{index}_url'] = img['url']
                    image_data[f'image{index}_local'] = img['local_path'] if img['success'] else None

                # 构建SQL语句
                columns = [
                    'product_url_id', 'product_url', 'product_title', 'product_sku',
                    'description', 'breadcrumb_path', 'local_directory',
                    'crawl_status', 'crawl_attempts', 'images_downloaded',
                    'last_crawl_attempt', 'updated_at'
                ]

                # 添加图片字段
                for i in range(1, 11):
                    columns.extend([f'image{i}_url', f'image{i}_local'])

                # 准备值
                values = [
                    product_url_id,
                    product['product_url'],
                    product.get('title', ''),
                    product.get('sku', ''),
                    description,
                    breadcrumb_path,
                    local_directory,
                    'success',
                    product.get('crawl_attempts', 0) + 1,
                    images_downloaded,
                    'CURRENT_TIMESTAMP',
                    'CURRENT_TIMESTAMP'
                ]

                # 添加图片数据
                for i in range(1, 11):
                    values.extend([
                        image_data.get(f'image{i}_url'),
                        image_data.get(f'image{i}_local')
                    ])

                # 使用REPLACE INTO处理重复记录
                placeholders = ', '.join(['?' for _ in values])
                columns_str = ', '.join(columns)

                sql = f'''
                    REPLACE INTO product_images ({columns_str})
                    VALUES ({placeholders})
                '''

                cursor.execute(sql, values)
                conn.commit()

        except Exception as e:
            self._notify_error(f"保存产品图片信息失败: {e}")
            self._update_product_status(product_url_id, 'failed', str(e))

    def _update_crawl_attempts(self, product_url_id: int):
        """更新采集尝试次数"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE product_images
                    SET crawl_attempts = crawl_attempts + 1,
                        last_crawl_attempt = CURRENT_TIMESTAMP
                    WHERE product_url_id = ?
                ''', (product_url_id,))

                if cursor.rowcount == 0:
                    # 如果记录不存在，创建一个
                    cursor.execute('''
                        INSERT INTO product_images (product_url_id, crawl_attempts, last_crawl_attempt)
                        VALUES (?, 1, CURRENT_TIMESTAMP)
                    ''', (product_url_id,))

                conn.commit()

        except Exception as e:
            self._notify_error(f"更新采集尝试次数失败: {e}")

    def _update_product_status(self, product_url_id: int, status: str, error_message: str = None):
        """更新产品状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE product_images
                    SET crawl_status = ?,
                        error_message = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE product_url_id = ?
                ''', (status, error_message, product_url_id))

                conn.commit()

        except Exception as e:
            self._notify_error(f"更新产品状态失败: {e}")

    def get_stage4_stats(self) -> Dict:
        """获取步骤四统计信息"""
        stats = {
            'total_products': 0,
            'pending': 0,
            'success': 0,
            'failed': 0,
            'total_images': 0
        }

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 总产品数（有详情的）
                cursor.execute('''
                    SELECT COUNT(*) FROM product_details
                    WHERE crawl_status = 'success' AND sku IS NOT NULL
                ''')
                stats['total_products'] = cursor.fetchone()[0]

                # 各状态统计
                cursor.execute('''
                    SELECT crawl_status, COUNT(*), SUM(images_downloaded)
                    FROM product_images
                    GROUP BY crawl_status
                ''')

                for status, count, images in cursor.fetchall():
                    if status in stats:
                        stats[status] = count
                        if images:
                            stats['total_images'] += images

                # 计算待处理数量
                stats['pending'] = stats['total_products'] - stats['success'] - stats['failed']

        except Exception as e:
            self._notify_error(f"获取统计信息失败: {e}")

        return stats
