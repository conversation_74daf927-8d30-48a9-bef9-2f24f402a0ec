#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查产品URL数据库状态
"""

import sqlite3

def check_product_urls():
    """检查产品URL数据库状态"""
    print("=" * 60)
    print("🔍 检查产品URL数据库状态")
    print("=" * 60)
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 1. 检查总产品URL数量
            cursor.execute('SELECT COUNT(*) FROM product_urls')
            total_products = cursor.fetchone()[0]
            print(f"📊 总产品URL数量: {total_products}")
            
            # 2. 按子分类统计产品数量
            print(f"\n📋 按子分类统计产品数量:")
            cursor.execute('''
                SELECT sc.sub_category_name, sc.sub_category_url, COUNT(pu.id) as product_count
                FROM sub_categories sc
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY sc.id, sc.sub_category_name, sc.sub_category_url
                ORDER BY product_count DESC
            ''')
            
            rows = cursor.fetchall()
            for i, (name, url, count) in enumerate(rows, 1):
                status = "✅" if count > 0 else "❌"
                print(f"   {i:3d}. {status} {name}: {count} 个产品")
                if count == 0:
                    print(f"        URL: {url}")
            
            # 3. 检查新添加的分类
            print(f"\n🆕 检查新添加的分类:")
            new_categories = [
                'Wellness Safety',
                'Toys Games Novelties', 
                'Usb Flash Drives',
                'Tech Mobile',
                'Stress Relievers',
                'Outdoor Leisure',
                'Office School Supplies',
                'Auto Accessories',
                'Home Auto',
                'Tumblers Drinkware Can Cooler',
                'Bottles Drinkware Can Cooler',
                'Drinkware Can Cooler',
                'Bags Trade Show',
                'Apparel Accessories'
            ]
            
            for cat_name in new_categories:
                cursor.execute('''
                    SELECT sc.id, sc.sub_category_url, COUNT(pu.id) as product_count
                    FROM sub_categories sc
                    LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                    WHERE sc.sub_category_name = ?
                    GROUP BY sc.id
                ''', (cat_name,))
                
                result = cursor.fetchone()
                if result:
                    cat_id, cat_url, count = result
                    status = "✅" if count > 0 else "❌"
                    print(f"   {status} {cat_name}: {count} 个产品 (ID: {cat_id})")
                    
                    # 如果有产品，显示最新的几个
                    if count > 0:
                        cursor.execute('''
                            SELECT product_name, product_url, created_at
                            FROM product_urls 
                            WHERE sub_category_id = ?
                            ORDER BY id DESC LIMIT 3
                        ''', (cat_id,))
                        
                        products = cursor.fetchall()
                        for prod_name, prod_url, created_at in products:
                            print(f"      - {prod_name} ({created_at})")
                else:
                    print(f"   ❓ {cat_name}: 分类不存在")
            
            # 4. 检查最近添加的产品URL
            print(f"\n🕒 最近添加的产品URL (最新10个):")
            cursor.execute('''
                SELECT pu.product_name, sc.sub_category_name, pu.created_at
                FROM product_urls pu
                JOIN sub_categories sc ON pu.sub_category_id = sc.id
                ORDER BY pu.id DESC LIMIT 10
            ''')
            
            recent_products = cursor.fetchall()
            for i, (prod_name, cat_name, created_at) in enumerate(recent_products, 1):
                print(f"   {i:2d}. {prod_name} ({cat_name}) - {created_at}")
            
            # 5. 检查数据库事务状态
            print(f"\n🔄 数据库状态检查:")
            cursor.execute('PRAGMA integrity_check')
            integrity = cursor.fetchone()[0]
            print(f"   数据库完整性: {integrity}")
            
            # 6. 检查是否有未提交的事务
            cursor.execute('BEGIN IMMEDIATE')
            cursor.execute('ROLLBACK')
            print(f"   事务状态: 正常")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

def check_crawl_process():
    """检查采集过程中可能的问题"""
    print(f"\n" + "=" * 60)
    print("🔧 诊断采集过程问题")
    print("=" * 60)
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 检查_crawl_products_for_category方法是否正确保存数据
            print("🔍 检查可能的问题:")
            
            # 1. 检查子分类ID是否正确
            cursor.execute('SELECT MAX(id) FROM sub_categories')
            max_sub_id = cursor.fetchone()[0]
            print(f"   最大子分类ID: {max_sub_id}")
            
            # 2. 检查是否有重复的产品URL
            cursor.execute('''
                SELECT product_url, COUNT(*) as count
                FROM product_urls
                GROUP BY product_url
                HAVING count > 1
                LIMIT 5
            ''')
            
            duplicates = cursor.fetchall()
            if duplicates:
                print(f"   发现重复产品URL: {len(duplicates)} 组")
                for url, count in duplicates:
                    print(f"      {url}: {count} 次")
            else:
                print(f"   ✅ 没有重复的产品URL")
            
            # 3. 检查产品URL格式
            cursor.execute('''
                SELECT COUNT(*) FROM product_urls 
                WHERE product_url NOT LIKE 'https://novolinepromo.com/%'
            ''')
            
            invalid_urls = cursor.fetchone()[0]
            if invalid_urls > 0:
                print(f"   ⚠️ 发现 {invalid_urls} 个格式异常的URL")
            else:
                print(f"   ✅ 所有产品URL格式正常")
                
    except Exception as e:
        print(f"❌ 诊断失败: {e}")

def main():
    """主函数"""
    print("🔍 产品URL数据库检查工具")
    print("检查采集的产品URL是否正确保存到数据库")
    print()
    
    check_product_urls()
    check_crawl_process()
    
    print(f"\n" + "=" * 60)
    print("检查完成")

if __name__ == "__main__":
    main()
