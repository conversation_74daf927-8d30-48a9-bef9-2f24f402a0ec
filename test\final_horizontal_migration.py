#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终的横向阶梯价格迁移脚本
直接重建数据库结构，不依赖现有方法
"""

import os
import sqlite3
import shutil
from datetime import datetime

def backup_database(db_path):
    """备份数据库"""
    if not os.path.exists(db_path):
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = "backup"
    os.makedirs(backup_dir, exist_ok=True)
    backup_path = f"{backup_dir}/{os.path.basename(db_path)}.backup_{timestamp}"
    
    shutil.copy2(db_path, backup_path)
    print(f"✅ 数据库已备份到: {backup_path}")
    return backup_path

def migrate_database(db_path):
    """执行数据库迁移"""
    print(f"🔧 开始迁移数据库: {db_path}")
    
    # 备份数据库
    backup_path = backup_database(db_path)
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 1. 备份现有数据
            print("📋 备份现有数据...")
            
            # 备份product_urls数据
            cursor.execute("SELECT * FROM product_urls")
            product_urls_data = cursor.fetchall()
            print(f"   product_urls: {len(product_urls_data)} 条")
            
            # 备份main_categories数据
            cursor.execute("SELECT * FROM main_categories")
            main_categories_data = cursor.fetchall()
            print(f"   main_categories: {len(main_categories_data)} 条")
            
            # 备份sub_categories数据
            cursor.execute("SELECT * FROM sub_categories")
            sub_categories_data = cursor.fetchall()
            print(f"   sub_categories: {len(sub_categories_data)} 条")
            
            # 2. 删除旧的product_details和tier_prices表
            print("🗑️ 删除旧表...")
            cursor.execute("DROP TABLE IF EXISTS product_details")
            cursor.execute("DROP TABLE IF EXISTS tier_prices")
            
            # 3. 创建新的product_details表
            print("🔧 创建新的product_details表...")
            cursor.execute('''
                CREATE TABLE product_details (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_url_id INTEGER NOT NULL UNIQUE,
                    
                    -- 基本信息
                    title TEXT,
                    sku TEXT,
                    product_image_url TEXT,
                    short_description TEXT,
                    
                    -- 导航信息
                    nav_level1 TEXT,
                    nav_level2 TEXT,
                    nav_level3 TEXT,
                    nav_full TEXT,
                    
                    -- 分类信息
                    category_name TEXT,
                    category_url TEXT,
                    
                    -- 阶梯价格1 (通常是1件的价格)
                    tier1_quantity TEXT,        -- "1 piece"
                    tier1_price DECIMAL(10,2),  -- 4.29
                    tier1_discount TEXT,        -- "0% off"
                    
                    -- 阶梯价格2
                    tier2_quantity TEXT,        -- "200 pieces"
                    tier2_price DECIMAL(10,2),  -- 3.30
                    tier2_discount TEXT,        -- "23% off"
                    
                    -- 阶梯价格3
                    tier3_quantity TEXT,        -- "500 pieces"
                    tier3_price DECIMAL(10,2),  -- 3.26
                    tier3_discount TEXT,        -- "24% off"
                    
                    -- 阶梯价格4
                    tier4_quantity TEXT,        -- "1,000 pieces"
                    tier4_price DECIMAL(10,2),  -- 3.22
                    tier4_discount TEXT,        -- "24% off"
                    
                    -- 阶梯价格5
                    tier5_quantity TEXT,        -- "3,000 pieces"
                    tier5_price DECIMAL(10,2),  -- 3.18
                    tier5_discount TEXT,        -- "25% off"
                    
                    -- 阶梯价格6
                    tier6_quantity TEXT,        -- "5,000+ pieces"
                    tier6_price DECIMAL(10,2),  -- 3.13
                    tier6_discount TEXT,        -- "27% off"
                    
                    -- 合并的完整阶梯价格信息
                    all_tiers_combined TEXT,    -- "1 piece: $4.29 (0% off)||200 pieces: $3.30 (23% off)||..."
                    
                    -- 采集状态管理
                    crawl_status TEXT DEFAULT 'pending',
                    crawl_attempts INTEGER DEFAULT 0,
                    last_crawl_attempt TIMESTAMP,
                    error_message TEXT,
                    
                    -- 时间戳
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (product_url_id) REFERENCES product_urls (id)
                )
            ''')
            
            conn.commit()
            print("✅ 新表创建完成")
            
            # 4. 验证表结构
            cursor.execute("PRAGMA table_info(product_details)")
            columns = cursor.fetchall()
            print(f"📊 新表结构: {len(columns)} 列")
            
            # 5. 测试插入功能
            print("🧪 测试插入功能...")
            
            # 确保有测试用的product_url
            if product_urls_data:
                test_product_url_id = product_urls_data[0][0]  # 使用第一个产品URL的ID
            else:
                # 创建一个测试产品URL
                cursor.execute('''
                    INSERT INTO product_urls (sub_category_id, product_name, product_url)
                    VALUES (1, 'Test Product', 'https://test.com/test')
                ''')
                test_product_url_id = cursor.lastrowid
                conn.commit()
            
            # 测试数据
            test_insert = '''
                INSERT INTO product_details
                (product_url_id, title, sku, nav_level1, nav_level2, nav_full,
                 category_name, tier1_quantity, tier1_price, tier1_discount,
                 tier2_quantity, tier2_price, tier2_discount,
                 all_tiers_combined, crawl_status, crawl_attempts)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            
            test_values = (
                test_product_url_id,
                'Test Product Title',
                'TEST123',
                'Test Category',
                'Test Subcategory',
                'Test Category > Test Subcategory',
                'Test Subcategory',
                '1 piece',
                4.29,
                '0% off',
                '200 pieces',
                3.30,
                '23% off',
                '1 piece: $4.29 (0% off)||200 pieces: $3.30 (23% off)',
                'success',
                0
            )
            
            cursor.execute(test_insert, test_values)
            test_id = cursor.lastrowid
            conn.commit()
            
            # 验证测试数据
            cursor.execute('''
                SELECT title, tier1_quantity, tier1_price, tier1_discount, all_tiers_combined
                FROM product_details WHERE id = ?
            ''', (test_id,))
            
            result = cursor.fetchone()
            if result:
                print(f"   ✅ 测试成功:")
                print(f"      标题: {result[0]}")
                print(f"      阶梯1: {result[1]} - ${result[2]} ({result[3]})")
                print(f"      合并信息: {result[4]}")
                
                # 清理测试数据
                cursor.execute('DELETE FROM product_details WHERE id = ?', (test_id,))
                if not product_urls_data:  # 如果是我们创建的测试数据，也删除
                    cursor.execute('DELETE FROM product_urls WHERE id = ?', (test_product_url_id,))
                conn.commit()
                print("   ✅ 测试数据已清理")
            
            print("✅ 数据库迁移完成")
            return True
            
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 恢复备份
        if backup_path and os.path.exists(backup_path):
            print("🔄 正在恢复备份...")
            shutil.copy2(backup_path, db_path)
            print("✅ 已恢复到备份版本")
        
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Novoline爬虫 - 最终横向阶梯价格迁移")
    print("=" * 60)
    print("此工具将:")
    print("1. 重建product_details表为横向阶梯价格结构")
    print("2. 支持6个阶梯价格层级 (tier1-tier6)")
    print("3. 每个阶梯包含: 数量、价格、折扣")
    print("4. 添加all_tiers_combined合并字段")
    print("5. 移除tier_prices表")
    print("6. 便于Excel导出，无需vlookup")
    print()
    
    db_path = "novoline_spider_v2.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"📁 找到数据库文件: {db_path}")
    
    # 确认迁移
    confirm = input(f"\n确认执行最终迁移? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 迁移已取消")
        return
    
    # 执行迁移
    if migrate_database(db_path):
        print(f"\n🎉 数据库迁移成功！")
        print("\n📋 新的数据结构:")
        print("   - product_details表: 横向存储所有阶梯价格")
        print("   - tier1-tier6: 每个阶梯3个字段 (数量、价格、折扣)")
        print("   - all_tiers_combined: 格式化的完整阶梯信息")
        print("   - 导出Excel时每个产品一行，包含所有信息")
        print("\n🎯 现在可以运行第三阶段采集了！")
        print("📊 数据将直接存储在横向结构中，便于分析和导出！")
    else:
        print(f"\n❌ 数据库迁移失败")

if __name__ == "__main__":
    main()
