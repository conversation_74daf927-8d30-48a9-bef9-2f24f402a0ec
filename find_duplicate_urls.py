#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
找出主目录和子目录中重复的URL
"""

import sqlite3

def find_duplicate_urls():
    """找出主目录和子目录中重复的URL"""
    print("=" * 60)
    print("🔍 查找主目录和子目录中重复的URL")
    print("=" * 60)
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 查找重复的URL
            cursor.execute('''
                SELECT 
                    mc.id as main_id,
                    mc.category_name as main_name,
                    mc.category_url as main_url,
                    sc.id as sub_id,
                    sc.sub_category_name as sub_name,
                    sc.sub_category_url as sub_url,
                    COUNT(pu.id) as product_count
                FROM main_categories mc
                INNER JOIN sub_categories sc ON mc.category_url = sc.sub_category_url
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY mc.id, sc.id
                ORDER BY mc.id
            ''')
            
            duplicates = cursor.fetchall()
            
            if duplicates:
                print(f"📋 发现 {len(duplicates)} 个重复的URL:")
                print()
                
                for main_id, main_name, main_url, sub_id, sub_name, sub_url, product_count in duplicates:
                    print(f"🔄 重复URL: {main_url}")
                    print(f"   主目录: [ID:{main_id}] {main_name}")
                    print(f"   子目录: [ID:{sub_id}] {sub_name} (产品数: {product_count})")
                    print()
                
                return duplicates
            else:
                print("✅ 没有发现重复的URL")
                return []
                
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return []

def find_main_category_in_subcategories():
    """查找主目录URL在子目录中的情况"""
    print("\n" + "=" * 60)
    print("🔍 查找主目录URL在子目录表中的情况")
    print("=" * 60)
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 查找主目录URL在子目录中的记录
            cursor.execute('''
                SELECT 
                    mc.id as main_id,
                    mc.category_name as main_name,
                    mc.category_url as main_url,
                    sc.id as sub_id,
                    sc.sub_category_name as sub_name,
                    sc.main_category_id,
                    COUNT(pu.id) as product_count
                FROM main_categories mc
                LEFT JOIN sub_categories sc ON mc.category_url = sc.sub_category_url
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                WHERE sc.id IS NOT NULL
                GROUP BY mc.id, sc.id
                ORDER BY mc.id
            ''')
            
            results = cursor.fetchall()
            
            if results:
                print(f"📋 主目录URL在子目录中的记录 ({len(results)} 个):")
                print()
                
                should_exclude = []
                
                for main_id, main_name, main_url, sub_id, sub_name, main_cat_id, product_count in results:
                    print(f"🎯 主目录: [ID:{main_id}] {main_name}")
                    print(f"   URL: {main_url}")
                    print(f"   在子目录中: [ID:{sub_id}] {sub_name}")
                    print(f"   所属主目录ID: {main_cat_id}")
                    print(f"   产品数量: {product_count}")
                    
                    should_exclude.append(sub_id)
                    print()
                
                return should_exclude
            else:
                print("✅ 主目录URL没有在子目录中重复")
                return []
                
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return []

def get_uncrawled_subcategories_excluding_main():
    """获取未采集的子目录，排除主目录重复的"""
    print("\n" + "=" * 60)
    print("🔍 获取真正需要采集的子目录")
    print("=" * 60)
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 查找未采集的子目录，排除与主目录重复的
            cursor.execute('''
                SELECT 
                    sc.id,
                    sc.sub_category_name,
                    sc.sub_category_url,
                    sc.main_category_id,
                    mc.category_name as main_category_name,
                    COUNT(pu.id) as product_count,
                    CASE 
                        WHEN EXISTS (
                            SELECT 1 FROM main_categories mc2 
                            WHERE mc2.category_url = sc.sub_category_url
                        ) THEN 'YES' 
                        ELSE 'NO' 
                    END as is_duplicate_of_main
                FROM sub_categories sc
                LEFT JOIN main_categories mc ON sc.main_category_id = mc.id
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY sc.id
                HAVING product_count = 0
                ORDER BY is_duplicate_of_main DESC, sc.id
            ''')
            
            results = cursor.fetchall()
            
            if results:
                duplicates = []
                real_uncrawled = []
                
                for sub_id, sub_name, sub_url, main_id, main_name, product_count, is_duplicate in results:
                    if is_duplicate == 'YES':
                        duplicates.append((sub_id, sub_name, sub_url))
                    else:
                        real_uncrawled.append((sub_id, sub_name, sub_url))
                
                print(f"📊 未采集子目录分析:")
                print(f"   总未采集: {len(results)}")
                print(f"   主目录重复: {len(duplicates)}")
                print(f"   真正需要采集: {len(real_uncrawled)}")
                
                if duplicates:
                    print(f"\n❌ 与主目录重复的子目录 (不需要采集):")
                    for sub_id, sub_name, sub_url in duplicates:
                        print(f"   [ID:{sub_id}] {sub_name}")
                        print(f"      URL: {sub_url}")
                
                if real_uncrawled:
                    print(f"\n✅ 真正需要采集的子目录:")
                    for sub_id, sub_name, sub_url in real_uncrawled:
                        print(f"   [ID:{sub_id}] {sub_name}")
                        print(f"      URL: {sub_url}")
                
                return duplicates, real_uncrawled
            else:
                print("✅ 所有子目录都已采集完成")
                return [], []
                
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return [], []

def create_filtered_batch_crawl_sql():
    """生成排除主目录重复的批量采集SQL"""
    print("\n" + "=" * 60)
    print("📝 生成优化的批量采集查询")
    print("=" * 60)
    
    sql_query = '''
-- 获取真正需要采集的子目录（排除与主目录重复的）
SELECT 
    sc.id,
    sc.sub_category_name,
    sc.sub_category_url
FROM sub_categories sc
LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
WHERE pu.id IS NULL  -- 未采集
  AND NOT EXISTS (
      SELECT 1 FROM main_categories mc 
      WHERE mc.category_url = sc.sub_category_url
  )  -- 排除与主目录重复的
ORDER BY sc.id;
'''
    
    print("🔧 优化后的查询SQL:")
    print(sql_query)
    
    return sql_query

def main():
    """主函数"""
    print("🔍 主目录和子目录重复URL分析工具")
    print("找出不需要采集的重复URL，优化批量采集")
    print()
    
    # 1. 查找重复URL
    duplicates = find_duplicate_urls()
    
    # 2. 查找主目录在子目录中的情况
    should_exclude = find_main_category_in_subcategories()
    
    # 3. 获取真正需要采集的子目录
    duplicate_subs, real_uncrawled = get_uncrawled_subcategories_excluding_main()
    
    # 4. 生成优化的SQL查询
    create_filtered_batch_crawl_sql()
    
    print("\n" + "=" * 60)
    print("🎯 分析结果总结")
    print("=" * 60)
    
    if duplicate_subs:
        print(f"⚠️ 发现问题:")
        print(f"   • {len(duplicate_subs)} 个子目录与主目录URL重复")
        print(f"   • 这些不需要采集，因为它们只是分类页面")
        
    if real_uncrawled:
        print(f"\n✅ 优化建议:")
        print(f"   • 只需采集 {len(real_uncrawled)} 个真正的子目录")
        print(f"   • 跳过与主目录重复的 {len(duplicate_subs)} 个")
        print(f"   • 这样可以避免无效采集")
        
        print(f"\n🔧 下一步:")
        print(f"   1. 修改批量采集逻辑，排除重复的主目录URL")
        print(f"   2. 只采集真正的子目录")
        print(f"   3. 提高采集效率")
    else:
        print(f"\n🎉 所有需要的子目录都已采集完成！")

if __name__ == "__main__":
    main()
