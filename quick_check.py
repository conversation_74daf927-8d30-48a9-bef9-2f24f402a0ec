#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速检查数据库状态
"""

import sqlite3

def quick_check():
    """快速检查数据库状态"""
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 检查未采集的分类
            cursor.execute('''
                SELECT sc.id, sc.sub_category_name, COUNT(pu.id) as product_count
                FROM sub_categories sc
                LEFT JOIN product_urls pu ON sc.id = pu.sub_category_id
                GROUP BY sc.id, sc.sub_category_name
                HAVING product_count = 0
                ORDER BY sc.id
            ''')
            
            uncrawled = cursor.fetchall()
            
            # 总统计
            cursor.execute('SELECT COUNT(*) FROM sub_categories')
            total_categories = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM product_urls')
            total_products = cursor.fetchone()[0]
            
            print("=" * 50)
            print("📊 数据库快速检查")
            print("=" * 50)
            print(f"总分类数: {total_categories}")
            print(f"总产品URL: {total_products:,}")
            print(f"未采集分类: {len(uncrawled)}")
            
            if uncrawled:
                print(f"\n❌ 未采集的分类:")
                for cat_id, name, count in uncrawled:
                    print(f"   [ID:{cat_id}] {name}")
            else:
                print(f"\n✅ 所有分类都已采集完成！")
            
    except Exception as e:
        print(f"检查失败: {e}")

if __name__ == "__main__":
    quick_check()
