#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复数据库结构脚本
解决第二阶段采集时的字段错误问题
"""

import os
import sqlite3
import shutil
from datetime import datetime

def backup_database(db_path):
    """备份数据库"""
    if not os.path.exists(db_path):
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = "backup"
    os.makedirs(backup_dir, exist_ok=True)
    backup_path = f"{backup_dir}/{os.path.basename(db_path)}.backup_{timestamp}"
    
    shutil.copy2(db_path, backup_path)
    print(f"✅ 数据库已备份到: {backup_path}")
    return backup_path

def fix_database_structure(db_path):
    """修复数据库结构"""
    print(f"🔧 开始修复数据库: {db_path}")
    
    # 备份数据库
    backup_path = backup_database(db_path)
    
    try:
        # 连接数据库
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查当前product_urls表结构
            cursor.execute("PRAGMA table_info(product_urls)")
            current_columns = [row[1] for row in cursor.fetchall()]
            print(f"当前product_urls表列: {current_columns}")
            
            # 如果缺少created_at字段，重建表
            if 'created_at' not in current_columns:
                print("🔄 重建product_urls表...")
                
                # 1. 备份现有数据
                cursor.execute("SELECT * FROM product_urls")
                existing_data = cursor.fetchall()
                print(f"   备份了 {len(existing_data)} 条产品数据")
                
                # 2. 删除旧表
                cursor.execute("DROP TABLE IF EXISTS product_urls")
                
                # 3. 创建新表（正确的结构）
                cursor.execute('''
                    CREATE TABLE product_urls (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sub_category_id INTEGER,
                        product_name TEXT NOT NULL,
                        product_url TEXT NOT NULL UNIQUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (sub_category_id) REFERENCES sub_categories (id)
                    )
                ''')
                
                # 4. 恢复数据
                for row in existing_data:
                    if len(row) >= 4:  # 至少有id, sub_category_id, product_name, product_url
                        cursor.execute('''
                            INSERT INTO product_urls 
                            (id, sub_category_id, product_name, product_url, created_at, updated_at)
                            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                        ''', (row[0], row[1], row[2], row[3]))
                
                print(f"   ✅ 恢复了 {len(existing_data)} 条产品数据")
            
            else:
                print("✅ product_urls表结构已正确")
            
            # 确保product_details表存在
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_details (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_url_id INTEGER NOT NULL UNIQUE,
                    product_sku TEXT,
                    product_image_url TEXT,
                    product_price TEXT,
                    short_description TEXT,
                    title TEXT,
                    nav_level1 TEXT,
                    nav_level2 TEXT,
                    nav_level3 TEXT,
                    nav_full TEXT,
                    category_name TEXT,
                    category_url TEXT,
                    regular_price DECIMAL(10,2),
                    sale_price DECIMAL(10,2),
                    currency TEXT DEFAULT 'USD',
                    price_data_raw TEXT,
                    crawl_status TEXT DEFAULT 'pending',
                    crawl_attempts INTEGER DEFAULT 0,
                    last_crawl_attempt TIMESTAMP,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_url_id) REFERENCES product_urls (id)
                )
            ''')
            
            # 确保tier_prices表存在且结构正确
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tier_prices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_detail_id INTEGER,
                    quantity_min INTEGER NOT NULL,
                    quantity_max INTEGER,
                    quantity_display TEXT,
                    unit_price DECIMAL(10, 2) NOT NULL,
                    currency TEXT DEFAULT 'USD',
                    discount_percent INTEGER,
                    raw_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_detail_id) REFERENCES product_details (id)
                )
            ''')
            
            conn.commit()
            
        print("✅ 数据库结构修复完成")
        
        # 验证修复结果
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(product_urls)")
            new_columns = [row[1] for row in cursor.fetchall()]
            print(f"修复后product_urls表列: {new_columns}")
            
            # 测试插入
            cursor.execute('''
                INSERT OR REPLACE INTO product_urls 
                (sub_category_id, product_name, product_url, updated_at)
                VALUES (1, 'Test Product', 'https://test.com/test', CURRENT_TIMESTAMP)
            ''')
            test_id = cursor.lastrowid
            
            cursor.execute('SELECT * FROM product_urls WHERE id = ?', (test_id,))
            test_row = cursor.fetchone()
            print(f"✅ 插入测试成功: {test_row}")
            
            # 清理测试数据
            cursor.execute('DELETE FROM product_urls WHERE id = ?', (test_id,))
            conn.commit()
            print("✅ 测试数据已清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 恢复备份
        if backup_path and os.path.exists(backup_path):
            print("🔄 正在恢复备份...")
            shutil.copy2(backup_path, db_path)
            print("✅ 已恢复到备份版本")
        
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Novoline爬虫 - 数据库结构快速修复工具")
    print("=" * 60)
    print("此工具将修复第二阶段采集时的字段错误问题")
    print()
    
    db_path = "novoline_spider_v2.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        print("请确保您在正确的目录中运行此脚本")
        return
    
    print(f"📁 找到数据库文件: {db_path}")
    
    # 确认修复
    confirm = input(f"\n确认修复数据库结构? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 修复已取消")
        return
    
    # 执行修复
    if fix_database_structure(db_path):
        print(f"\n🎉 数据库修复成功！")
        print("\n📋 修复总结:")
        print("   - product_urls表: 确保包含所有必要字段")
        print("   - product_details表: 为第三阶段做准备")
        print("   - tier_prices表: 更新为正确的结构")
        print("\n🎯 现在可以正常运行第二阶段采集了！")
        print("\n💡 使用方法:")
        print("   python gui.py  # 启动GUI界面")
        print("   然后点击'第二步：产品URL'开始采集")
    else:
        print(f"\n❌ 数据库修复失败")

if __name__ == "__main__":
    main()
