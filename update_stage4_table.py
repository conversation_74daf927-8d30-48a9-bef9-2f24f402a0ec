#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新步骤四数据库表结构 - 添加additional_information字段
"""

import sqlite3

def update_stage4_table():
    """更新步骤四的数据库表，添加additional_information字段"""
    print("🔧 更新步骤四数据库表结构...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute('''
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='product_images'
            ''')
            
            if not cursor.fetchone():
                print("❌ product_images表不存在，请先运行 create_stage4_table.py")
                return False
            
            # 检查additional_information字段是否已存在
            cursor.execute('PRAGMA table_info(product_images)')
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            if 'additional_information' in column_names:
                print("✅ additional_information字段已存在")
                return True
            
            # 添加additional_information字段
            print("📝 添加additional_information字段...")
            cursor.execute('''
                ALTER TABLE product_images 
                ADD COLUMN additional_information TEXT
            ''')
            
            conn.commit()
            print("✅ additional_information字段添加成功")
            
            # 验证字段添加
            cursor.execute('PRAGMA table_info(product_images)')
            columns = cursor.fetchall()
            print(f"📋 更新后的表结构包含 {len(columns)} 个字段:")
            
            for col in columns:
                if col[1] == 'additional_information':
                    print(f"   ✅ {col[1]} ({col[2]}) - 新添加")
                else:
                    print(f"   {col[1]} ({col[2]})")
            
            return True
            
    except Exception as e:
        print(f"❌ 更新表结构失败: {e}")
        return False

def check_existing_data():
    """检查现有数据"""
    print(f"\n📊 检查现有数据...")
    
    try:
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            # 检查product_images表中的数据
            cursor.execute('SELECT COUNT(*) FROM product_images')
            total_images = cursor.fetchone()[0]
            
            cursor.execute('''
                SELECT COUNT(*) FROM product_images 
                WHERE crawl_status = 'success'
            ''')
            success_images = cursor.fetchone()[0]
            
            cursor.execute('''
                SELECT COUNT(*) FROM product_images 
                WHERE additional_information IS NOT NULL AND additional_information != ''
            ''')
            with_additional_info = cursor.fetchone()[0]
            
            print(f"   总图片记录: {total_images}")
            print(f"   成功采集: {success_images}")
            print(f"   有Additional Information: {with_additional_info}")
            
            # 显示示例数据
            if total_images > 0:
                cursor.execute('''
                    SELECT product_sku, product_title, 
                           CASE 
                               WHEN additional_information IS NOT NULL AND additional_information != '' 
                               THEN 'YES' 
                               ELSE 'NO' 
                           END as has_additional_info
                    FROM product_images 
                    LIMIT 5
                ''')
                
                samples = cursor.fetchall()
                if samples:
                    print(f"\n📋 示例数据:")
                    for sku, title, has_info in samples:
                        print(f"   SKU: {sku}")
                        print(f"   标题: {title}")
                        print(f"   Additional Info: {has_info}")
                        print()
            
            return True
            
    except Exception as e:
        print(f"❌ 检查数据失败: {e}")
        return False

def test_additional_info_extraction():
    """测试Additional Information提取功能"""
    print(f"\n🧪 测试Additional Information提取...")
    
    try:
        from stage4_image_crawler import Stage4ImageCrawler
        import requests
        from bs4 import BeautifulSoup
        
        # 创建爬虫实例
        crawler = Stage4ImageCrawler("novoline_spider_v2.db")
        
        # 获取一个测试URL
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT pu.product_url, pd.title, pd.sku
                FROM product_urls pu
                JOIN product_details pd ON pu.id = pd.product_url_id
                WHERE pd.crawl_status = 'success' 
                  AND pd.sku IS NOT NULL
                LIMIT 1
            ''')
            
            result = cursor.fetchone()
            
            if not result:
                print("❌ 没有找到可测试的产品URL")
                return False
            
            test_url, title, sku = result
            print(f"🎯 测试产品:")
            print(f"   SKU: {sku}")
            print(f"   标题: {title}")
            print(f"   URL: {test_url}")
            
            # 获取页面内容
            print(f"\n📥 获取页面内容...")
            response = requests.get(test_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 测试Additional Information提取
            additional_info = crawler._extract_additional_information(soup)
            
            if additional_info:
                print(f"✅ 成功提取Additional Information")
                print(f"📝 内容长度: {len(additional_info)} 字符")
                print(f"📄 内容预览:")
                # 显示前200个字符
                preview = additional_info[:200].replace('\n', ' ').strip()
                print(f"   {preview}...")
            else:
                print(f"⚠️ 未找到Additional Information内容")
                
                # 检查页面中是否有相关标题
                h2_elements = soup.find_all('h2')
                print(f"📋 页面中的H2标题:")
                for h2 in h2_elements:
                    h2_text = h2.get_text(strip=True)
                    print(f"   - {h2_text}")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试Additional Information提取失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 步骤四数据库表结构更新")
    print("=" * 60)
    print("添加additional_information字段并测试功能")
    print()
    
    # 更新表结构
    if update_stage4_table():
        print("\n✅ 数据库表更新成功")
    else:
        print("\n❌ 数据库表更新失败")
        return
    
    # 检查现有数据
    if check_existing_data():
        print("\n✅ 数据检查完成")
    else:
        print("\n❌ 数据检查失败")
    
    # 测试Additional Information提取
    if test_additional_info_extraction():
        print("\n✅ Additional Information提取测试完成")
    else:
        print("\n❌ Additional Information提取测试失败")
    
    print("\n" + "=" * 60)
    print("🎯 更新完成")
    print("=" * 60)
    print("✅ additional_information字段已添加")
    print("✅ 图片本地路径将存储相对路径")
    print("✅ Additional Information提取功能已就绪")
    
    print(f"\n🚀 下一步:")
    print(f"   1. 重新启动GUI程序")
    print(f"   2. 使用步骤四采集产品图片")
    print(f"   3. 检查additional_information字段数据")

if __name__ == "__main__":
    main()
