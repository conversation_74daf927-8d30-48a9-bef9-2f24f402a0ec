#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本
用于更新现有数据库结构以支持第二阶段功能
"""

import os
import sqlite3
import shutil
from datetime import datetime

def backup_database(db_path):
    """备份数据库"""
    if not os.path.exists(db_path):
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_{timestamp}"
    shutil.copy2(db_path, backup_path)
    print(f"✅ 数据库已备份到: {backup_path}")
    return backup_path

def check_column_exists(cursor, table_name, column_name):
    """检查列是否存在"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [row[1] for row in cursor.fetchall()]
    return column_name in columns

def migrate_sub_categories_table(cursor):
    """迁移子目录表"""
    print("🔄 检查子目录表结构...")
    
    # 检查新列是否存在
    columns_to_add = [
        ('product_count', 'INTEGER DEFAULT 0'),
        ('page_count', 'INTEGER DEFAULT 0'),
        ('last_crawled_at', 'TIMESTAMP')
    ]
    
    for column_name, column_def in columns_to_add:
        if not check_column_exists(cursor, 'sub_categories', column_name):
            print(f"   添加列: {column_name}")
            cursor.execute(f"ALTER TABLE sub_categories ADD COLUMN {column_name} {column_def}")
        else:
            print(f"   列已存在: {column_name}")

def migrate_product_urls_table(cursor):
    """迁移产品URL表"""
    print("🔄 检查产品URL表结构...")
    
    # 检查新列是否存在
    columns_to_add = [
        ('product_sku', 'TEXT'),
        ('product_price', 'TEXT')
    ]
    
    for column_name, column_def in columns_to_add:
        if not check_column_exists(cursor, 'product_urls', column_name):
            print(f"   添加列: {column_name}")
            cursor.execute(f"ALTER TABLE product_urls ADD COLUMN {column_name} {column_def}")
        else:
            print(f"   列已存在: {column_name}")

def migrate_database(db_path):
    """执行数据库迁移"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    print(f"🔧 开始迁移数据库: {db_path}")
    
    # 备份数据库
    backup_path = backup_database(db_path)
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 迁移各个表
            migrate_sub_categories_table(cursor)
            migrate_product_urls_table(cursor)
            
            # 提交更改
            conn.commit()
            
        print("✅ 数据库迁移完成")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        
        # 恢复备份
        if backup_path and os.path.exists(backup_path):
            print("🔄 正在恢复备份...")
            shutil.copy2(backup_path, db_path)
            print("✅ 已恢复到备份版本")
        
        return False

def main():
    """主函数"""
    print("Novoline爬虫数据库迁移工具")
    print("=" * 50)
    
    # 查找数据库文件
    db_files = []
    for file in os.listdir('.'):
        if file.endswith('.db') and not file.startswith('test_'):
            db_files.append(file)
    
    if not db_files:
        print("❌ 当前目录下没有找到数据库文件")
        return
    
    print("📁 找到以下数据库文件:")
    for i, db_file in enumerate(db_files, 1):
        file_size = os.path.getsize(db_file) / 1024  # KB
        print(f"   {i}. {db_file} ({file_size:.1f} KB)")
    
    # 选择要迁移的数据库
    if len(db_files) == 1:
        selected_db = db_files[0]
        print(f"\n自动选择: {selected_db}")
    else:
        try:
            choice = input(f"\n请选择要迁移的数据库 (1-{len(db_files)}, 或 'all' 迁移所有): ").strip()
            
            if choice.lower() == 'all':
                # 迁移所有数据库
                success_count = 0
                for db_file in db_files:
                    print(f"\n迁移 {db_file}...")
                    if migrate_database(db_file):
                        success_count += 1
                
                print(f"\n🎉 迁移完成: {success_count}/{len(db_files)} 个数据库成功迁移")
                return
            else:
                index = int(choice) - 1
                if 0 <= index < len(db_files):
                    selected_db = db_files[index]
                else:
                    print("❌ 无效选择")
                    return
        except ValueError:
            print("❌ 请输入有效数字")
            return
    
    # 执行迁移
    if migrate_database(selected_db):
        print(f"\n🎉 {selected_db} 迁移成功！")
        print("现在可以运行第二阶段功能了。")
    else:
        print(f"\n❌ {selected_db} 迁移失败")

if __name__ == "__main__":
    main()
