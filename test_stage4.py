#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试步骤四图片采集功能
"""

import os
import sqlite3
from stage4_image_crawler import Stage4ImageCrawler

def test_stage4_basic():
    """测试步骤四基本功能"""
    print("=" * 60)
    print("🧪 测试步骤四图片采集功能")
    print("=" * 60)
    
    try:
        # 初始化爬虫
        crawler = Stage4ImageCrawler("novoline_spider_v2.db", max_workers=2)
        
        # 设置回调函数
        def progress_callback(message):
            print(f"📈 进度: {message}")
        
        def error_callback(message):
            print(f"❌ 错误: {message}")
        
        def completion_callback(message):
            print(f"✅ 完成: {message}")
        
        crawler.set_callbacks(
            progress_callback=progress_callback,
            error_callback=error_callback,
            completion_callback=completion_callback
        )
        
        # 获取产品列表
        products = crawler.get_products_for_image_crawl('pending')
        
        print(f"📊 数据统计:")
        print(f"   待采集图片的产品: {len(products)}")
        
        if products:
            print(f"\n📋 前5个产品示例:")
            for i, product in enumerate(products[:5], 1):
                print(f"   {i}. SKU: {product['sku']}")
                print(f"      标题: {product['title']}")
                print(f"      URL: {product['product_url']}")
                print()
        
        # 获取统计信息
        stats = crawler.get_stage4_stats()
        print(f"📈 步骤四统计:")
        print(f"   总产品数: {stats['total_products']:,}")
        print(f"   待处理: {stats['pending']:,}")
        print(f"   成功: {stats['success']:,}")
        print(f"   失败: {stats['failed']:,}")
        print(f"   总图片: {stats['total_images']:,}")
        
        return len(products) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_single_product():
    """测试单个产品的图片采集"""
    print(f"\n" + "=" * 60)
    print("🧪 测试单个产品图片采集")
    print("=" * 60)
    
    try:
        # 获取一个测试产品
        with sqlite3.connect('novoline_spider_v2.db') as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT pu.id, pu.product_url, pd.title, pd.sku
                FROM product_urls pu
                JOIN product_details pd ON pu.id = pd.product_url_id
                WHERE pd.crawl_status = 'success' 
                  AND pd.sku IS NOT NULL 
                  AND pd.title IS NOT NULL
                LIMIT 1
            ''')
            
            result = cursor.fetchone()
            
            if not result:
                print("❌ 没有找到可测试的产品")
                return False
            
            product_url_id, product_url, title, sku = result
            
            print(f"🎯 测试产品:")
            print(f"   ID: {product_url_id}")
            print(f"   SKU: {sku}")
            print(f"   标题: {title}")
            print(f"   URL: {product_url}")
            
            # 创建测试产品字典
            test_product = {
                'product_url_id': product_url_id,
                'product_url': product_url,
                'product_name': title,
                'title': title,
                'sku': sku,
                'crawl_status': 'pending',
                'crawl_attempts': 0
            }
            
            # 初始化爬虫
            crawler = Stage4ImageCrawler("novoline_spider_v2.db", max_workers=1)
            
            # 测试单个产品采集
            print(f"\n🚀 开始测试采集...")
            success = crawler._crawl_single_product_images(test_product)
            
            if success:
                print(f"✅ 单个产品采集成功")
                
                # 检查结果
                cursor.execute('''
                    SELECT crawl_status, images_downloaded, local_directory
                    FROM product_images
                    WHERE product_url_id = ?
                ''', (product_url_id,))
                
                result = cursor.fetchone()
                if result:
                    status, images_count, local_dir = result
                    print(f"📊 采集结果:")
                    print(f"   状态: {status}")
                    print(f"   图片数量: {images_count}")
                    print(f"   本地目录: {local_dir}")
                    
                    # 检查本地文件
                    if local_dir and os.path.exists(local_dir):
                        files = os.listdir(local_dir)
                        image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp'))]
                        print(f"   本地图片文件: {len(image_files)} 个")
                        for img_file in image_files:
                            print(f"      - {img_file}")
                
            else:
                print(f"❌ 单个产品采集失败")
            
            return success
            
    except Exception as e:
        print(f"❌ 测试单个产品失败: {e}")
        return False

def check_directory_structure():
    """检查目录结构创建"""
    print(f"\n" + "=" * 60)
    print("🧪 检查目录结构创建")
    print("=" * 60)
    
    try:
        images_root = "images"
        
        if os.path.exists(images_root):
            print(f"✅ 图片根目录存在: {images_root}")
            
            # 遍历目录结构
            for root, dirs, files in os.walk(images_root):
                level = root.replace(images_root, '').count(os.sep)
                indent = ' ' * 2 * level
                print(f"{indent}{os.path.basename(root)}/")
                
                # 只显示前几个文件
                subindent = ' ' * 2 * (level + 1)
                for file in files[:3]:
                    print(f"{subindent}{file}")
                
                if len(files) > 3:
                    print(f"{subindent}... 还有 {len(files) - 3} 个文件")
                
                # 限制显示深度
                if level > 3:
                    break
        else:
            print(f"⚠️ 图片根目录不存在: {images_root}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查目录结构失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 步骤四图片采集功能测试")
    print("测试图片采集、目录创建、数据库操作等功能")
    print()
    
    # 测试基本功能
    basic_ok = test_stage4_basic()
    
    # 测试单个产品采集
    if basic_ok:
        single_ok = test_single_product()
    else:
        single_ok = False
    
    # 检查目录结构
    dir_ok = check_directory_structure()
    
    print("\n" + "=" * 60)
    print("🎯 测试结果总结")
    print("=" * 60)
    
    print(f"基本功能测试: {'✅ 通过' if basic_ok else '❌ 失败'}")
    print(f"单个产品测试: {'✅ 通过' if single_ok else '❌ 失败'}")
    print(f"目录结构检查: {'✅ 通过' if dir_ok else '❌ 失败'}")
    
    if all([basic_ok, single_ok, dir_ok]):
        print(f"\n🎉 所有测试通过！步骤四功能正常")
        print(f"\n🚀 下一步:")
        print(f"   1. 启动GUI程序: python main.py")
        print(f"   2. 进入第四步：产品图片")
        print(f"   3. 选择采集范围和线程数")
        print(f"   4. 开始批量图片采集")
    else:
        print(f"\n⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
