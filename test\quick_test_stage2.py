#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二阶段快速测试脚本
测试单个分类的产品URL收集功能
"""

import os
import sys
import time
from spider_core import NovolineSpider
from database import DatabaseManager
from html_parser import NovolineParser

def test_single_category():
    """测试单个分类的产品收集"""
    print("=" * 60)
    print("第二阶段快速测试 - 单个分类产品收集")
    print("=" * 60)
    
    # 使用测试URL（Storage & Home Organization分类）
    test_url = "https://novolinepromo.com/home-auto/storage-home-organization/"
    
    print(f"测试URL: {test_url}")
    print("这个分类在网站上显示有84个产品")
    print()
    
    # 创建解析器
    parser = NovolineParser("https://novolinepromo.com")
    
    print("🔍 开始解析产品列表...")
    start_time = time.time()
    
    result = parser.parse_product_list(test_url)
    
    end_time = time.time()
    duration = end_time - start_time
    
    if result and result.get('products'):
        products = result['products']
        total_count = result.get('total_count', 0)
        page_count = result.get('page_count', 0)
        
        print(f"✅ 解析成功! 耗时: {duration:.1f} 秒")
        print()
        print(f"📊 统计结果:")
        print(f"   总产品数: {total_count}")
        print(f"   总页数: {page_count}")
        print(f"   平均每页: {total_count/page_count if page_count > 0 else 0:.1f} 个产品")
        print()
        
        # 显示前几个产品
        print(f"📦 前5个产品:")
        for i, product in enumerate(products[:5]):
            print(f"   {i+1}. {product['name']}")
            print(f"      SKU: {product.get('sku', 'N/A')}")
            print(f"      价格: {product.get('price', 'N/A')}")
            print()
        
        # 验证数量
        if total_count == 84:
            print("✅ 产品数量与网站显示一致!")
        else:
            print(f"⚠️  产品数量与网站显示不一致 (期望84个，实际{total_count}个)")
        
        return True
    else:
        print("❌ 解析失败")
        return False

def test_database_integration():
    """测试数据库集成"""
    print("\n" + "=" * 60)
    print("测试数据库集成功能")
    print("=" * 60)
    
    # 创建测试数据库
    test_db = "quick_test.db"
    if os.path.exists(test_db):
        os.remove(test_db)
    
    db = DatabaseManager(test_db)
    
    # 创建测试分类
    main_cat_id = db.insert_main_category("Home & Auto", "https://novolinepromo.com/home-auto/", "")
    sub_cat_id = db.insert_sub_category(main_cat_id, "Storage & Home Organization", 
                                       "https://novolinepromo.com/home-auto/storage-home-organization/")
    
    print("✅ 创建测试分类成功")
    
    # 解析产品
    parser = NovolineParser("https://novolinepromo.com")
    result = parser.parse_product_list("https://novolinepromo.com/home-auto/storage-home-organization/")
    
    if result and result.get('products'):
        products = result['products']
        page_count = result.get('page_count', 0)
        
        # 插入产品到数据库
        print(f"📥 正在插入 {len(products)} 个产品到数据库...")
        
        for product in products:
            db.insert_product_url(
                sub_cat_id,
                product['name'],
                product['url'],
                product.get('sku', ''),
                product.get('image_url', ''),
                product.get('price', ''),
                product.get('description', '')
            )
        
        # 更新统计信息
        db.update_sub_category_stats(sub_cat_id, len(products), page_count)
        
        print("✅ 产品插入完成")
        
        # 验证数据
        stats = db.get_category_stats()
        for stat in stats:
            if stat['sub_name'] == "Storage & Home Organization":
                print(f"📊 数据库统计:")
                print(f"   记录的产品数: {stat['recorded_product_count']}")
                print(f"   实际产品数: {stat['actual_product_count']}")
                print(f"   页面数: {stat['page_count']}")
                
                if stat['recorded_product_count'] == stat['actual_product_count']:
                    print("✅ 数据一致性验证通过")
                else:
                    print("❌ 数据一致性验证失败")
                break
        
        # 清理测试数据库
        try:
            db = None
            time.sleep(0.1)
            if os.path.exists(test_db):
                os.remove(test_db)
            print("✅ 测试数据库已清理")
        except:
            print(f"⚠️  测试数据库未能删除: {test_db}")
        
        return True
    else:
        print("❌ 产品解析失败")
        return False

def main():
    """主函数"""
    print("Novoline爬虫第二阶段快速测试")
    print("测试产品URL收集和数量统计功能")
    print()
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 单个分类产品收集
    if test_single_category():
        print("✓ 单个分类产品收集测试通过")
        success_count += 1
    else:
        print("✗ 单个分类产品收集测试失败")
    
    # 测试2: 数据库集成
    if test_database_integration():
        print("✓ 数据库集成测试通过")
        success_count += 1
    else:
        print("✗ 数据库集成测试失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！第二阶段功能正常")
        print("\n📋 功能特点:")
        print("   ✅ 支持多页面自动翻页")
        print("   ✅ 提取产品SKU、价格等详细信息")
        print("   ✅ 统计每个子目录的产品数量和页面数")
        print("   ✅ 数据库结构支持产品数量核对")
        print("\n🚀 可以开始使用第二阶段功能了!")
        return True
    else:
        print("❌ 部分测试失败，请检查代码")
        return False

if __name__ == "__main__":
    main()
