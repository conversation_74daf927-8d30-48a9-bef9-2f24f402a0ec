#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库结构迁移脚本 - 修正第二阶段表结构
将product_urls表调整为只包含第二阶段需要的字段
添加product_details表用于第三阶段
"""

import os
import sqlite3
import shutil
from datetime import datetime

def backup_database(db_path):
    """备份数据库"""
    if not os.path.exists(db_path):
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"backup/{os.path.basename(db_path)}.backup_{timestamp}"
    
    # 确保backup目录存在
    os.makedirs("backup", exist_ok=True)
    
    shutil.copy2(db_path, backup_path)
    print(f"✅ 数据库已备份到: {backup_path}")
    return backup_path

def check_table_exists(cursor, table_name):
    """检查表是否存在"""
    cursor.execute('''
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
    ''', (table_name,))
    return cursor.fetchone() is not None

def get_table_columns(cursor, table_name):
    """获取表的列信息"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    return [row[1] for row in cursor.fetchall()]

def migrate_product_urls_table(cursor):
    """迁移product_urls表结构"""
    print("🔄 迁移product_urls表结构...")
    
    if not check_table_exists(cursor, 'product_urls'):
        print("   表不存在，跳过迁移")
        return
    
    # 获取当前表结构
    columns = get_table_columns(cursor, 'product_urls')
    print(f"   当前列: {columns}")
    
    # 检查是否需要迁移
    extra_columns = ['product_sku', 'product_image_url', 'product_price', 'short_description']
    has_extra_columns = any(col in columns for col in extra_columns)
    
    if has_extra_columns:
        print("   检测到第三阶段字段，需要迁移...")
        
        # 创建新的product_urls表（只包含第二阶段字段）
        cursor.execute('''
            CREATE TABLE product_urls_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sub_category_id INTEGER,
                product_name TEXT NOT NULL,
                product_url TEXT NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sub_category_id) REFERENCES sub_categories (id)
            )
        ''')
        
        # 复制数据（只复制第二阶段需要的字段）
        cursor.execute('''
            INSERT INTO product_urls_new (id, sub_category_id, product_name, product_url, created_at, updated_at)
            SELECT id, sub_category_id, product_name, product_url, created_at, updated_at
            FROM product_urls
        ''')
        
        # 删除旧表，重命名新表
        cursor.execute('DROP TABLE product_urls')
        cursor.execute('ALTER TABLE product_urls_new RENAME TO product_urls')
        
        print("   ✅ product_urls表迁移完成")
    else:
        print("   ✅ product_urls表结构已正确")

def create_product_details_table(cursor):
    """创建product_details表"""
    print("🔄 创建product_details表...")
    
    if check_table_exists(cursor, 'product_details'):
        print("   表已存在，跳过创建")
        return
    
    cursor.execute('''
        CREATE TABLE product_details (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_url_id INTEGER NOT NULL UNIQUE,
            product_sku TEXT,
            product_image_url TEXT,
            product_price TEXT,
            short_description TEXT,
            title TEXT,
            nav_level1 TEXT,
            nav_level2 TEXT,
            nav_level3 TEXT,
            nav_full TEXT,
            category_name TEXT,
            category_url TEXT,
            regular_price DECIMAL(10,2),
            sale_price DECIMAL(10,2),
            currency TEXT DEFAULT 'USD',
            price_data_raw TEXT,
            crawl_status TEXT DEFAULT 'pending',
            crawl_attempts INTEGER DEFAULT 0,
            last_crawl_attempt TIMESTAMP,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_url_id) REFERENCES product_urls (id)
        )
    ''')
    
    print("   ✅ product_details表创建完成")

def update_tier_prices_table(cursor):
    """更新tier_prices表结构"""
    print("🔄 更新tier_prices表结构...")
    
    if not check_table_exists(cursor, 'tier_prices'):
        print("   表不存在，跳过更新")
        return
    
    columns = get_table_columns(cursor, 'tier_prices')
    
    # 检查是否需要更新外键引用
    if 'product_id' in columns:
        print("   需要更新外键引用...")
        
        # 创建新的tier_prices表
        cursor.execute('''
            CREATE TABLE tier_prices_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_detail_id INTEGER,
                quantity_min INTEGER NOT NULL,
                quantity_max INTEGER,
                quantity_display TEXT,
                unit_price DECIMAL(10, 2) NOT NULL,
                currency TEXT DEFAULT 'USD',
                discount_percent INTEGER,
                raw_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_detail_id) REFERENCES product_details (id)
            )
        ''')
        
        # 注意：旧数据不会自动迁移，因为外键关系已改变
        # 第三阶段运行时会重新填充这个表
        
        # 删除旧表，重命名新表
        cursor.execute('DROP TABLE tier_prices')
        cursor.execute('ALTER TABLE tier_prices_new RENAME TO tier_prices')
        
        print("   ✅ tier_prices表更新完成")
    else:
        print("   ✅ tier_prices表结构已正确")

def migrate_database(db_path):
    """执行数据库迁移"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    print(f"🔧 开始迁移数据库: {db_path}")
    
    # 备份数据库
    backup_path = backup_database(db_path)
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 执行迁移步骤
            migrate_product_urls_table(cursor)
            create_product_details_table(cursor)
            update_tier_prices_table(cursor)
            
            # 提交更改
            conn.commit()
            
        print("✅ 数据库迁移完成")
        print("\n📋 迁移总结:")
        print("   - product_urls表: 只包含第二阶段字段（产品名称和URL）")
        print("   - product_details表: 新增，用于存储第三阶段详细信息")
        print("   - tier_prices表: 更新外键引用到product_details")
        print("\n🎯 现在可以正常运行第二阶段了！")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 恢复备份
        if backup_path and os.path.exists(backup_path):
            print("🔄 正在恢复备份...")
            shutil.copy2(backup_path, db_path)
            print("✅ 已恢复到备份版本")
        
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Novoline爬虫 - 第二阶段表结构迁移工具")
    print("=" * 60)
    print("此工具将:")
    print("1. 修正product_urls表结构（移除第三阶段字段）")
    print("2. 创建product_details表（用于第三阶段）")
    print("3. 更新tier_prices表结构")
    print()
    
    # 查找数据库文件
    db_files = []
    for file in os.listdir('.'):
        if file.endswith('.db') and not file.startswith('test_'):
            db_files.append(file)
    
    if not db_files:
        print("❌ 当前目录下没有找到数据库文件")
        return
    
    print("📁 找到以下数据库文件:")
    for i, db_file in enumerate(db_files, 1):
        file_size = os.path.getsize(db_file) / 1024  # KB
        print(f"   {i}. {db_file} ({file_size:.1f} KB)")
    
    # 选择要迁移的数据库
    if len(db_files) == 1:
        selected_db = db_files[0]
        print(f"\n自动选择: {selected_db}")
    else:
        try:
            choice = input(f"\n请选择要迁移的数据库 (1-{len(db_files)}): ").strip()
            index = int(choice) - 1
            if 0 <= index < len(db_files):
                selected_db = db_files[index]
            else:
                print("❌ 无效选择")
                return
        except ValueError:
            print("❌ 请输入有效数字")
            return
    
    # 确认迁移
    confirm = input(f"\n确认迁移 {selected_db}? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 迁移已取消")
        return
    
    # 执行迁移
    if migrate_database(selected_db):
        print(f"\n🎉 {selected_db} 迁移成功！")
    else:
        print(f"\n❌ {selected_db} 迁移失败")

if __name__ == "__main__":
    main()
